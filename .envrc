# Local Development Environment
export PROJECT_ENV="Local"
export NEXT_PUBLIC_SUPABASE_URL="http://127.0.0.1:54321"
export NEXT_PUBLIC_SUPABASE_ANON_KEY="sb_local_anon_key"
export SUPABASE_SERVICE_ROLE_KEY="sb_local_service_role_key"
export DATABASE_URI="postgresql://postgres:postgres@127.0.0.1:54322/postgres"
export PAYLOAD_SECRET="f25b45d0a313d16360ab4a95"
export OPENAI_API_KEY="********************************************************************************************************************************************************************"
export FIRECRAWL_API_KEY="fc-c1d19d22a2f74de8a1a114bb568a3813"
export NEXT_PUBLIC_SERVER_URL="http://localhost:3000"
export PAYLOAD_PUBLIC_DRAFT_SECRET="65172933035dd26d4186a8c48a40dc8784ef9c42e6c179fbe9c0c11dcfbf9080"
export RESEND_API_KEY="re_dwvmD9Dr_PAwNswau8MEYA1WCzv9hZRyc"
export RESEND_DEFAULT_FROM_ADDRESS="<EMAIL>"
export RESEND_DEFAULT_FROM_NAME="Local System Alerts - Börsen Blick"
export PIPELINE_NOTIFICATION_EMAIL="<EMAIL>"
export BLOB_READ_WRITE_TOKEN="vercel_blob_rw_WeBTj8EcH8xcY4bf_2goyS5grRjumY7l6eEl7cpwYpUtquz"
