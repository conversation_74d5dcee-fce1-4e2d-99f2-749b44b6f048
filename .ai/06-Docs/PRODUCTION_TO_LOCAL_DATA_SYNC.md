# Production to Local Data Sync Guide

Complete step-by-step guide for safely pulling production data into your local Supabase development environment.

## Overview

This guide walks you through the recommended approach for syncing your production database to your local development environment using the Supabase CLI and your existing backup infrastructure.

## Prerequisites

- Supabase CLI installed and configured
- Local Supabase development environment running
- Access to your production Supabase project
- Existing backup scripts (which you already have)

## Before You Start

### ⚠️ Important Safety Notes

1. **Always backup your local database first** - you can restore it if something goes wrong
2. **This will completely replace your local data** with production data
3. **Test the process in a safe environment first** if you're unsure
4. **Follow the PayloadCMS safety rules** - never use PayloadCMS migrations for schema changes

## Step-by-Step Process

### Step 1: Verify Your Project Setup

Check which Supabase project you're currently linked to:

```bash
supabase projects list
```

You should see something like:

```
  LINKED | ORG ID               | REFERENCE ID         | NAME
      ●  | hpockzgwmruopcegtirx | vucefescwpcdbpjlccea | BörsenBlick Production
```

The `●` indicates which project is currently linked.

### Step 2: Safety First - Backup Your Local Database

Before making any changes, create a safety backup of your current local database:

```bash
pnpm backup-local-db
```

This is script we created ourselves. This creates a complete backup with separate files for roles, schema, and data. You'll see output like:

```
✅ Database backed up successfully!
📁 Files created:
   📋 Roles: supabase/backups/local_db_development_YYYYMMDD_HHMMSS/...
   🏗️ Schema: supabase/backups/local_db_development_YYYYMMDD_HHMMSS/...
   📊 Data: supabase/backups/local_db_development_YYYYMMDD_HHMMSS/...
```

**Keep note of the backup folder name** - you'll need it if you want to restore later.

### Step 3: Create Production Database Backup

Create a backup of your production database:

```bash
pnpm backup:remote
```

This is script we created ourselves. This uses your existing script to create a production backup with proper naming and organisation. Output:

```
✅ REMOTE database (BörsenBlick Production) backed up successfully!
📁 Files created:
   📋 Roles: supabase/backups/remote_borsenblick_production_development_YYYYMMDD_HHMMSS/...
   🏗️ Schema: supabase/backups/remote_borsenblick_production_development_YYYYMMDD_HHMMSS/...
   📊 Data: supabase/backups/remote_borsenblick_production_development_YYYYMMDD_HHMMSS/...
```

**Note the backup folder name** - you'll need it for the restore steps.

### Step 4: Prepare Local Environment

Stop and restart your local Supabase to prepare for the restore:

```bash
# Stop local Supabase
supabase stop

# Start with fresh instance
supabase start
```

### Step 5: Handle Migrations (If Needed)

If you have pending migrations that might conflict with the restore, temporarily move them:

```bash
# Check if you have migrations that might conflict
ls supabase/migrations/

# If needed, temporarily move problematic migrations
mv supabase/migrations/PROBLEMATIC_MIGRATION.sql supabase/migrations/PROBLEMATIC_MIGRATION.sql.temp
```

Reset the local database to a clean state:

```bash
supabase db reset --no-seed
```

This will totally wipe your local database. If you get errors about missing tables, the migration temporary move was correct.

### Step 6: Restore Production Schema

Restore the production database schema to your local environment:

```bash
psql "postgresql://postgres:postgres@127.0.0.1:54322/postgres" \
  -f "supabase/backups/remote_borsenblick_production_development_YYYYMMDD_HHMMSS/remote_borsenblick_production_development_YYYYMMDD_HHMMSS_schema.sql" \
  --single-transaction \
  --variable ON_ERROR_STOP=1
```

**Replace `YYYYMMDD_HHMMSS` with your actual backup timestamp.**

You should see output showing table creation, indexes, and permissions being set up.

### Step 7: Restore Production Data

Restore all the production data:

```bash
psql "postgresql://postgres:postgres@127.0.0.1:54322/postgres" \
  -f "supabase/backups/remote_borsenblick_production_development_YYYYMMDD_HHMMSS/remote_borsenblick_production_development_YYYYMMDD_HHMMSS_data.sql" \
  --single-transaction \
  --variable ON_ERROR_STOP=1 \
  --set session_replication_role=replica
```

**Replace `YYYYMMDD_HHMMSS` with your actual backup timestamp.**

The `session_replication_role=replica` setting helps handle foreign key constraints during the restore.

You should see output showing data being copied into tables:

```
COPY 336  # Articles
COPY 4    # Categories
COPY 10   # Media files
...
```

### Step 8: Restore and Apply Migrations

If you moved any migrations in Step 5, restore them:

```bash
# Restore the migration file
mv supabase/migrations/PROBLEMATIC_MIGRATION.sql.temp supabase/migrations/PROBLEMATIC_MIGRATION.sql

# Apply the migration directly to the local database
psql "postgresql://postgres:postgres@127.0.0.1:54322/postgres" \
  -f "supabase/migrations/PROBLEMATIC_MIGRATION.sql"
```

The migration should apply cleanly (possibly with notices that columns already exist, which is fine).

### Step 9: Verify the Restore

Check that the data was restored correctly:

```bash
# Check record counts
psql "postgresql://postgres:postgres@127.0.0.1:54322/postgres" \
  -c "SELECT COUNT(*) as article_count FROM articles;" \
  -c "SELECT COUNT(*) as category_count FROM categories;" \
  -c "SELECT COUNT(*) as media_count FROM media;"

# Check sample data
psql "postgresql://postgres:postgres@127.0.0.1:54322/postgres" \
  -c "SELECT title FROM articles LIMIT 3;" \
  -c "SELECT title, english FROM categories;"
```

You should see realistic production data in the output.

### Step 10: Test Your Application

Start your development server to ensure everything works:

```bash
pnpm dev
```

Visit `http://localhost:3000/admin` to verify the admin interface loads with your production data.

## What You've Accomplished

After completing this process, your local development environment will have:

✅ **Complete production schema** - all tables, indexes, and relationships  
✅ **All production data** - articles, categories, media files, etc.  
✅ **Proper sequence values** - auto-incrementing IDs will continue correctly  
✅ **All admin configurations** - settings and preferences from production  
✅ **Working application** - ready for development and testing

## Recovery Options

### Restore Your Original Local Database

If you need to go back to your original local database:

```bash
# Use the auto-generated restore script
./supabase/backups/local_db_development_YYYYMMDD_HHMMSS/local_db_development_YYYYMMDD_HHMMSS_restore.sh

# Or use the package script
pnpm restore-local-db development_YYYYMMDD_HHMMSS
```

### Create a Fresh Local Database

If you want to start completely fresh:

```bash
supabase stop
supabase start
supabase db reset
```

## Troubleshooting

### Migration Conflicts

**Problem**: Migrations fail during reset because they expect tables that don't exist yet.

**Solution**: Temporarily move the migrations, restore the production schema (which includes the changes), then restore the migrations.

### Foreign Key Constraint Errors

**Problem**: Data restore fails due to foreign key constraints.

**Solution**: Use the `--set session_replication_role=replica` flag with psql to temporarily disable constraint checking during the restore.

### Large Database Timeouts

**Problem**: Restore takes too long or times out.

**Solution**: The process uses `--single-transaction` for safety. For very large databases, you might need to restore in smaller chunks or adjust timeout settings.

## Best Practices

1. **Regular Sync Schedule**: Consider doing this weekly or before major development work
2. **Team Coordination**: Let team members know when you're syncing production data
3. **Selective Sync**: For very large databases, consider syncing only specific tables you need
4. **Data Privacy**: Ensure you have permission to work with production data locally
5. **Backup Everything**: Always backup before making changes

## File Locations

Your backup files are organised in:

```
supabase/backups/
├── local_db_development_YYYYMMDD_HHMMSS/     # Local backups
└── remote_borsenblick_production_development_YYYYMMDD_HHMMSS/  # Production backups
```

Each backup folder contains:

- `*_roles.sql` - Database roles and permissions
- `*_schema.sql` - Complete database schema
- `*_data.sql` - All data with COPY statements
- `*_restore.sh` - Auto-generated restore script
- `*.info` - Backup metadata and instructions

## Security Considerations

- Production backups contain real user data - treat them securely
- Don't commit backup files to version control (they're already gitignored)
- Consider data protection regulations when working with production data locally
- Regularly clean up old backup files to save disk space

---

This process gives you a complete, safe, and reliable way to sync your production data to your local development environment. The approach follows Supabase best practices and respects your existing backup infrastructure.
