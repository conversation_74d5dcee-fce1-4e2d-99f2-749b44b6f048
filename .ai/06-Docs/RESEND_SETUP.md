# Resend Email Adapter Setup Guide

## Overview

This guide explains how to set up the Resend email adapter for PayloadCMS in the Börsen Blick application.

## Installation Status

✅ **Completed**: Package installation and PayloadCMS configuration

## Required Environment Variables

Add the following variables to your `.env` file:

```bash
# Resend Email Configuration
RESEND_API_KEY=re_xxxxxxxxxxxxxxxxxxxxxxxxxx
RESEND_DEFAULT_FROM_ADDRESS=<EMAIL>
RESEND_DEFAULT_FROM_NAME=Börsen Blick

# Pipeline Notifications (optional)
# Single email:
PIPELINE_NOTIFICATION_EMAIL=<EMAIL>
# Multiple emails (comma-separated):
# PIPELINE_NOTIFICATION_EMAIL=<EMAIL>,<EMAIL>,<EMAIL>
```

### Variable Details

- **RESEND_API_KEY**: Your Resend API key (required)
  - Get this from your Resend dashboard at resend.com
  - Format: `re_` followed by a long string of characters

- **RESEND_DEFAULT_FROM_ADDRESS**: Email address for outgoing emails (optional)
  - Default: `<EMAIL>`
  - Must be a verified domain in your Resend account

- **RESEND_DEFAULT_FROM_NAME**: Display name for outgoing emails (optional)
  - Default: `Börsen Blick`
  - This appears as the sender name in email clients

- **PIPELINE_NOTIFICATION_EMAIL**: Email address(es) for pipeline reports (optional)
  - Default: Uses `RESEND_DEFAULT_FROM_ADDRESS`
  - Where to send content pipeline completion reports and error notifications
  - **Multiple emails**: Separate with commas: `<EMAIL>,<EMAIL>,<EMAIL>`

## Next Steps Required

### 1. Create Resend Account

1. Go to [resend.com](https://resend.com)
2. Click "Get Started" to create an account
3. Complete email verification process

### 2. Generate API Key

1. Log in to your Resend dashboard
2. Navigate to API Keys section
3. Click "Create API Key"
4. Copy the generated key and add it to your `.env` file as `RESEND_API_KEY`

### 3. Domain Verification (Critical for Email Delivery)

1. In Resend dashboard, go to "Domains"
2. Add your domain: `borsenblick.de`
3. Configure the following DNS records at your DNS provider:

#### Required DNS Records

```
Type: CNAME
Name: resend._domainkey
Value: (provided by Resend)

Type: TXT
Name: @
Value: v=spf1 include:spf.resend.com ~all

Type: TXT
Name: _dmarc
Value: v=DMARC1; p=none; rua=mailto:<EMAIL>
```

4. Return to Resend and click "Verify DNS Records"
5. Wait for verification (can take up to 72 hours for DNS propagation)

### 4. Testing

Once configured, test the email functionality:

1. Restart your development server
2. Go to PayloadCMS admin login page
3. Click "Forgot Password?"
4. Enter an existing user's email address
5. Check that the password reset email is delivered

## Configuration Details

The email adapter is configured in `src/payload.config.ts`:

```typescript
import { resendAdapter } from '@payloadcms/email-resend';

export default buildConfig({
  // ... other config
  email: resendAdapter({
    defaultFromAddress:
      process.env.RESEND_DEFAULT_FROM_ADDRESS || '<EMAIL>',
    defaultFromName: process.env.RESEND_DEFAULT_FROM_NAME || 'Börsen Blick',
    apiKey: process.env.RESEND_API_KEY || '',
  }),
  // ... rest of config
});
```

## Usage in Application

Once configured, you can send emails programmatically:

```typescript
// In PayloadCMS hooks or API routes
await payload.sendEmail({
  to: '<EMAIL>',
  subject: 'Your Subject',
  text: 'Plain text content',
  html: '<p>HTML content</p>',
});
```

### Automated Pipeline Reports

The system now automatically sends email reports when content pipelines complete:

- **Test Content Pipeline**: Comprehensive reports with processing statistics
- **Article Review Queue**: Direct links to articles waiting for review
- **Error Notifications**: Automatic alerts when pipelines fail
- **Processing Metrics**: Performance and success rates

Reports include:

- ✅ Processing statistics (accepted/rejected/errors)
- 📧 Direct links to review articles in the admin panel
- ⏱️ Processing time and performance metrics
- 🔗 Quick access links to different article collections

## Troubleshooting

### Common Issues

1. **API Key Invalid**
   - Verify the API key is correctly copied
   - Ensure no extra spaces or characters
   - Check that the key starts with `re_`

2. **Domain Not Verified**
   - Emails will fail if domain is not verified
   - Check DNS records are correctly configured
   - Allow time for DNS propagation

3. **Environment Variables Not Loaded**
   - Restart your development server
   - Check `.env` file is in the project root
   - Verify variable names match exactly

### Development vs Production

- **Development**: You can use any verified domain
- **Production**: Must use the actual production domain (`borsenblick.de`)

## Benefits of Resend

- ✅ Modern REST API (lightweight)
- ✅ Excellent deliverability rates
- ✅ Built-in analytics and tracking
- ✅ Perfect for serverless deployments
- ✅ Better performance than traditional SMTP

## Status Checklist

- [x] Package installed (`@payloadcms/email-resend`)
- [x] PayloadCMS configuration updated
- [x] Environment variables documented
- [ ] Resend account created
- [ ] API key generated and configured
- [ ] Domain verification completed
- [ ] DNS records configured
- [ ] Email functionality tested
- [ ] Documentation updated

## Support

- [Resend Documentation](https://resend.com/docs)
- [PayloadCMS Email Documentation](https://payloadcms.com/docs/email/overview)
- [Resend Dashboard](https://resend.com/dashboard)
