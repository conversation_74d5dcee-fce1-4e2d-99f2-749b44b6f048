# Local to Production Data Sync Guide

## Prerequisites

- ✅ Production database backed up (`pnpm backup:remote`)
- ✅ Seed file ready (`supabase/seed.sql`)
- ✅ Supabase CLI installed and configured
- ✅ Project linked to production (`supabase link`)

## Overview

This guide walks through the complete process of pushing local development data to production for the first time. This is typically done during initial deployment when moving from development to production.

## Step-by-Step Process

### 1. Clear Production Database

```bash
supabase db reset --linked
# Confirm with 'y' when prompted
```

**What this does:**

- Completely wipes production database clean
- Resets to empty state ready for fresh deployment
- Removes all existing data and schema

### 2. Fix Migration Issues (if needed)

**Common Problem:** SQL syntax errors in migration files

**Example issue encountered:**

- **Problem:** Malformed `FOREIGN KEY` constraint
- **Error:** `syntax error at or near "using"`
- **Location:** Migration file line with incomplete constraint syntax

**Fix applied:**

```sql
# Changed from broken "using index" syntax:
FOREIGN KEY using index "constraint_name"

# To proper FOREIGN KEY reference:
<PERSON>OREIG<PERSON> KEY (_parent_id) REFERENCES _articles_v(id) ON DELETE CASCADE
```

### 3. Push Schema Migration

```bash
supabase db push
# Confirm with 'y' when prompted
```

**What this does:**

- Applies all pending migrations to production
- Creates complete database schema structure
- Establishes tables, indexes, and constraints

### 4. Push Seed Data

```bash
supabase db push --include-seed
# Confirm with 'y' when prompted
```

**What this does:**

- Pushes all data from `supabase/seed.sql`
- Populates production with your local development data
- Includes all articles, categories, media, users, etc.

## Common Issues & Solutions

### Character Encoding Errors

**Problem:** German characters and JSON content causing SQL errors

```
ERROR: syntax error at or near "Diese"
```

**Solution:** Use `INSERT` statements instead of `COPY` statements

```bash
# Generate seed file with INSERT statements
supabase db dump --local --data-only -f supabase/seed.sql
```

### Schema Reference Errors

**Problem:** References to `supabase_functions` schema that doesn't exist in production

```
ERROR: schema "supabase_functions" does not exist
```

**Solution:** Remove problematic schema references

```bash
# Remove supabase_functions references
sed -i '' '/supabase_functions/d' supabase/seed.sql
```

### Duplicate Data Errors

**Problem:** Trying to insert data that already exists

```
ERROR: duplicate key value violates unique constraint
```

**Solution:** Either clear production first (as shown above) or use selective sync

## Key Commands Summary

```bash
# Complete workflow for first-time production deployment:

# 1. Reset production (clears everything)
supabase db reset --linked

# 2. Push schema (creates structure)
supabase db push

# 3. Push data (populates with content)
supabase db push --include-seed
```

## Safety Notes

⚠️ **CRITICAL WARNINGS:**

- Always backup production before starting: `pnpm backup:remote`
- This process **completely replaces** all production data
- Only use for initial deployment or when you intentionally want to replace everything
- Test the process on staging first if available

✅ **Post-Deployment Checklist:**

- [ ] Verify application loads correctly
- [ ] Test key functionality with production data
- [ ] Check that all media files are accessible
- [ ] Confirm user authentication works
- [ ] Validate that all articles display properly

## Troubleshooting

### Migration Fails

1. Check for SQL syntax errors in migration files
2. Look for incomplete constraint definitions
3. Verify all referenced tables exist
4. Fix issues and re-run `supabase db push`

### Seed Data Fails

1. Regenerate seed file: `supabase db dump --local --data-only -f supabase/seed.sql`
2. Remove problematic schema references
3. Check for character encoding issues
4. Re-run `supabase db push --include-seed`

### Production App Not Working

1. Check database connection strings
2. Verify environment variables are set correctly
3. Confirm all migrations applied successfully
4. Test database connectivity

## Alternative Approaches

### For Ongoing Updates (Not First Deployment)

Instead of full replacement, consider:

- Selective data sync for specific tables
- Using Supabase's built-in backup/restore features
- Manual SQL scripts for specific changes

### Using GitHub Integration

If you have Supabase GitHub integration enabled:

1. Commit seed file to repository
2. Push to main branch
3. GitHub integration will automatically apply changes

---

**Last Updated:** January 2025  
**Process Verified:** ✅ Successfully deployed local data to production
