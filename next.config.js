/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  poweredByHeader: false, // Remove X-Powered-By header for security
  compress: true,
  
  // Image optimization configuration
  images: {
    formats: ['image/avif', 'image/webp'], // Enable AVIF and WebP conversion
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**', // Allow all hostnames for Vercel Blob Storage
      },
    ],
  },
  
  async headers() {
    return [
      {
        // Apply security headers to all routes
        source: '/(.*)',
        headers: [
          // Strict Transport Security (HSTS)
          {
            key: 'Strict-Transport-Security',
            value: 'max-age=31536000; includeSubDomains; preload'
          },
          // Prevent clickjacking
          {
            key: 'X-Frame-Options',
            value: 'DENY'
          },
          // Prevent MIME sniffing
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff'
          },
          // XSS Protection
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block'
          },
          // Referrer Policy
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin'
          },
          // Permissions Policy (restrict dangerous features)
          {
            key: 'Permissions-Policy',
            value: 'camera=(), microphone=(), geolocation=(), payment=()'
          },
          // Content Security Policy (will be enhanced in middleware for nonce support)
          {
            key: 'Content-Security-Policy',
            value: "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://unpkg.com https://s3.tradingview.com https://www.tradingview-widget.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.openai.com https://api.firecrawl.dev https://symbol-search.tradingview.com https://scanner.tradingview.com https://www.tradingview.com; frame-src 'self' https://www.tradingview-widget.com https://s.tradingview.com; media-src 'self'; object-src 'none'; base-uri 'self'; form-action 'self'; frame-ancestors 'none'; upgrade-insecure-requests;"
          }
        ]
      },
      {
        // Special headers for admin routes
        source: '/admin/:path*',
        headers: [
          {
            key: 'X-Robots-Tag',
            value: 'noindex, nofollow, noarchive, nosnippet'
          },
          {
            key: 'Cache-Control',
            value: 'no-cache, no-store, must-revalidate, max-age=0'
          }
        ]
      },
      {
        // API route specific headers
        source: '/api/:path*',
        headers: [
          {
            key: 'X-Robots-Tag',
            value: 'noindex'
          },
          {
            key: 'Cache-Control',
            value: 'no-cache, no-store, must-revalidate'
          }
        ]
      }
    ]
  },

  // Security-related webpack configuration
  webpack: (config, { dev, isServer }) => {
    // Only in production, add additional security measures
    if (!dev && !isServer) {
      // Remove source maps in production for security
      config.devtool = false;
    }
    
    return config;
  },

  // Environment variables that should be exposed to the client (be very careful here)
  // Note: NODE_ENV is automatically managed by Next.js
  env: {
    // Only expose non-sensitive environment variables here if needed
    // NODE_ENV is automatically managed by Next.js - do not add it manually
  },

  // Security-related experimental features
  experimental: {
    // Enable strict mode for better security
    strictNextHead: true,
  }
};

export default nextConfig; 
