{"timestamp":"2025-07-28T09:52:06.466Z","level":"INFO","component":"single-pipeline","message":"🎯 Single article pipeline started","context":{"startTime":"2025-07-28T09:52:06.466Z","pipeline":"single-pipeline"}}
{"timestamp":"2025-07-28T09:52:06.466Z","level":"INFO","component":"single-pipeline","message":"🔍 Validating database state"}
{"timestamp":"2025-07-28T09:52:06.484Z","level":"INFO","component":"single-pipeline","message":"📡 Configuring feeds for single article processing"}
{"timestamp":"2025-07-28T09:52:06.484Z","level":"INFO","component":"single-pipeline","message":"🔧 Feed configurations applied","context":{"pipeline":"single-pipeline","totalFeeds":8,"maxArticlesPerFeed":1,"maxFirecrawlScrapePerFeed":15,"originalConfigs":[{"id":10,"originalMaxArticlesPerRun":null},{"id":9,"originalMaxArticlesPerRun":null},{"id":8,"originalMaxArticlesPerRun":null},{"id":7,"originalMaxArticlesPerRun":null},{"id":6,"originalMaxArticlesPerRun":null},{"id":5,"originalMaxArticlesPerRun":null},{"id":2,"originalMaxArticlesPerRun":null},{"id":1,"originalMaxArticlesPerRun":null}]}}
{"timestamp":"2025-07-28T09:52:06.484Z","level":"INFO","component":"single-pipeline","message":"📡 Starting RSS processing pipeline","context":{"pipeline":"single-pipeline","processingMode":"parallel-with-smart-filtering","feedCount":8}}
{"timestamp":"2025-07-28T09:52:06.484Z","level":"INFO","component":"single-pipeline","message":"✅ Database validation passed","context":{"pipeline":"single-pipeline","activeFeeds":8,"activeKeywords":20,"totalCategories":4,"feeds":[{"id":10,"name":"DW","language":"de","priority":"medium"},{"id":9,"name":"Stern DE","language":"de","priority":"medium"},{"id":8,"name":"Finanz Nachrichten","language":"en","priority":"medium"},{"id":7,"name":"Onvista","language":"de","priority":"medium"},{"id":6,"name":"Der Acktionaer","language":"de","priority":"medium"},{"id":5,"name":"Finanzen","language":"de","priority":"high"},{"id":2,"name":"Wallstreet Online","language":"de","priority":"medium"},{"id":1,"name":"Handelsblatt","language":"de","priority":"high"}]}}
{"timestamp":"2025-07-28T10:06:47.540Z","level":"INFO","component":"single-pipeline","message":"📊 📊 RSS processing completed","context":{"metrics":{"rssProcessingDurationMs":881056,"processed":309,"accepted":6,"rejected":301,"errorCount":0},"pipeline":"single-pipeline"}}
{"timestamp":"2025-07-28T10:06:47.779Z","level":"INFO","component":"single-pipeline","message":"✅ Single article pipeline completed successfully","context":{"pipeline":"single-pipeline","timing":{"processingTimeMs":881074,"processingTimeSeconds":881,"rssProcessingDurationMs":881056},"results":{"feedsProcessed":8,"articlesProcessed":309,"accepted":6,"rejected":301,"successRate":2},"database":{"totalArticles":6,"candidateArticles":6,"publishedArticles":0},"optimizations":{"parallelProcessing":true,"smartPreFiltering":true,"englishFeedOptimization":true,"maxArticlesPerFeed":1},"firecrawl":{"totalRequests":67,"successfulRequests":67,"failedRequests":0,"successRate":100,"errors":{"rateLimits":0,"configuration":0,"authentication":0,"timeouts":0}}}}
{"timestamp":"2025-07-28T10:06:47.778Z","level":"INFO","component":"single-pipeline","message":"🔥 Firecrawl API usage summary","context":{"pipeline":"single-pipeline","firecrawl":{"totalRequests":67,"successfulRequests":67,"failedRequests":0,"successRate":100,"rateLimitErrors":0,"configErrors":0,"authErrors":0,"timeoutErrors":0}}}
