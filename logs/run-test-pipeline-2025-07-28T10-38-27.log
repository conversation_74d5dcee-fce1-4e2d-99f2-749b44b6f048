=== PIPELINE RUN: RUN-TEST-PIPELINE ===
Run ID: 2025-07-28T10-38-27
Start Time: 2025-07-28T10:38:27.792Z
Log File: run-test-pipeline-2025-07-28T10-38-27.log
===================================================
[2025-07-28T10:38:27.807Z] INFO  ✅ Database validation passed:
[2025-07-28T10:38:27.807Z] INFO     - Active keywords: 20
[2025-07-28T10:38:27.807Z] INFO     - Categories: 4
[2025-07-28T10:38:27.807Z] INFO       Keywords: ETF, Investition, Prognose, Wall Street, Dividende, Wachstumsaktien, Small Cap, Fusion, Börsengang, Aktien...
[2025-07-28T10:38:27.807Z] INFO  
🔍 Getting test RSS feed ID...
[2025-07-28T10:38:27.813Z] INFO  ✅ Using RSS feed for testing: DW (ID: 10)
[2025-07-28T10:38:27.813Z] INFO  
🔄 Processing predefined URLs with parallel processing...
[2025-07-28T10:38:27.813Z] INFO  🚀 Using OpenAI-safe parallel processing (2 concurrent URLs with rate limiting)...
[2025-07-28T10:38:27.813Z] INFO  
📦 Processing batch 1/1: 2 URLs
[2025-07-28T10:38:27.814Z] INFO  📰 [1/2] Processing: https://www.finanzen.net/nachricht/aktien/zurueckhaltung-tesla-aktie-unter-druck...
[2025-07-28T10:38:27.814Z] INFO  📰 [2/2] Processing: https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-i...
[2025-07-28T10:38:27.921Z] INFO        🔍 Keyword check result: matched - proceeding anyway for testing
[2025-07-28T10:38:27.921Z] INFO        🔍 Extracting content with enhanced client...
[2025-07-28T10:38:27.921Z] INFO  🚀 Enhanced extraction starting for: https://www.finanzen.net/nachricht/aktien/zurueckhaltung-tesla-aktie-unter-druck-schwache-verkaufszahlen-und-aerger-mit-trump-belasten-14597049
[2025-07-28T10:38:27.921Z] INFO  📋 Using site configuration for: Finanzen.net
[2025-07-28T10:38:27.921Z] INFO  🎯 Known German financial site: Finanzen.net
[2025-07-28T10:38:27.921Z] INFO  🎯 Selected strategy: Structured Data Enhanced
[2025-07-28T10:38:27.922Z] INFO  🎯 Attempting Structured Data Enhanced extraction for: https://www.finanzen.net/nachricht/aktien/zurueckhaltung-tesla-aktie-unter-druck-schwache-verkaufszahlen-und-aerger-mit-trump-belasten-14597049
[2025-07-28T10:38:27.923Z] INFO  📡 Standard extraction with options: {
  "formats": [
    "html"
  ],
  "timeout": 60000,
  "waitFor": 5000,
  "actionsCount": 1,
  "removeBase64Images": true,
  "blockAds": true,
  "excludeTagsCount": 6
}
[2025-07-28T10:38:27.929Z] INFO        🔍 Keyword check result: matched - proceeding anyway for testing
[2025-07-28T10:38:27.929Z] INFO        🔍 Extracting content with enhanced client...
[2025-07-28T10:38:27.929Z] INFO  🚀 Enhanced extraction starting for: https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-daimler-truck-kontron-mercedes-benz-nordex-und-siemens-im-check-20382487.html?feed=directrssfeed
[2025-07-28T10:38:27.930Z] INFO  📋 Using site configuration for: Der Aktionär
[2025-07-28T10:38:27.930Z] INFO  🎯 Known German financial site: Der Aktionär
[2025-07-28T10:38:27.930Z] INFO  🎯 Selected strategy: Structured Data Enhanced
[2025-07-28T10:38:27.930Z] INFO  🎯 Attempting Structured Data Enhanced extraction for: https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-daimler-truck-kontron-mercedes-benz-nordex-und-siemens-im-check-20382487.html?feed=directrssfeed
[2025-07-28T10:38:27.930Z] INFO  📡 Standard extraction with options: {
  "formats": [
    "html"
  ],
  "timeout": 54000,
  "waitFor": 4000,
  "actionsCount": 1,
  "removeBase64Images": true,
  "blockAds": true,
  "excludeTagsCount": 81
}
[2025-07-28T10:38:28.435Z] INFO   [32m[1m✓[22m[39m Compiled in 623ms (1476 modules)
[2025-07-28T10:38:28.669Z] INFO  Generating import map
[2025-07-28T10:38:28.749Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:38:28.873Z] INFO   [32m[1m✓[22m[39m Compiled in 434ms (1476 modules)
[2025-07-28T10:38:29.079Z] INFO  Generating import map
[2025-07-28T10:38:29.084Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:38:29.185Z] INFO   [32m[1m✓[22m[39m Compiled in 309ms (1476 modules)
[2025-07-28T10:38:29.456Z] INFO  Generating import map
[2025-07-28T10:38:29.461Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:38:29.484Z] INFO   [32m[1m✓[22m[39m Compiled in 295ms (1476 modules)
[2025-07-28T10:38:29.778Z] INFO  Generating import map
[2025-07-28T10:38:29.780Z] INFO   [32m[1m✓[22m[39m Compiled in 292ms (1476 modules)
[2025-07-28T10:38:29.786Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:38:30.094Z] INFO   [32m[1m✓[22m[39m Compiled in 311ms (1476 modules)
[2025-07-28T10:38:30.123Z] INFO  Generating import map
[2025-07-28T10:38:30.141Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:38:30.437Z] INFO   [32m[1m✓[22m[39m Compiled in 339ms (1476 modules)
[2025-07-28T10:38:30.482Z] INFO  Generating import map
[2025-07-28T10:38:30.494Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:38:30.774Z] INFO   [32m[1m✓[22m[39m Compiled in 333ms (1476 modules)
[2025-07-28T10:38:30.983Z] INFO  Generating import map
[2025-07-28T10:38:30.988Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:38:31.107Z] INFO   [32m[1m✓[22m[39m Compiled in 329ms (1476 modules)
[2025-07-28T10:38:31.334Z] INFO  Generating import map
[2025-07-28T10:38:31.343Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:38:31.449Z] INFO   [32m[1m✓[22m[39m Compiled in 338ms (1476 modules)
[2025-07-28T10:38:31.669Z] INFO  Generating import map
[2025-07-28T10:38:31.675Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:38:31.770Z] INFO   [32m[1m✓[22m[39m Compiled in 318ms (1476 modules)
[2025-07-28T10:38:31.988Z] INFO  Generating import map
[2025-07-28T10:38:31.997Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:38:32.099Z] INFO   [32m[1m✓[22m[39m Compiled in 326ms (1476 modules)
[2025-07-28T10:38:32.339Z] INFO  Generating import map
[2025-07-28T10:38:32.443Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:38:32.479Z] INFO   [32m[1m✓[22m[39m Compiled in 376ms (1476 modules)
[2025-07-28T10:38:32.743Z] INFO  Generating import map
[2025-07-28T10:38:32.826Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:38:32.861Z] INFO   [32m[1m✓[22m[39m Compiled in 379ms (1476 modules)
[2025-07-28T10:38:33.086Z] INFO  Generating import map
[2025-07-28T10:38:33.093Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:38:33.199Z] INFO   [32m[1m✓[22m[39m Compiled in 335ms (1476 modules)
[2025-07-28T10:38:33.439Z] INFO  Generating import map
[2025-07-28T10:38:33.631Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:38:33.737Z] INFO   [32m[1m✓[22m[39m Compiled in 534ms (1476 modules)
[2025-07-28T10:38:33.966Z] INFO  Generating import map
[2025-07-28T10:38:34.035Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:38:34.052Z] INFO   [32m[1m✓[22m[39m Compiled in 312ms (1476 modules)
[2025-07-28T10:38:34.342Z] INFO   [32m[1m✓[22m[39m Compiled in 286ms (1476 modules)
[2025-07-28T10:38:34.362Z] INFO  Generating import map
[2025-07-28T10:38:34.369Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:38:34.641Z] INFO   [32m[1m✓[22m[39m Compiled in 296ms (1476 modules)
[2025-07-28T10:38:34.832Z] INFO  Generating import map
[2025-07-28T10:38:34.839Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:38:34.947Z] INFO   [32m[1m✓[22m[39m Compiled in 302ms (1476 modules)
[2025-07-28T10:38:35.131Z] INFO  Generating import map
[2025-07-28T10:38:35.137Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:38:35.248Z] INFO   [32m[1m✓[22m[39m Compiled in 298ms (1476 modules)
[2025-07-28T10:38:35.464Z] INFO  Generating import map
[2025-07-28T10:38:35.469Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:38:35.562Z] INFO   [32m[1m✓[22m[39m Compiled in 311ms (1476 modules)
[2025-07-28T10:38:35.823Z] INFO  Generating import map
[2025-07-28T10:38:35.902Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:38:35.929Z] INFO   [32m[1m✓[22m[39m Compiled in 364ms (1476 modules)
[2025-07-28T10:38:36.222Z] INFO  Generating import map
[2025-07-28T10:38:36.228Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:38:36.251Z] INFO   [32m[1m✓[22m[39m Compiled in 319ms (1476 modules)
[2025-07-28T10:38:36.474Z] INFO  Generating import map
[2025-07-28T10:38:36.478Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:38:36.584Z] INFO   [32m[1m✓[22m[39m Compiled in 326ms (1476 modules)
[2025-07-28T10:38:36.818Z] INFO  Generating import map
[2025-07-28T10:38:36.901Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:38:36.941Z] INFO   [32m[1m✓[22m[39m Compiled in 354ms (1476 modules)
[2025-07-28T10:38:37.192Z] INFO  Generating import map
[2025-07-28T10:38:37.196Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:38:37.322Z] INFO   [32m[1m✓[22m[39m Compiled in 377ms (1476 modules)
[2025-07-28T10:38:37.557Z] INFO  Generating import map
[2025-07-28T10:38:37.570Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:38:37.710Z] INFO   [32m[1m✓[22m[39m Compiled in 385ms (1476 modules)
[2025-07-28T10:38:37.953Z] INFO  Generating import map
[2025-07-28T10:38:37.960Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:38:38.071Z] INFO   [32m[1m✓[22m[39m Compiled in 357ms (1476 modules)
[2025-07-28T10:38:38.363Z] INFO  Generating import map
[2025-07-28T10:38:38.376Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:38:38.485Z] INFO   [32m[1m✓[22m[39m Compiled in 411ms (1476 modules)
[2025-07-28T10:38:38.757Z] INFO  Generating import map
[2025-07-28T10:38:38.819Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:38:39.049Z] INFO   [32m[1m✓[22m[39m Compiled in 561ms (1476 modules)
[2025-07-28T10:38:39.274Z] INFO  Generating import map
[2025-07-28T10:38:39.342Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:38:39.370Z] INFO   [32m[1m✓[22m[39m Compiled in 317ms (1476 modules)
[2025-07-28T10:38:39.588Z] INFO  Generating import map
[2025-07-28T10:38:39.592Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:38:39.699Z] INFO   [32m[1m✓[22m[39m Compiled in 326ms (1476 modules)
[2025-07-28T10:38:39.918Z] INFO  Generating import map
[2025-07-28T10:38:39.982Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:38:40.009Z] INFO   [32m[1m✓[22m[39m Compiled in 305ms (1476 modules)
[2025-07-28T10:38:40.239Z] INFO  Generating import map
[2025-07-28T10:38:40.243Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:38:40.355Z] INFO   [32m[1m✓[22m[39m Compiled in 343ms (1476 modules)
[2025-07-28T10:38:40.601Z] INFO  Generating import map
[2025-07-28T10:38:40.606Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:38:40.718Z] INFO   [32m[1m✓[22m[39m Compiled in 360ms (1476 modules)
[2025-07-28T10:38:41.060Z] INFO  Generating import map
[2025-07-28T10:38:41.068Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:38:41.093Z] INFO   [32m[1m✓[22m[39m Compiled in 368ms (1476 modules)
[2025-07-28T10:38:41.334Z] INFO  Generating import map
[2025-07-28T10:38:41.338Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:38:41.451Z] INFO   [32m[1m✓[22m[39m Compiled in 355ms (1476 modules)
[2025-07-28T10:38:41.706Z] INFO  Generating import map
[2025-07-28T10:38:41.710Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:38:41.842Z] INFO   [32m[1m✓[22m[39m Compiled in 388ms (1476 modules)
[2025-07-28T10:38:42.041Z] INFO  ✅ Standard extraction successful
[2025-07-28T10:38:42.043Z] INFO  ✅ Enhanced extraction completed in 14122ms
[2025-07-28T10:38:42.043Z] INFO        📝 Creating candidate article with English-only enhancement
[2025-07-28T10:38:42.043Z] INFO  📅 Extracted date from HTML datetime: 2025-07-28T11:18:00
[2025-07-28T10:38:42.050Z] INFO  🌐 Feed language: DE (German feed)
[2025-07-28T10:38:42.050Z] INFO  📊 Content size check: 73665 characters (0.07MB) → 18417 estimated tokens
[2025-07-28T10:38:42.050Z] INFO  🚀 Starting English-only content enhancement for: Tesla-Aktie kräftig im Minus: Erwartetete Absatzflaute und Trumps Kritik belasten Kurs | finanzen.net
[2025-07-28T10:38:42.050Z] INFO  📏 Limits: Content size max 10MB, Token max 25000
[2025-07-28T10:38:42.051Z] INFO  🔄 Enhancement attempt 1/3...
[2025-07-28T10:38:42.051Z] INFO  🚀 Starting English-only content enhancement...
[2025-07-28T10:38:42.051Z] INFO  📄 Processing: "Tesla-Aktie kräftig im Minus: Erwartetete Absatzflaute und Trumps Kritik belasten Kurs | finanzen.net" (73665 chars, 0.07MB)
[2025-07-28T10:38:42.051Z] INFO  🚀 Using ENGLISH-ONLY ENHANCEMENT SYSTEM...
[2025-07-28T10:38:42.051Z] INFO  📝 Using simplified approach: Plain text generation with completion focus
[2025-07-28T10:38:42.051Z] INFO  🔄 [Rate Limiter] Queued request: english-enhancement-1753699122051-suwq0v (queue size: 1)
[2025-07-28T10:38:42.051Z] INFO  🚀 [Rate Limiter] Processing request: english-enhancement-1753699122051-suwq0v
[2025-07-28T10:38:42.043Z] INFO  📅 Extracted date from HTML content: 2025-07-28T10:18:00.000Z
[2025-07-28T10:38:42.051Z] INFO  📊 Token allocation: Prompt ~18916, Output limit: 16000, Available: 108084
[2025-07-28T10:38:42.230Z] INFO   [32m[1m✓[22m[39m Compiled in 385ms (1476 modules)
[2025-07-28T10:38:42.564Z] INFO   [32m[1m✓[22m[39m Compiled in 331ms (1476 modules)
[2025-07-28T10:38:42.591Z] INFO  Generating import map
[2025-07-28T10:38:42.601Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:38:42.901Z] INFO   [32m[1m✓[22m[39m Compiled in 334ms (1476 modules)
[2025-07-28T10:38:43.104Z] INFO  Generating import map
[2025-07-28T10:38:43.109Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:38:43.218Z] INFO   [32m[1m✓[22m[39m Compiled in 314ms (1476 modules)
[2025-07-28T10:38:43.432Z] INFO  Generating import map
[2025-07-28T10:38:43.437Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:38:43.540Z] INFO   [32m[1m✓[22m[39m Compiled in 319ms (1476 modules)
[2025-07-28T10:38:43.942Z] INFO  Generating import map
[2025-07-28T10:38:43.948Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:38:43.972Z] INFO   [32m[1m✓[22m[39m Compiled in 429ms (1476 modules)
[2025-07-28T10:38:44.404Z] INFO   [32m[1m✓[22m[39m Compiled in 423ms (1476 modules)
[2025-07-28T10:38:44.559Z] INFO  Generating import map
[2025-07-28T10:38:44.566Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:38:44.686Z] INFO   [32m[1m✓[22m[39m Compiled in 278ms (1476 modules)
[2025-07-28T10:38:44.858Z] INFO  Generating import map
[2025-07-28T10:38:44.863Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:38:44.967Z] INFO   [32m[1m✓[22m[39m Compiled in 278ms (1476 modules)
[2025-07-28T10:38:45.159Z] INFO  Generating import map
[2025-07-28T10:38:45.165Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:38:45.273Z] INFO   [32m[1m✓[22m[39m Compiled in 303ms (1476 modules)
[2025-07-28T10:38:45.474Z] INFO  Generating import map
[2025-07-28T10:38:45.478Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:38:45.579Z] INFO   [32m[1m✓[22m[39m Compiled in 303ms (1476 modules)
[2025-07-28T10:38:45.788Z] INFO  ✅ Standard extraction successful
[2025-07-28T10:38:45.790Z] INFO  ✅ Enhanced extraction completed in 17861ms
[2025-07-28T10:38:45.790Z] INFO  📅 Extracted date from HTML content: 2024-02-08T00:00:00.000Z
[2025-07-28T10:38:45.790Z] INFO        📝 Creating candidate article with English-only enhancement
[2025-07-28T10:38:45.795Z] INFO  🌐 Feed language: DE (German feed)
[2025-07-28T10:38:45.795Z] INFO  📏 Limits: Content size max 10MB, Token max 25000
[2025-07-28T10:38:45.795Z] INFO  🚀 Using ENGLISH-ONLY ENHANCEMENT SYSTEM...
[2025-07-28T10:38:45.795Z] INFO  🔄 Enhancement attempt 1/3...
[2025-07-28T10:38:45.795Z] INFO  🚀 Starting English-only content enhancement...
[2025-07-28T10:38:45.795Z] INFO  📄 Processing: "DAX macht 4.000 Punkte im 1. Halbjahr – Bayer, Daimler Truck, Kontron, Mercedes-Benz, Nordex und Siemens im Check - DER AKTIONÄR" (55346 chars, 0.05MB)
[2025-07-28T10:38:45.795Z] INFO  📊 Token allocation: Prompt ~14343, Output limit: 16000, Available: 112657
[2025-07-28T10:38:45.795Z] INFO  🔄 [Rate Limiter] Queued request: english-enhancement-1753699125795-yvqtsq (queue size: 1)
[2025-07-28T10:38:45.795Z] INFO  📝 Using simplified approach: Plain text generation with completion focus
[2025-07-28T10:38:45.790Z] INFO  📅 Extracted date from text pattern: 08.02.2024
[2025-07-28T10:38:45.795Z] INFO  📊 Content size check: 55346 characters (0.05MB) → 13837 estimated tokens
[2025-07-28T10:38:45.795Z] INFO  🚀 Starting English-only content enhancement for: DAX macht 4.000 Punkte im 1. Halbjahr – Bayer, Daimler Truck, Kontron, Mercedes-Benz, Nordex und Siemens im Check - DER AKTIONÄR
[2025-07-28T10:38:45.953Z] INFO   [32m[1m✓[22m[39m Compiled in 371ms (1476 modules)
[2025-07-28T10:38:46.194Z] INFO  Generating import map
[2025-07-28T10:38:46.279Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:38:46.308Z] INFO   [32m[1m✓[22m[39m Compiled in 351ms (1476 modules)
[2025-07-28T10:38:46.601Z] INFO  Generating import map
[2025-07-28T10:38:46.622Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:38:46.656Z] INFO   [32m[1m✓[22m[39m Compiled in 345ms (1476 modules)
[2025-07-28T10:38:46.893Z] INFO  Generating import map
[2025-07-28T10:38:46.902Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:38:47.002Z] INFO   [32m[1m✓[22m[39m Compiled in 342ms (1476 modules)
[2025-07-28T10:38:47.222Z] INFO  Generating import map
[2025-07-28T10:38:47.230Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:38:47.336Z] INFO   [32m[1m✓[22m[39m Compiled in 330ms (1476 modules)
[2025-07-28T10:38:47.571Z] INFO  Generating import map
[2025-07-28T10:38:47.576Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:38:47.699Z] INFO   [32m[1m✓[22m[39m Compiled in 360ms (1476 modules)
[2025-07-28T10:38:47.938Z] INFO  Generating import map
[2025-07-28T10:38:47.942Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:38:48.050Z] INFO   [32m[1m✓[22m[39m Compiled in 346ms (1476 modules)
[2025-07-28T10:38:48.328Z] INFO  Generating import map
[2025-07-28T10:38:48.429Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:38:48.468Z] INFO   [32m[1m✓[22m[39m Compiled in 413ms (1476 modules)
[2025-07-28T10:38:48.715Z] INFO  Generating import map
[2025-07-28T10:38:48.809Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:38:48.853Z] INFO   [32m[1m✓[22m[39m Compiled in 382ms (1476 modules)
[2025-07-28T10:38:49.083Z] INFO  Generating import map
[2025-07-28T10:38:49.161Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:38:49.215Z] INFO   [32m[1m✓[22m[39m Compiled in 358ms (1476 modules)
[2025-07-28T10:38:49.591Z] INFO  Generating import map
[2025-07-28T10:38:49.666Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:38:49.702Z] INFO   [32m[1m✓[22m[39m Compiled in 483ms (1476 modules)
[2025-07-28T10:38:49.931Z] INFO  Generating import map
[2025-07-28T10:38:50.006Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:38:50.035Z] INFO   [32m[1m✓[22m[39m Compiled in 329ms (1476 modules)
[2025-07-28T10:38:50.236Z] INFO  ✅ [Rate Limiter] Completed request: english-enhancement-1753699122051-suwq0v (1/100 this minute)
[2025-07-28T10:38:50.236Z] INFO  ⏳ [Rate Limiter] Enforcing 199ms delay between requests...
[2025-07-28T10:38:50.236Z] INFO  📊 Token usage: 633/16000 (4.0%)
[2025-07-28T10:38:50.236Z] INFO  📊 Plain text validation: ✅ Complete
[2025-07-28T10:38:50.237Z] INFO  📊 Word count: 345 words
[2025-07-28T10:38:50.237Z] INFO  📝 Converted plain text to HTML (2333 chars)
[2025-07-28T10:38:50.238Z] WARN  ⚠️ Content validation issues detected:
[2025-07-28T10:38:50.238Z] WARN     ⚠️ WARNING: Content appears truncated - ends with "developments in the Musk-Trump saga.</p>" instead of proper punctuation
[2025-07-28T10:38:50.238Z] WARN     Title length out of range (62, expected 50-60 chars)
[2025-07-28T10:38:50.238Z] WARN     ⚠️ Minor validation issues detected but proceeding (quality score: 75)
[2025-07-28T10:38:50.238Z] INFO  📊 Unified Enhancement Metrics:
    - Processing Time: 8187ms
    - Functions Covered: Direct English Enhancement, SEO Optimization, International Context Addition, Quality Assessment, Readability Optimization
    - API Calls Reduced: 7
    - Tokens Used: 24542
    - Cost: $0.4908
    - Success: ✅
[2025-07-28T10:38:50.238Z] INFO  ✅ English-only enhancement completed successfully
[2025-07-28T10:38:50.238Z] INFO  📊 Performance: 8187ms, 24542 tokens
[2025-07-28T10:38:50.238Z] INFO  💰 Cost reduction: -309.0%
[2025-07-28T10:38:50.239Z] INFO  🎯 Functions consolidated: 5
[2025-07-28T10:38:50.239Z] INFO  ⚡ Direct transformation: German → Enhanced English
[2025-07-28T10:38:50.239Z] INFO  ✅ English-only enhancement completed successfully
[2025-07-28T10:38:50.239Z] INFO     - Attempt: 1/3
[2025-07-28T10:38:50.239Z] INFO     - Enhanced English title: Tesla Stock Plummets Amid Sales Concerns and Trump...
[2025-07-28T10:38:50.239Z] INFO     - Content quality score: 90
[2025-07-28T10:38:50.239Z] INFO     - Enhancement quality: good
[2025-07-28T10:38:50.239Z] INFO     - Processing time: 8187ms
[2025-07-28T10:38:50.239Z] INFO     - Insights generated: 5
[2025-07-28T10:38:50.239Z] INFO     - Content length: 2331 characters
[2025-07-28T10:38:50.239Z] INFO  🔄 Converting original German content to Lexical format...
[2025-07-28T10:38:50.321Z] INFO  🔄 Processing content using strategy: html-direct {
  "inputSize": 73665,
  "sourceFormat": "html",
  "forceStrategy": false
}
[2025-07-28T10:38:50.424Z] INFO  %s: %s html-to-lexical-conversion 102.462ms
[2025-07-28T10:38:50.424Z] INFO  ✅ HTML→Lexical conversion successful {
  "duration": 102,
  "memoryDelta": 22542472
}
[2025-07-28T10:38:50.457Z] INFO  ✅ Content processing completed {
  "strategy": "html-direct",
  "processingTime": 186,
  "qualityScore": 100,
  "success": true
}
[2025-07-28T10:38:50.457Z] INFO  ✅ German content converted to Lexical successfully
[2025-07-28T10:38:50.457Z] INFO  🔄 Converting enhanced English content to Lexical format...
[2025-07-28T10:38:50.461Z] INFO  🔄 Processing content using strategy: html-direct {
  "inputSize": 2331,
  "sourceFormat": "html",
  "forceStrategy": false
}
[2025-07-28T10:38:50.469Z] INFO  %s: %s html-to-lexical-conversion 7.344ms
[2025-07-28T10:38:50.469Z] INFO  ✅ HTML→Lexical conversion successful {
  "duration": 8,
  "memoryDelta": 7232752
}
[2025-07-28T10:38:50.477Z] INFO  ✅ Content processing completed {
  "strategy": "html-direct",
  "processingTime": 12,
  "qualityScore": 75,
  "success": true
}
[2025-07-28T10:38:50.477Z] INFO  ✅ Enhanced English content converted to Lexical successfully
[2025-07-28T10:38:50.477Z] INFO  🔍 Searching for predefined keywords in article content...
[2025-07-28T10:38:50.483Z] INFO  🚀 [Rate Limiter] Processing request: english-enhancement-1753699125795-yvqtsq
[2025-07-28T10:38:50.494Z] INFO  ✅ Found predefined keyword in content: "etf" → ETF (ETF)
[2025-07-28T10:38:50.494Z] INFO  ✅ Found predefined keyword in content: "investment" → Investition (Investment)
[2025-07-28T10:38:50.494Z] INFO  ✅ Found predefined keyword in content: "fusion" → Fusion (Merger)
[2025-07-28T10:38:50.494Z] INFO  ✅ Found predefined keyword in content: "aktien" → Aktien (Stocks)
[2025-07-28T10:38:50.495Z] INFO  ✅ Found predefined keyword in content: "dax" → DAX (DAX)
[2025-07-28T10:38:50.495Z] INFO  ✅ Found predefined keyword in content: "technologie" → Technologie (Technology)
[2025-07-28T10:38:50.495Z] INFO  ✅ Found predefined keyword in content: "gold" → Gold (Gold)
[2025-07-28T10:38:50.638Z] INFO   [32m[1m✓[22m[39m Compiled in 600ms (1476 modules)
[2025-07-28T10:38:50.889Z] INFO  Generating import map
[2025-07-28T10:38:50.891Z] ERROR ❌ Failed to update usage count for keyword 2: Cannot read properties of undefined (reading 'id')
[2025-07-28T10:38:50.891Z] INFO  🔍 Pre-creation data inspection:
[2025-07-28T10:38:50.891Z] INFO  🔗 Found 7 predefined keywords in article content
[2025-07-28T10:38:50.891Z] INFO     - Original German title: "TeslaAktie kräftig im Minus: Erwartetete Absatzflaute und Trumps Kritik belasten Kurs | finanzen.net"
[2025-07-28T10:38:50.891Z] INFO     - Enhanced content available: Yes
[2025-07-28T10:38:50.891Z] INFO     - Enhancement quality: good
[2025-07-28T10:38:50.891Z] INFO     - Keywords matched: 7
[2025-07-28T10:38:50.891Z] INFO  🏢 Extracted 0 companies from content
[2025-07-28T10:38:50.891Z] INFO  📅 Article will include source publication date: 2025-07-28T10:18:00.000Z
[2025-07-28T10:38:50.962Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:38:50.970Z] INFO  🔍 SPRINT 3: Lexical Content Debug: {
  "operation": "create",
  "hasEnhancedContent": true,
  "contentType": "object",
  "contentStructure": "{\n  \"root\": {\n    \"children\": [\n      {\n        \"children\": [],\n        \"direction\": null,\n        \"format\": \"\",\n        \"indent\": 0,\n        \"type\": \"paragraph\",\n        \"version\": 1,\n        \"textFormat\": 0,\n        \"textStyle\": \"\"\n      },\n      {\n        \"children\": [\n          {\n            \"detail\": 0,\n            \"format\": 0,\n            \"mode\": \"normal\",\n            \"style\": \"\",\n            \"text\": \"Market Analysis\",\n            \"type\": \"text\",\n            \"version\": 1\n          }\n        ],\n        \"direction\": null,\n        \"format\": \"\",\n        \"indent\": 0,\n        \"type\": \"heading\",\n        \"version\": 1,\n        \"tag\": \"h2\"\n      },\n      {\n        \"children\": [\n          {\n            \"detail\": 0,\n            \"format\": 0,\n            \"mode\": \"normal\",\n            \"style\": \"\",\n            \"text\": \"Tesla's stock faced significant pressure on Tuesday as it dropped sharply due to anticipations of weak quarterly sales figures and a renewed conflict between Elon Musk and former President Donald Trump, which has unsettled investors. During early trading, Tesla's shares fell by almost 8%, eventually closing with a 5.34% loss at $300.71 on the NASDAQ. This decline brings the stock closer to its early June low, which was previously linked to tensions between Musk and Trump. While there was a brief recovery, recent events have reignited investor anxiety. Musk, who was once a close ally of Trump, has again criticized the tax and spending policies promoted by the former president. In retaliation, Trump took to his social network, Truth Social, claiming that Musk has received more subsidies than anyone in history and would struggle without them.\",\n            \"type\": \"text\",\n            \"version\": 1\n          }\n        ],\n        \"direction\": null,\n        \"format\": \"\",\n        \"indent\": 0,\n        \"type\": \"paragraph\",\n        \"version\": 1,\n        \"textFormat\": 0,\n        \"textStyle\": \"\"\n      },\n      {\n        \"children\": [\n          {\n            \"detail\": 0,\n            \"format\": 0,\n            \"mode\": \"normal\",\n            \"style\": \"\",\n            \"text\": \"Investor Reactions and Market Implications\",\n            \"type\": \"text\",\n            \"version\": 1\n          }\n        ],\n        \"direction\": null,\n        \"format\": \"\",\n        \"indent\": 0,\n        \"type\": \"heading\",\n        \"version\": 1,\n        \"tag\": \"h2\"\n      },\n      {\n        \"children\": [\n          {\n            \"detail\": 0,\n            \"format\": 0,\n            \"mode\": \"normal\",\n            \"style\": \"\",\n            \"text\": \"The ongoing spat has drawn the attention of market analysts, with Dirk Friczewsky from Activtrades suggesting that the media coverage of this dispute could further influence Tesla's stock performance. There are concerns that Trump might threaten to cut federal support for Tesla and Musk's other ventures, such as SpaceX, which could have implications for upcoming midterm elections. Adding to Tesla's challenges, the departure of Musk's close associate, Omead Afshar, has also weighed on the stock. Bloomberg reported that Musk plans to personally oversee sales strategies in Europe and the U.S., suggesting a strategic shift amid these turbulent times.\",\n            \"type\": \"text\",\n            \"version\": 1\n          }\n        ],\n        \"direction\": null,\n        \"format\": \"\",\n        \"indent\": 0,\n        \"type\": \"paragraph\",\n        \"version\": 1,\n        \"textFormat\": 0,\n        \"textStyle\": \"\"\n      },\n      {\n        \"children\": [\n          {\n            \"detail\": 0,\n            \"format\": 0,\n            \"mode\": \"normal\",\n            \"style\": \"\",\n            \"text\": \"Outlook and Analyst Insights\",\n            \"type\": \"text\",\n            \"version\": 1\n          }\n        ],\n        \"direction\": null,\n        \"format\": \"\",\n        \"indent\": 0,\n        \"type\": \"heading\",\n        \"version\": 1,\n        \"tag\": \"h2\"\n      },\n      {\n        \"children\": [\n          {\n            \"detail\": 0,\n            \"format\": 0,\n            \"mode\": \"normal\",\n            \"style\": \"\",\n            \"text\": \"As the market anticipates Teslas second-quarter sales results, there is widespread caution among investors. Edison Yu from Deutsche Bank Research anticipates that Tesla's sales might fall short of average market expectations. However, he notes that the market might not react strongly as investor expectations are already tempered. In conclusion, Tesla is navigating a complex landscape of internal challenges and external pressures, with investor sentiment being influenced by both anticipated sales performance and high-profile disputes involving its CEO. The coming weeks will be crucial as investors watch closely for Teslas actual sales figures and any further developments in the Musk-Trump saga.\",\n            \"type\": \"text\",\n            \"version\": 1\n          }\n        ],\n        \"direction\": null,\n        \"format\": \"\",\n        \"indent\": 0,\n        \"type\": \"paragraph\",\n        \"version\": 1,\n        \"textFormat\": 0,\n        \"textStyle\": \"\"\n      },\n      {\n        \"children\": [],\n        \"direction\": null,\n        \"format\": \"\",\n        \"indent\": 0,\n        \"type\": \"paragraph\",\n        \"version\": 1,\n        \"textFormat\": 0,\n        \"textStyle\": \"\"\n      }\n    ],\n    \"direction\": null,\n    \"format\": \"\",\n    \"indent\": 0,\n    \"type\": \"root\",\n    \"version\": 1\n  }\n}",
  "textExtraction": []
}
[2025-07-28T10:38:51.057Z] INFO   [32m[1m✓[22m[39m Compiled in 416ms (1476 modules)
[2025-07-28T10:38:51.247Z] INFO  🔄 Invalidating caches for article 172...
[2025-07-28T10:38:51.252Z] INFO  ✅ Cache invalidation completed for article 172
[2025-07-28T10:38:51.252Z] INFO     - Tags invalidated: 5
[2025-07-28T10:38:51.252Z] INFO     - Paths revalidated: No
[2025-07-28T10:38:51.256Z] INFO  ✅ Created candidate article with English-only enhancement and 7 predefined keywords found: Tesla-Aktie kräftig im Minus: Erwartetete Absatzflaute und Trumps Kritik belasten Kurs | finanzen.net (ID: 172)
[2025-07-28T10:38:51.256Z] INFO  📊 Article structure: Enhanced English content + German reference
[2025-07-28T10:38:51.257Z] INFO  📅 Publication date found: 2025-07-28T10:18:00.000Z
[2025-07-28T10:38:51.261Z] INFO  ✅ Using original publication date: 2025-07-28T10:18:00.000Z
[2025-07-28T10:38:51.298Z] INFO  ✅ Created processed URL record: https://www.finanzen.net/nachricht/aktien/zurueckhaltung-tesla-aktie-unter-druck-schwache-verkaufszahlen-und-aerger-mit-trump-belasten-14597049 (accepted) with publication date 2025-07-28T10:18:00.000Z
[2025-07-28T10:38:51.299Z] INFO     ✅ [1] Accepted: Tesla-Aktie kräftig im Minus: Erwartetete Absatzflaute und Trumps Kritik belasten Kurs | finanzen.net
[2025-07-28T10:38:51.406Z] INFO   [32m[1m✓[22m[39m Compiled in 347ms (1476 modules)
[2025-07-28T10:38:51.658Z] INFO   [32m[1m✓[22m[39m Compiled in 249ms (1476 modules)
[2025-07-28T10:38:51.677Z] INFO  Generating import map
[2025-07-28T10:38:51.686Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:38:51.951Z] INFO   [32m[1m✓[22m[39m Compiled in 290ms (1476 modules)
[2025-07-28T10:38:51.991Z] INFO  Generating import map
[2025-07-28T10:38:52.145Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:38:52.281Z] INFO   [32m[1m✓[22m[39m Compiled in 327ms (1476 modules)
[2025-07-28T10:38:52.485Z] INFO  Generating import map
[2025-07-28T10:38:52.495Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:38:52.601Z] INFO   [32m[1m✓[22m[39m Compiled in 316ms (1476 modules)
[2025-07-28T10:38:52.801Z] INFO  Generating import map
[2025-07-28T10:38:52.807Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:38:52.906Z] INFO   [32m[1m✓[22m[39m Compiled in 302ms (1476 modules)
[2025-07-28T10:38:53.144Z] INFO  Generating import map
[2025-07-28T10:38:53.215Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:38:53.245Z] INFO   [32m[1m✓[22m[39m Compiled in 335ms (1476 modules)
[2025-07-28T10:38:53.459Z] INFO  Generating import map
[2025-07-28T10:38:53.471Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:38:53.568Z] INFO   [32m[1m✓[22m[39m Compiled in 320ms (1476 modules)
[2025-07-28T10:38:53.822Z] INFO  Generating import map
[2025-07-28T10:38:53.831Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:38:53.976Z] INFO   [32m[1m✓[22m[39m Compiled in 405ms (1476 modules)
[2025-07-28T10:38:54.229Z] INFO  Generating import map
[2025-07-28T10:38:54.237Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:38:54.379Z] INFO   [32m[1m✓[22m[39m Compiled in 400ms (1476 modules)
[2025-07-28T10:38:54.630Z] INFO  Generating import map
[2025-07-28T10:38:54.641Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:38:54.766Z] INFO   [32m[1m✓[22m[39m Compiled in 384ms (1476 modules)
[2025-07-28T10:38:55.124Z] INFO  Generating import map
[2025-07-28T10:38:55.204Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:38:55.231Z] INFO   [32m[1m✓[22m[39m Compiled in 459ms (1476 modules)
[2025-07-28T10:38:55.547Z] INFO  Generating import map
[2025-07-28T10:38:55.552Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:38:55.578Z] INFO   [32m[1m✓[22m[39m Compiled in 344ms (1476 modules)
[2025-07-28T10:38:55.893Z] INFO  Generating import map
[2025-07-28T10:38:55.900Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:38:55.929Z] INFO   [32m[1m✓[22m[39m Compiled in 342ms (1476 modules)
[2025-07-28T10:38:56.199Z] INFO  Generating import map
[2025-07-28T10:38:56.205Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:38:56.309Z] INFO   [32m[1m✓[22m[39m Compiled in 376ms (1476 modules)
[2025-07-28T10:38:56.548Z] INFO  Generating import map
[2025-07-28T10:38:56.553Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:38:56.661Z] INFO   [32m[1m✓[22m[39m Compiled in 348ms (1476 modules)
[2025-07-28T10:38:56.901Z] INFO  Generating import map
[2025-07-28T10:38:56.906Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:38:57.024Z] INFO   [32m[1m✓[22m[39m Compiled in 359ms (1476 modules)
[2025-07-28T10:38:57.360Z] INFO  Generating import map
[2025-07-28T10:38:57.371Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:38:57.389Z] INFO   [32m[1m✓[22m[39m Compiled in 362ms (1476 modules)
[2025-07-28T10:38:57.699Z] INFO   [32m[1m✓[22m[39m Compiled in 306ms (1476 modules)
[2025-07-28T10:38:57.784Z] INFO  Generating import map
[2025-07-28T10:38:57.943Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:38:58.078Z] INFO   [32m[1m✓[22m[39m Compiled in 375ms (1476 modules)
[2025-07-28T10:38:58.278Z] INFO  Generating import map
[2025-07-28T10:38:58.283Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:38:58.400Z] INFO   [32m[1m✓[22m[39m Compiled in 319ms (1476 modules)
[2025-07-28T10:38:58.623Z] INFO  Generating import map
[2025-07-28T10:38:58.627Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:38:58.727Z] INFO   [32m[1m✓[22m[39m Compiled in 317ms (1476 modules)
[2025-07-28T10:38:58.979Z] INFO  Generating import map
[2025-07-28T10:38:58.986Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:38:59.098Z] INFO   [32m[1m✓[22m[39m Compiled in 357ms (1476 modules)
[2025-07-28T10:38:59.341Z] INFO  Generating import map
[2025-07-28T10:38:59.464Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:38:59.498Z] INFO   [32m[1m✓[22m[39m Compiled in 397ms (1476 modules)
[2025-07-28T10:38:59.745Z] INFO  Generating import map
[2025-07-28T10:38:59.759Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:38:59.881Z] INFO   [32m[1m✓[22m[39m Compiled in 380ms (1476 modules)
[2025-07-28T10:39:00.122Z] INFO  Generating import map
[2025-07-28T10:39:00.127Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:39:00.235Z] INFO   [32m[1m✓[22m[39m Compiled in 350ms (1476 modules)
[2025-07-28T10:39:00.632Z] INFO  Generating import map
[2025-07-28T10:39:00.718Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:39:00.748Z] INFO   [32m[1m✓[22m[39m Compiled in 508ms (1476 modules)
[2025-07-28T10:39:00.982Z] INFO  Generating import map
[2025-07-28T10:39:01.055Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:39:01.092Z] INFO   [32m[1m✓[22m[39m Compiled in 339ms (1476 modules)
[2025-07-28T10:39:01.437Z] INFO  Generating import map
[2025-07-28T10:39:01.444Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:39:01.463Z] INFO   [32m[1m✓[22m[39m Compiled in 368ms (1476 modules)
[2025-07-28T10:39:01.774Z] INFO   [32m[1m✓[22m[39m Compiled in 308ms (1476 modules)
[2025-07-28T10:39:01.796Z] INFO  Generating import map
[2025-07-28T10:39:01.827Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:39:02.136Z] INFO   [32m[1m✓[22m[39m Compiled in 359ms (1476 modules)
[2025-07-28T10:39:02.167Z] INFO  Generating import map
[2025-07-28T10:39:02.177Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:39:02.456Z] INFO   [32m[1m✓[22m[39m Compiled in 316ms (1476 modules)
[2025-07-28T10:39:02.667Z] INFO  Generating import map
[2025-07-28T10:39:02.672Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:39:02.779Z] INFO   [32m[1m✓[22m[39m Compiled in 319ms (1476 modules)
[2025-07-28T10:39:02.988Z] INFO  Generating import map
[2025-07-28T10:39:02.996Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:39:03.085Z] INFO   [32m[1m✓[22m[39m Compiled in 302ms (1476 modules)
[2025-07-28T10:39:03.315Z] INFO  Generating import map
[2025-07-28T10:39:03.381Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:39:03.410Z] INFO   [32m[1m✓[22m[39m Compiled in 321ms (1476 modules)
[2025-07-28T10:39:03.635Z] INFO  Generating import map
[2025-07-28T10:39:03.639Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:39:03.735Z] INFO   [32m[1m✓[22m[39m Compiled in 322ms (1476 modules)
[2025-07-28T10:39:03.977Z] INFO  Generating import map
[2025-07-28T10:39:04.048Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:39:04.090Z] INFO   [32m[1m✓[22m[39m Compiled in 352ms (1476 modules)
[2025-07-28T10:39:04.300Z] INFO  ✅ [Rate Limiter] Completed request: english-enhancement-1753699125795-yvqtsq (2/100 this minute)
[2025-07-28T10:39:04.300Z] INFO  📊 Word count: 282 words
[2025-07-28T10:39:04.300Z] INFO  📝 Converted plain text to HTML (2044 chars)
[2025-07-28T10:39:04.301Z] WARN  ⚠️ Content validation issues detected:
[2025-07-28T10:39:04.301Z] WARN     ⚠️ WARNING: Content appears truncated - ends with "to the evolving financial landscape.</p>" instead of proper punctuation
[2025-07-28T10:39:04.301Z] WARN     ⚠️ Content below target length (282 words, expected 600-750) - possible truncation
[2025-07-28T10:39:04.301Z] WARN     Title length out of range (82, expected 50-60 chars)
[2025-07-28T10:39:04.301Z] WARN     Summary length out of range (303, expected 100-150 chars)
[2025-07-28T10:39:04.301Z] WARN     ⚠️ Minor validation issues detected but proceeding (quality score: 50)
[2025-07-28T10:39:04.301Z] INFO  ✅ English-only enhancement completed successfully
[2025-07-28T10:39:04.301Z] INFO  📊 Unified Enhancement Metrics:
    - Processing Time: 18506ms
    - Functions Covered: Direct English Enhancement, SEO Optimization, International Context Addition, Quality Assessment, Readability Optimization
    - API Calls Reduced: 7
    - Tokens Used: 23341
    - Cost: $0.4668
    - Success: ✅
[2025-07-28T10:39:04.301Z] INFO  💰 Cost reduction: -289.0%
[2025-07-28T10:39:04.301Z] INFO  📊 Performance: 18506ms, 23341 tokens
[2025-07-28T10:39:04.301Z] INFO  ⚡ Direct transformation: German → Enhanced English
[2025-07-28T10:39:04.301Z] INFO  🎯 Functions consolidated: 5
[2025-07-28T10:39:04.302Z] INFO  ✅ English-only enhancement completed successfully
[2025-07-28T10:39:04.302Z] INFO     - Attempt: 1/3
[2025-07-28T10:39:04.302Z] INFO     - Enhanced English title: DAX Briefly Surpasses 24,000: Bayer, Daimler Truck...
[2025-07-28T10:39:04.302Z] INFO     - Content quality score: 95
[2025-07-28T10:39:04.302Z] INFO     - Enhancement quality: good
[2025-07-28T10:39:04.302Z] INFO     - Processing time: 18506ms
[2025-07-28T10:39:04.302Z] INFO     - Insights generated: 4
[2025-07-28T10:39:04.302Z] INFO     - Content length: 2044 characters
[2025-07-28T10:39:04.302Z] INFO  🔄 Converting original German content to Lexical format...
[2025-07-28T10:39:04.334Z] INFO  🔄 Processing content using strategy: html-direct {
  "inputSize": 55346,
  "sourceFormat": "html",
  "forceStrategy": false
}
[2025-07-28T10:39:04.396Z] INFO  ✅ HTML→Lexical conversion successful {
  "duration": 62,
  "memoryDelta": 6269128
}
[2025-07-28T10:39:04.396Z] INFO  %s: %s html-to-lexical-conversion 62.188ms
[2025-07-28T10:39:04.420Z] INFO  ✅ German content converted to Lexical successfully
[2025-07-28T10:39:04.420Z] INFO  ✅ Content processing completed {
  "strategy": "html-direct",
  "processingTime": 95,
  "qualityScore": 100,
  "success": true
}
[2025-07-28T10:39:04.300Z] INFO  📊 Token usage: 591/16000 (3.7%)
[2025-07-28T10:39:04.420Z] INFO  🔄 Converting enhanced English content to Lexical format...
[2025-07-28T10:39:04.424Z] INFO  🔄 Processing content using strategy: html-direct {
  "inputSize": 2044,
  "sourceFormat": "html",
  "forceStrategy": false
}
[2025-07-28T10:39:04.449Z] INFO  %s: %s html-to-lexical-conversion 24.423ms
[2025-07-28T10:39:04.449Z] INFO  ✅ HTML→Lexical conversion successful {
  "duration": 25,
  "memoryDelta": 1869112
}
[2025-07-28T10:39:04.452Z] INFO  ✅ Content processing completed {
  "strategy": "html-direct",
  "processingTime": 29,
  "qualityScore": 70,
  "success": true
}
[2025-07-28T10:39:04.452Z] INFO  🔍 Searching for predefined keywords in article content...
[2025-07-28T10:39:04.452Z] INFO  ✅ Enhanced English content converted to Lexical successfully
[2025-07-28T10:39:04.300Z] INFO  📊 Plain text validation: ✅ Complete
[2025-07-28T10:39:04.471Z] INFO  ✅ Found predefined keyword in content: "aktien" → Aktien (Stocks)
[2025-07-28T10:39:04.471Z] INFO  ✅ Found predefined keyword in content: "dax" → DAX (DAX)
[2025-07-28T10:39:04.550Z] INFO  🔍 Pre-creation data inspection:
[2025-07-28T10:39:04.550Z] INFO     - Enhanced content available: Yes
[2025-07-28T10:39:04.550Z] INFO     - Enhancement quality: good
[2025-07-28T10:39:04.550Z] INFO  🏢 Extracted 0 companies from content
[2025-07-28T10:39:04.550Z] INFO  📅 Article will include source publication date: 2024-02-08T00:00:00.000Z
[2025-07-28T10:39:04.550Z] INFO  🔗 Found 2 predefined keywords in article content
[2025-07-28T10:39:04.550Z] INFO     - Original German title: "DAX macht 4.000 Punkte im 1. Halbjahr Bayer, Daimler Truck, Kontron, MercedesBenz, Nordex und Siemens im Check DER AKTIONÄR"
[2025-07-28T10:39:04.550Z] INFO     - Keywords matched: 2
[2025-07-28T10:39:04.553Z] INFO  🔍 SPRINT 3: Lexical Content Debug: {
  "operation": "create",
  "hasEnhancedContent": true,
  "contentType": "object",
  "contentStructure": "{\n  \"root\": {\n    \"children\": [\n      {\n        \"children\": [],\n        \"direction\": null,\n        \"format\": \"\",\n        \"indent\": 0,\n        \"type\": \"paragraph\",\n        \"version\": 1,\n        \"textFormat\": 0,\n        \"textStyle\": \"\"\n      },\n      {\n        \"children\": [\n          {\n            \"detail\": 0,\n            \"format\": 0,\n            \"mode\": \"normal\",\n            \"style\": \"\",\n            \"text\": \"Market Analysis\",\n            \"type\": \"text\",\n            \"version\": 1\n          }\n        ],\n        \"direction\": null,\n        \"format\": \"\",\n        \"indent\": 0,\n        \"type\": \"heading\",\n        \"version\": 1,\n        \"tag\": \"h2\"\n      },\n      {\n        \"children\": [\n          {\n            \"detail\": 0,\n            \"format\": 0,\n            \"mode\": \"normal\",\n            \"style\": \"\",\n            \"text\": \"The German stock index, DAX, made a brief foray above the 24,000 mark on Monday, only to retreat by 0.5%, closing 123 points lower. As the market opened on Tuesday, attention was drawn again to this psychologically significant level, with major players like Bayer, Daimler Truck, Kontron, Mercedes-Benz, Nordex, and Siemens taking center stage.\",\n            \"type\": \"text\",\n            \"version\": 1\n          }\n        ],\n        \"direction\": null,\n        \"format\": \"\",\n        \"indent\": 0,\n        \"type\": \"paragraph\",\n        \"version\": 1,\n        \"textFormat\": 0,\n        \"textStyle\": \"\"\n      },\n      {\n        \"children\": [\n          {\n            \"detail\": 0,\n            \"format\": 0,\n            \"mode\": \"normal\",\n            \"style\": \"\",\n            \"text\": \"Key Developments\",\n            \"type\": \"text\",\n            \"version\": 1\n          }\n        ],\n        \"direction\": null,\n        \"format\": \"\",\n        \"indent\": 0,\n        \"type\": \"heading\",\n        \"version\": 1,\n        \"tag\": \"h3\"\n      },\n      {\n        \"children\": [\n          {\n            \"detail\": 0,\n            \"format\": 0,\n            \"mode\": \"normal\",\n            \"style\": \"\",\n            \"text\": \"Bayer and Daimler Truck have been under the microscope due to their potential to influence market dynamics. Bayer continues to navigate challenges in the pharmaceutical sector, while Daimler Truck's focus on sustainable transportation solutions keeps investors intrigued. Mercedes-Benz, a leader in automotive innovation, remains a key player as it accelerates its push towards electric vehicles. Meanwhile, Siemens continues to be a pivotal force in the industrial sector, leveraging its technological advancements to maintain market leadership. Kontron, known for its embedded computing solutions, and Nordex, a significant name in renewable energy, are also garnering interest. These companies highlight the diverse sectors contributing to the DAX's performance.\",\n            \"type\": \"text\",\n            \"version\": 1\n          }\n        ],\n        \"direction\": null,\n        \"format\": \"\",\n        \"indent\": 0,\n        \"type\": \"paragraph\",\n        \"version\": 1,\n        \"textFormat\": 0,\n        \"textStyle\": \"\"\n      },\n      {\n        \"children\": [\n          {\n            \"detail\": 0,\n            \"format\": 0,\n            \"mode\": \"normal\",\n            \"style\": \"\",\n            \"text\": \"Investment Insights\",\n            \"type\": \"text\",\n            \"version\": 1\n          }\n        ],\n        \"direction\": null,\n        \"format\": \"\",\n        \"indent\": 0,\n        \"type\": \"heading\",\n        \"version\": 1,\n        \"tag\": \"h2\"\n      },\n      {\n        \"children\": [\n          {\n            \"detail\": 0,\n            \"format\": 0,\n            \"mode\": \"normal\",\n            \"style\": \"\",\n            \"text\": \"The market's volatility underscores the importance of strategic investment decisions. Investors are advised to keep a close watch on these key players, as their developments could significantly impact the DAX's trajectory. Financial stakeholders should consider the broader economic context, including potential shifts in market sentiment and regulatory changes. With technological and sustainable innovations at the forefront, companies like Bayer and Siemens are well-positioned to capitalize on emerging trends.\",\n            \"type\": \"text\",\n            \"version\": 1\n          }\n        ],\n        \"direction\": null,\n        \"format\": \"\",\n        \"indent\": 0,\n        \"type\": \"paragraph\",\n        \"version\": 1,\n        \"textFormat\": 0,\n        \"textStyle\": \"\"\n      },\n      {\n        \"children\": [\n          {\n            \"detail\": 0,\n            \"format\": 0,\n            \"mode\": \"normal\",\n            \"style\": \"\",\n            \"text\": \"Conclusion\",\n            \"type\": \"text\",\n            \"version\": 1\n          }\n        ],\n        \"direction\": null,\n        \"format\": \"\",\n        \"indent\": 0,\n        \"type\": \"heading\",\n        \"version\": 1,\n        \"tag\": \"h3\"\n      },\n      {\n        \"children\": [\n          {\n            \"detail\": 0,\n            \"format\": 0,\n            \"mode\": \"normal\",\n            \"style\": \"\",\n            \"text\": \"As the DAX hovers around the critical 24,000 mark, the focus remains on these influential companies. Their performance and strategic maneuvers will likely play a crucial role in shaping the market's future. Investors are encouraged to stay informed and agile, ready to adapt to the evolving financial landscape.\",\n            \"type\": \"text\",\n            \"version\": 1\n          }\n        ],\n        \"direction\": null,\n        \"format\": \"\",\n        \"indent\": 0,\n        \"type\": \"paragraph\",\n        \"version\": 1,\n        \"textFormat\": 0,\n        \"textStyle\": \"\"\n      },\n      {\n        \"children\": [],\n        \"direction\": null,\n        \"format\": \"\",\n        \"indent\": 0,\n        \"type\": \"paragraph\",\n        \"version\": 1,\n        \"textFormat\": 0,\n        \"textStyle\": \"\"\n      }\n    ],\n    \"direction\": null,\n    \"format\": \"\",\n    \"indent\": 0,\n    \"type\": \"root\",\n    \"version\": 1\n  }\n}",
  "textExtraction": []
}
[2025-07-28T10:39:04.669Z] INFO   [32m[1m✓[22m[39m Compiled in 576ms (1476 modules)
[2025-07-28T10:39:04.699Z] INFO  🔄 Invalidating caches for article 173...
[2025-07-28T10:39:04.699Z] INFO     - Tags invalidated: 5
[2025-07-28T10:39:04.699Z] INFO     - Paths revalidated: No
[2025-07-28T10:39:04.699Z] INFO  ✅ Cache invalidation completed for article 173
[2025-07-28T10:39:04.705Z] INFO  ✅ Created candidate article with English-only enhancement and 2 predefined keywords found: DAX macht 4.000 Punkte im 1. Halbjahr – Bayer, Daimler Truck, Kontron, Mercedes-Benz, Nordex und Siemens im Check - DER AKTIONÄR (ID: 173)
[2025-07-28T10:39:04.705Z] INFO  📅 Publication date found: 2024-02-08T00:00:00.000Z
[2025-07-28T10:39:04.712Z] INFO  ✅ Using original publication date: 2024-02-08T00:00:00.000Z
[2025-07-28T10:39:04.705Z] INFO  📊 Article structure: Enhanced English content + German reference
[2025-07-28T10:39:04.767Z] INFO     ✅ [2] Accepted: DAX macht 4.000 Punkte im 1. Halbjahr – Bayer, Daimler Truck, Kontron, Mercedes-Benz, Nordex und Siemens im Check - DER AKTIONÄR
[2025-07-28T10:39:04.767Z] INFO  ✅ Created processed URL record: https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-daimler-truck-kontron-mercedes-benz-nordex-und-siemens-im-check-20382487.html?feed=directrssfeed (accepted) with publication date 2024-02-08T00:00:00.000Z
[2025-07-28T10:39:04.955Z] INFO  
📊 Enhanced Firecrawl Usage Summary:
[2025-07-28T10:39:04.955Z] INFO     📈 Recent operations: 0
[2025-07-28T10:39:04.955Z] INFO     ✅ Success rate: 100.0%
[2025-07-28T10:39:04.955Z] INFO     ❌ Failed requests: 0
[2025-07-28T10:39:04.955Z] INFO     🔄 Consecutive failures: 0
[2025-07-28T10:39:04.955Z] INFO     📊 View detailed alerts: /api/firecrawl-alerts?action=dashboard
[2025-07-28T10:39:04.955Z] INFO  
🎯 Optimized Test Pipeline Summary:
[2025-07-28T10:39:04.955Z] INFO     📊 URLs processed: 2
[2025-07-28T10:39:04.955Z] INFO     ✅ Articles processed: 2
[2025-07-28T10:39:04.955Z] INFO     💥 Errors: 0
[2025-07-28T10:39:04.955Z] INFO     ⏱️  Processing time: 37s
[2025-07-28T10:39:04.955Z] INFO     🚀 Optimizations: OpenAI-safe parallel processing (2 concurrent URLs + rate limiting)
[2025-07-28T10:39:04.955Z] INFO     🎯 Processing: German → Enhanced English + English → Company Extraction
[2025-07-28T10:39:04.955Z] INFO     💰 Cost reduction: ~70% (single API call vs 7 calls)
[2025-07-28T10:39:04.955Z] INFO     ❌ Articles rejected: 0
[2025-07-28T10:39:04.955Z] INFO     ⚡ Performance boost: ~70% faster via parallel processing
[2025-07-28T10:39:04.955Z] INFO     📈 Total articles in database: 2
[2025-07-28T10:39:04.955Z] INFO     🔍 Candidate articles: 2
[2025-07-28T10:39:04.955Z] INFO     📰 Published articles: 0
[2025-07-28T10:39:04.956Z] INFO  📧 Generating pipeline email report...

=== PIPELINE RUN COMPLETED ===
End Time: 2025-07-28T10:39:04.964Z
Duration: 37s (37172ms)
Run ID: 2025-07-28T10-38-27
Summary: {}
================================
[2025-07-28T10:39:04.970Z] INFO  
📝 Log file saved: /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/logs/run-test-pipeline-2025-07-28T10-38-27.log
[2025-07-28T10:39:04.970Z] INFO  ⏱️  Run duration: 37s
