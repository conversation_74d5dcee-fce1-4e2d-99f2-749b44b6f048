{"timestamp":"2025-07-27T21:01:24.284Z","level":"ERROR","component":"content-pipeline","message":"English-only enhancement failed completely","context":{"title":"<PERSON><PERSON><PERSON> von BMW, VW und Tesla im globalen E-Automarkt: Europa erlebt Renaissance, China dominiert","url":"https://www.finanzen.net/nachricht/aktien/zulassungen-steigen-aktien-von-bmw-vw-und-tesla-im-globalen-e-automarkt-europa-erlebt-renaissance-china-dominiert-14660862","contentLength":77759,"feedLanguage":"de","errorCategory":"validation_error"},"error":{"name":"Error","message":"All 3 enhancement attempts failed. Last error: 400 Invalid 'max_output_tokens': integer below minimum value. Expected a value >= 16, but got -13408 instead.","stack":"Error: All 3 enhancement attempts failed. Last error: 400 Invalid 'max_output_tokens': integer below minimum value. Expected a value >= 16, but got -13408 instead.\n    at createCandidateArticle (webpack-internal:///(rsc)/./src/lib/server/create-candidate.ts:178:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async eval (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:476:47)\n    at async Promise.all (index 0)\n    at async RSSProcessingService.processSingleFeed (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:539:17)\n    at async RSSProcessingService.processFeeds (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:145:40)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-single-pipeline/route.ts:122:34)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-27T21:29:51.928Z","level":"ERROR","component":"content-pipeline","message":"English-only enhancement failed completely","context":{"title":"dax macht  punkte im  halbjahr bayer daimler truck kontron mercedes benz nordex und siemens im check","url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-daimler-truck-kontron-mercedes-benz-nordex-und-siemens-im-check-20382487.html?feed=directrssfeed","contentLength":48,"feedLanguage":"de","errorCategory":"conversion_error"},"error":{"name":"Error","message":"All 3 enhancement attempts failed. Last error: Content appears incomplete despite normal token usage (13.4%). Check content quality and prompt instructions. Issues: 🚨 CRITICAL: Content appears truncated - ends with \"observing how these efforts to\" instead of proper punctuation, 🚨 CRITICAL: HTML structure incomplete - 19 open tags vs 18 close tags (likely truncated), 🚨 CRITICAL: Content severely truncated (251 words, expected 600-750) - likely token limit issue","stack":"Error: All 3 enhancement attempts failed. Last error: Content appears incomplete despite normal token usage (13.4%). Check content quality and prompt instructions. Issues: 🚨 CRITICAL: Content appears truncated - ends with \"observing how these efforts to\" instead of proper punctuation, 🚨 CRITICAL: HTML structure incomplete - 19 open tags vs 18 close tags (likely truncated), 🚨 CRITICAL: Content severely truncated (251 words, expected 600-750) - likely token limit issue\n    at createCandidateArticle (webpack-internal:///(rsc)/./src/lib/server/create-candidate.ts:178:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async processTestUrl (webpack-internal:///(rsc)/./src/app/api/test-content-pipeline/route.ts:676:31)\n    at async eval (webpack-internal:///(rsc)/./src/app/api/test-content-pipeline/route.ts:209:46)\n    at async Promise.all (index 1)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/test-content-pipeline/route.ts:276:13)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-27T21:47:00.894Z","level":"ERROR","component":"content-pipeline","message":"English-only enhancement failed completely","context":{"title":"Israel: Gute Umfragewerte – Netanjahu könnte Neuwahlen provozieren","url":"https://www.handelsblatt.com/politik/international/israel-gute-umfragewerte-netanjahu-koennte-neuwahlen-provozieren/100138438.html","contentLength":31949,"feedLanguage":"de","errorCategory":"conversion_error"},"error":{"name":"Error","message":"All 3 enhancement attempts failed. Last error: Content appears incomplete despite normal token usage (4.8%). Check content quality and prompt instructions. Issues: 🚨 CRITICAL: Content appears truncated - ends with \"often viewed as undermining democratic\" instead of proper punctuation, 🚨 CRITICAL: HTML structure incomplete - 9 open tags vs 8 close tags (likely truncated), 🚨 CRITICAL: Content severely truncated (275 words, expected 600-750) - likely token limit issue","stack":"Error: All 3 enhancement attempts failed. Last error: Content appears incomplete despite normal token usage (4.8%). Check content quality and prompt instructions. Issues: 🚨 CRITICAL: Content appears truncated - ends with \"often viewed as undermining democratic\" instead of proper punctuation, 🚨 CRITICAL: HTML structure incomplete - 9 open tags vs 8 close tags (likely truncated), 🚨 CRITICAL: Content severely truncated (275 words, expected 600-750) - likely token limit issue\n    at createCandidateArticle (webpack-internal:///(rsc)/./src/lib/server/create-candidate.ts:178:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async processTestUrl (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:676:31)\n    at async eval (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:209:46)\n    at async Promise.all (index 0)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:276:13)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-27T21:49:01.621Z","level":"ERROR","component":"content-pipeline","message":"English-only enhancement failed completely","context":{"title":"dax macht  punkte im  halbjahr bayer daimler truck kontron mercedes benz nordex und siemens im check","url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-daimler-truck-kontron-mercedes-benz-nordex-und-siemens-im-check-20382487.html?feed=directrssfeed","contentLength":48,"feedLanguage":"de","errorCategory":"conversion_error"},"error":{"name":"Error","message":"All 3 enhancement attempts failed. Last error: Content appears incomplete despite normal token usage (5.5%). Check content quality and prompt instructions. Issues: 🚨 CRITICAL: Content appears truncated - ends with \"contributor to the renewable wind\" instead of proper punctuation, 🚨 CRITICAL: HTML structure incomplete - 22 open tags vs 21 close tags (likely truncated), 🚨 CRITICAL: Content severely truncated (240 words, expected 600-750) - likely token limit issue","stack":"Error: All 3 enhancement attempts failed. Last error: Content appears incomplete despite normal token usage (5.5%). Check content quality and prompt instructions. Issues: 🚨 CRITICAL: Content appears truncated - ends with \"contributor to the renewable wind\" instead of proper punctuation, 🚨 CRITICAL: HTML structure incomplete - 22 open tags vs 21 close tags (likely truncated), 🚨 CRITICAL: Content severely truncated (240 words, expected 600-750) - likely token limit issue\n    at createCandidateArticle (webpack-internal:///(rsc)/./src/lib/server/create-candidate.ts:178:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async processTestUrl (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:676:31)\n    at async eval (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:209:46)\n    at async Promise.all (index 1)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:276:13)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-27T21:50:38.091Z","level":"ERROR","component":"content-pipeline","message":"English-only enhancement failed completely","context":{"title":"Tesla-Aktie kräftig im Minus: Erwartetete Absatzflaute und Trumps Kritik belasten Kurs | finanzen.ne","url":"https://www.finanzen.net/nachricht/aktien/zurueckhaltung-tesla-aktie-unter-druck-schwache-verkaufszahlen-und-aerger-mit-trump-belasten-14597049","contentLength":73520,"feedLanguage":"de","errorCategory":"conversion_error"},"error":{"name":"Error","message":"All 3 enhancement attempts failed. Last error: Content appears incomplete despite normal token usage (4.5%). Check content quality and prompt instructions. Issues: 🚨 CRITICAL: Content appears truncated - ends with \"especially as it prepares to\" instead of proper punctuation, 🚨 CRITICAL: HTML structure incomplete - 10 open tags vs 9 close tags (likely truncated), 🚨 CRITICAL: Content severely truncated (279 words, expected 600-750) - likely token limit issue","stack":"Error: All 3 enhancement attempts failed. Last error: Content appears incomplete despite normal token usage (4.5%). Check content quality and prompt instructions. Issues: 🚨 CRITICAL: Content appears truncated - ends with \"especially as it prepares to\" instead of proper punctuation, 🚨 CRITICAL: HTML structure incomplete - 10 open tags vs 9 close tags (likely truncated), 🚨 CRITICAL: Content severely truncated (279 words, expected 600-750) - likely token limit issue\n    at createCandidateArticle (webpack-internal:///(rsc)/./src/lib/server/create-candidate.ts:178:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async processTestUrl (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:676:31)\n    at async eval (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:209:46)\n    at async Promise.all (index 0)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:276:13)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-27T21:50:48.640Z","level":"ERROR","component":"content-pipeline","message":"English-only enhancement failed completely","context":{"title":"Dax Vorbörse: Dax dürfte mit leichtem Schwung in den Juli starten • news • onvista","url":"https://www.onvista.de/news/2025/07-01-dax-vorboerse-dax-duerfte-mit-leichtem-schwung-in-den-juli-starten-41121301-19-26405537","contentLength":44964,"feedLanguage":"de","errorCategory":"conversion_error"},"error":{"name":"Error","message":"All 3 enhancement attempts failed. Last error: Content appears incomplete despite normal token usage (5.0%). Check content quality and prompt instructions. Issues: 🚨 CRITICAL: Content appears truncated - ends with \"to a public holiday.</p> <h3>R\" instead of proper punctuation, 🚨 CRITICAL: HTML structure incomplete - 12 open tags vs 11 close tags (likely truncated), 🚨 CRITICAL: Content severely truncated (291 words, expected 600-750) - likely token limit issue","stack":"Error: All 3 enhancement attempts failed. Last error: Content appears incomplete despite normal token usage (5.0%). Check content quality and prompt instructions. Issues: 🚨 CRITICAL: Content appears truncated - ends with \"to a public holiday.</p> <h3>R\" instead of proper punctuation, 🚨 CRITICAL: HTML structure incomplete - 12 open tags vs 11 close tags (likely truncated), 🚨 CRITICAL: Content severely truncated (291 words, expected 600-750) - likely token limit issue\n    at createCandidateArticle (webpack-internal:///(rsc)/./src/lib/server/create-candidate.ts:178:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async processTestUrl (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:676:31)\n    at async eval (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:209:46)\n    at async Promise.all (index 1)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:276:13)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-27T21:51:48.537Z","level":"ERROR","component":"content-pipeline","message":"English-only enhancement failed completely","context":{"title":"Ölpreise fallen weiter - Warten auf Förderbeschluss der Opec+ - 01.07.2025","url":"https://www.wallstreet-online.de/nachricht/19557818-oelpreise-fallen-warten-foerderbeschluss-opec","contentLength":20550,"feedLanguage":"de","errorCategory":"conversion_error"},"error":{"name":"Error","message":"All 3 enhancement attempts failed. Last error: Content appears incomplete despite normal token usage (4.2%). Check content quality and prompt instructions. Issues: 🚨 CRITICAL: Content appears truncated - ends with \"oil market faces both near\" instead of proper punctuation, 🚨 CRITICAL: HTML structure incomplete - 9 open tags vs 8 close tags (likely truncated), 🚨 CRITICAL: Content severely truncated (286 words, expected 600-750) - likely token limit issue","stack":"Error: All 3 enhancement attempts failed. Last error: Content appears incomplete despite normal token usage (4.2%). Check content quality and prompt instructions. Issues: 🚨 CRITICAL: Content appears truncated - ends with \"oil market faces both near\" instead of proper punctuation, 🚨 CRITICAL: HTML structure incomplete - 9 open tags vs 8 close tags (likely truncated), 🚨 CRITICAL: Content severely truncated (286 words, expected 600-750) - likely token limit issue\n    at createCandidateArticle (webpack-internal:///(rsc)/./src/lib/server/create-candidate.ts:178:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async processTestUrl (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:676:31)\n    at async eval (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:209:46)\n    at async Promise.all (index 0)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:276:13)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-27T21:56:37.880Z","level":"ERROR","component":"content-pipeline","message":"English-only enhancement failed completely","context":{"title":"Israel: Gute Umfragewerte – Netanjahu könnte Neuwahlen provozieren","url":"https://www.handelsblatt.com/politik/international/israel-gute-umfragewerte-netanjahu-koennte-neuwahlen-provozieren/100138438.html","contentLength":31951,"feedLanguage":"de","errorCategory":"conversion_error"},"error":{"name":"Error","message":"All 3 enhancement attempts failed. Last error: Content appears incomplete despite normal token usage (3.8%). Check content quality and prompt instructions. Issues: 🚨 CRITICAL: Content appears truncated - ends with \"leadership or government policy can,\" instead of proper punctuation, 🚨 CRITICAL: HTML structure incomplete - 11 open tags vs 10 close tags (likely truncated), 🚨 CRITICAL: Content contains unprocessed JSON/structured data - likely parsing truncation","stack":"Error: All 3 enhancement attempts failed. Last error: Content appears incomplete despite normal token usage (3.8%). Check content quality and prompt instructions. Issues: 🚨 CRITICAL: Content appears truncated - ends with \"leadership or government policy can,\" instead of proper punctuation, 🚨 CRITICAL: HTML structure incomplete - 11 open tags vs 10 close tags (likely truncated), 🚨 CRITICAL: Content contains unprocessed JSON/structured data - likely parsing truncation\n    at createCandidateArticle (webpack-internal:///(rsc)/./src/lib/server/create-candidate.ts:178:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async processTestUrl (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:676:31)\n    at async eval (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:209:46)\n    at async Promise.all (index 0)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:276:13)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-27T21:57:49.189Z","level":"ERROR","component":"content-pipeline","message":"English-only enhancement failed completely","context":{"title":"DAX macht 4.000 Punkte im 1. Halbjahr – Bayer, Daimler Truck, Kontron, Mercedes-Benz, Nordex und Sie","url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-daimler-truck-kontron-mercedes-benz-nordex-und-siemens-im-check-20382487.html?feed=directrssfeed","contentLength":55309,"feedLanguage":"de","errorCategory":"conversion_error"},"error":{"name":"Error","message":"All 3 enhancement attempts failed. Last error: Content appears incomplete despite normal token usage (5.2%). Check content quality and prompt instructions. Issues: 🚨 CRITICAL: Content appears truncated - ends with \"positions. The interplay between market,\" instead of proper punctuation, 🚨 CRITICAL: HTML structure incomplete - 15 open tags vs 14 close tags (likely truncated), 🚨 CRITICAL: Content contains unprocessed JSON/structured data - likely parsing truncation","stack":"Error: All 3 enhancement attempts failed. Last error: Content appears incomplete despite normal token usage (5.2%). Check content quality and prompt instructions. Issues: 🚨 CRITICAL: Content appears truncated - ends with \"positions. The interplay between market,\" instead of proper punctuation, 🚨 CRITICAL: HTML structure incomplete - 15 open tags vs 14 close tags (likely truncated), 🚨 CRITICAL: Content contains unprocessed JSON/structured data - likely parsing truncation\n    at createCandidateArticle (webpack-internal:///(rsc)/./src/lib/server/create-candidate.ts:178:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async processTestUrl (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:676:31)\n    at async eval (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:209:46)\n    at async Promise.all (index 1)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:276:13)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-27T22:07:10.039Z","level":"ERROR","component":"content-pipeline","message":"English-only enhancement failed completely","context":{"title":"DAX macht 4.000 Punkte im 1. Halbjahr – Bayer, Daimler Truck, Kontron, Mercedes-Benz, Nordex und Sie","url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-daimler-truck-kontron-mercedes-benz-nordex-und-siemens-im-check-20382487.html?feed=directrssfeed","contentLength":6066,"feedLanguage":"de","errorCategory":"unknown_error"},"error":{"name":"Error","message":"All 3 enhancement attempts failed. Last error: 400 'messages' must contain the word 'json' in some form, to use 'response_format' of type 'json_object'.","stack":"Error: All 3 enhancement attempts failed. Last error: 400 'messages' must contain the word 'json' in some form, to use 'response_format' of type 'json_object'.\n    at createCandidateArticle (webpack-internal:///(rsc)/./src/lib/server/create-candidate.ts:178:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async processTestUrl (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:676:31)\n    at async eval (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:209:46)\n    at async Promise.all (index 1)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:276:13)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-27T22:07:25.992Z","level":"ERROR","component":"content-pipeline","message":"English-only enhancement failed completely","context":{"title":"Test Article","url":"https://www.handelsblatt.com/politik/international/israel-gute-umfragewerte-netanjahu-koennte-neuwahlen-provozieren/100138438.html","contentLength":48,"feedLanguage":"de","errorCategory":"unknown_error"},"error":{"name":"Error","message":"All 3 enhancement attempts failed. Last error: 400 'messages' must contain the word 'json' in some form, to use 'response_format' of type 'json_object'.","stack":"Error: All 3 enhancement attempts failed. Last error: 400 'messages' must contain the word 'json' in some form, to use 'response_format' of type 'json_object'.\n    at createCandidateArticle (webpack-internal:///(rsc)/./src/lib/server/create-candidate.ts:178:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async processTestUrl (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:676:31)\n    at async eval (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:209:46)\n    at async Promise.all (index 0)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:276:13)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-27T22:08:06.100Z","level":"ERROR","component":"content-pipeline","message":"English-only enhancement failed completely","context":{"title":"Tesla-Aktie kräftig im Minus: Erwartetete Absatzflaute und Trumps Kritik belasten Kurs | finanzen.ne","url":"https://www.finanzen.net/nachricht/aktien/zurueckhaltung-tesla-aktie-unter-druck-schwache-verkaufszahlen-und-aerger-mit-trump-belasten-14597049","contentLength":73296,"feedLanguage":"de","errorCategory":"unknown_error"},"error":{"name":"Error","message":"All 3 enhancement attempts failed. Last error: 400 'messages' must contain the word 'json' in some form, to use 'response_format' of type 'json_object'.","stack":"Error: All 3 enhancement attempts failed. Last error: 400 'messages' must contain the word 'json' in some form, to use 'response_format' of type 'json_object'.\n    at createCandidateArticle (webpack-internal:///(rsc)/./src/lib/server/create-candidate.ts:178:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async processTestUrl (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:676:31)\n    at async eval (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:209:46)\n    at async Promise.all (index 0)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:276:13)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-27T22:27:27.514Z","level":"ERROR","component":"content-pipeline","message":"English-only enhancement failed completely","context":{"title":"Tesla-Aktie kräftig im Minus: Erwartetete Absatzflaute und Trumps Kritik belasten Kurs | finanzen.ne","url":"https://www.finanzen.net/nachricht/aktien/zurueckhaltung-tesla-aktie-unter-druck-schwache-verkaufszahlen-und-aerger-mit-trump-belasten-14597049","contentLength":73250,"feedLanguage":"de","errorCategory":"conversion_error"},"error":{"name":"Error","message":"All 3 enhancement attempts failed. Last error: Content appears incomplete despite normal token usage (4.5%). Check content quality and prompt instructions. Issues: 🚨 CRITICAL: Content appears truncated - ends with \"positive outlook on Tesla's long-term\" instead of proper punctuation, 🚨 CRITICAL: HTML structure incomplete - 10 open tags vs 9 close tags (likely truncated)","stack":"Error: All 3 enhancement attempts failed. Last error: Content appears incomplete despite normal token usage (4.5%). Check content quality and prompt instructions. Issues: 🚨 CRITICAL: Content appears truncated - ends with \"positive outlook on Tesla's long-term\" instead of proper punctuation, 🚨 CRITICAL: HTML structure incomplete - 10 open tags vs 9 close tags (likely truncated)\n    at createCandidateArticle (webpack-internal:///(rsc)/./src/lib/server/create-candidate.ts:178:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async processTestUrl (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:669:31)\n    at async eval (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:202:46)\n    at async Promise.all (index 0)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:269:13)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-27T22:32:19.117Z","level":"ERROR","component":"content-pipeline","message":"English-only enhancement failed completely","context":{"title":"DAX macht 4.000 Punkte im 1. Halbjahr – Bayer, Daimler Truck, Kontron, Mercedes-Benz, Nordex und Sie","url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-daimler-truck-kontron-mercedes-benz-nordex-und-siemens-im-check-20382487.html?feed=directrssfeed","contentLength":55309,"feedLanguage":"de","errorCategory":"conversion_error"},"error":{"name":"Error","message":"All 3 enhancement attempts failed. Last error: Content appears incomplete despite normal token usage (5.4%). Check content quality and prompt instructions. Issues: 🚨 CRITICAL: Content appears truncated - ends with \"energy sector, represented by <em\" instead of proper punctuation, 🚨 CRITICAL: HTML structure incomplete - 19 open tags vs 18 close tags (likely truncated)","stack":"Error: All 3 enhancement attempts failed. Last error: Content appears incomplete despite normal token usage (5.4%). Check content quality and prompt instructions. Issues: 🚨 CRITICAL: Content appears truncated - ends with \"energy sector, represented by <em\" instead of proper punctuation, 🚨 CRITICAL: HTML structure incomplete - 19 open tags vs 18 close tags (likely truncated)\n    at createCandidateArticle (webpack-internal:///(rsc)/./src/lib/server/create-candidate.ts:178:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async processTestUrl (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:669:31)\n    at async eval (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:202:46)\n    at async Promise.all (index 1)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-test-pipeline/route.ts:269:13)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
