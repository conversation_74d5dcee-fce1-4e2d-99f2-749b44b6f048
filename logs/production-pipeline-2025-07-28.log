{"timestamp":"2025-07-28T09:58:02.382Z","level":"ERROR","component":"production-pipeline","message":"Failed to create candidate article in production pipeline","context":{"title":"Nach Tour-Erfolg: Rad-<PERSON>: Lange Party-Nacht und ein Paris-Bummel","url":"https://www.stern.de/sport/nach-tour-erfolg--rad-held-lipowitz--lange-party-nacht-und-ein-paris-bummel-35928318.html","feedId":"9","feedName":"Stern DE","contentLength":116189,"pipeline":"production","errorCategory":"unknown_error"},"error":{"name":"Error","message":"Failed to create candidate article: Enhancement returned incomplete data for article \"Nach Tour-Erfolg: Rad-Held Lipowitz: Lange Party-Nacht und ein Paris-Bummel\": success=false, hasContent=false","stack":"Error: Failed to create candidate article: Enhancement returned incomplete data for article \"Nach Tour-Erfolg: Ra<PERSON>-<PERSON>: Lange Party-Nacht und ein Paris-Bummel\": success=false, hasContent=false\n    at createCandidateArticle (webpack-internal:///(rsc)/./src/lib/server/create-candidate.ts:475:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async eval (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:476:47)\n    at async Promise.all (index 0)\n    at async RSSProcessingService.processSingleFeed (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:539:17)\n    at async RSSProcessingService.processFeeds (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:145:40)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-single-pipeline/route.ts:173:34)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-28T10:06:47.451Z","level":"ERROR","component":"production-pipeline","message":"Failed to create candidate article in production pipeline","context":{"title":"Der neue Online Broker - Smartbroker","url":"https://smartbroker.de?utm_source=wallstreetonline&utm_medium=referral&utm_campaign=intern&utm_content=rss_feed","feedId":"2","feedName":"Wallstreet Online","contentLength":39569404,"pipeline":"production","errorCategory":"unknown_error"},"error":{"name":"Error","message":"Failed to create candidate article: Enhancement returned incomplete data for article \"Der neue Online Broker - Smartbroker\": success=false, hasContent=false","stack":"Error: Failed to create candidate article: Enhancement returned incomplete data for article \"Der neue Online Broker - Smartbroker\": success=false, hasContent=false\n    at createCandidateArticle (webpack-internal:///(rsc)/./src/lib/server/create-candidate.ts:475:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async eval (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:476:47)\n    at async Promise.all (index 0)\n    at async RSSProcessingService.processSingleFeed (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:539:17)\n    at async RSSProcessingService.processFeeds (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:145:40)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-single-pipeline/route.ts:173:34)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-28T11:54:53.965Z","level":"ERROR","component":"production-pipeline","message":"Failed to create candidate article in production pipeline","context":{"title":"ETF : Bis zu 112 Prozent Rendite – Das sind die besten ausschüttenden Fonds","url":"https://www.handelsblatt.com/finanzen/anlagestrategie/fonds-etf/geldanlage-diese-vier-guenstigen-etfs-schuetten-eine-hohe-dividende-aus-01/100134070.html","feedId":"1","feedName":"Handelsblatt","contentLength":297417,"pipeline":"production","errorCategory":"unknown_error"},"error":{"name":"Error","message":"Failed to create candidate article: TECHNICAL_ERROR: Enhancement system failed to process article \"ETF : Bis zu 112 Prozent Rendite – Das sind die besten aussc...\". This may indicate an AI processing issue or content format problem.","stack":"Error: Failed to create candidate article: TECHNICAL_ERROR: Enhancement system failed to process article \"ETF : Bis zu 112 Prozent Rendite – Das sind die besten aussc...\". This may indicate an AI processing issue or content format problem.\n    at createCandidateArticle (webpack-internal:///(rsc)/./src/lib/server/create-candidate.ts:585:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async eval (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:509:47)\n    at async Promise.all (index 0)\n    at async RSSProcessingService.processSingleFeed (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:621:17)\n    at async RSSProcessingService.processFeeds (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:145:40)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-production-pipeline/route.ts:102:34)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-28T11:54:53.965Z","level":"ERROR","component":"production-pipeline","message":"Failed to create candidate article in production pipeline","context":{"title":"Geldanlage  : Ergänzungen zum MSCI World: Vier ETFs mit niedrigem US-Anteil, die besser performen al","url":"https://www.handelsblatt.com/finanzen/anlagestrategie/fonds-etf/geldanlage-mit-diesen-etfs-verringern-sie-den-us-anteil-in-ihrem-depot/100131157.html","feedId":"1","feedName":"Handelsblatt","contentLength":111484,"pipeline":"production","errorCategory":"unknown_error"},"error":{"name":"Error","message":"Failed to create candidate article: TECHNICAL_ERROR: Enhancement system failed to process article \"Geldanlage  : Ergänzungen zum MSCI World: Vier ETFs mit nied...\". This may indicate an AI processing issue or content format problem.","stack":"Error: Failed to create candidate article: TECHNICAL_ERROR: Enhancement system failed to process article \"Geldanlage  : Ergänzungen zum MSCI World: Vier ETFs mit nied...\". This may indicate an AI processing issue or content format problem.\n    at createCandidateArticle (webpack-internal:///(rsc)/./src/lib/server/create-candidate.ts:585:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async eval (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:509:47)\n    at async Promise.all (index 1)\n    at async RSSProcessingService.processSingleFeed (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:621:17)\n    at async RSSProcessingService.processFeeds (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:145:40)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-production-pipeline/route.ts:102:34)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-28T11:56:48.898Z","level":"ERROR","component":"production-pipeline","message":"Failed to create candidate article in production pipeline","context":{"title":"Energie : Das sind die schönsten Wärmepumpen","url":"https://www.handelsblatt.com/unternehmen/energie/energie-diese-waermepumpen-ueberzeugen-durch-ein-innovatives-design/100029817.html","feedId":"1","feedName":"Handelsblatt","contentLength":153563,"pipeline":"production","errorCategory":"unknown_error"},"error":{"name":"Error","message":"Failed to create candidate article: TECHNICAL_ERROR: Enhancement system failed to process article \"Energie : Das sind die schönsten Wärmepumpen...\". This may indicate an AI processing issue or content format problem.","stack":"Error: Failed to create candidate article: TECHNICAL_ERROR: Enhancement system failed to process article \"Energie : Das sind die schönsten Wärmepumpen...\". This may indicate an AI processing issue or content format problem.\n    at createCandidateArticle (webpack-internal:///(rsc)/./src/lib/server/create-candidate.ts:585:15)\n    at async eval (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:509:47)\n    at async Promise.all (index 0)\n    at async RSSProcessingService.processSingleFeed (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:621:17)\n    at async RSSProcessingService.processFeeds (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:145:40)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-production-pipeline/route.ts:102:34)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-28T12:19:48.137Z","level":"ERROR","component":"production-pipeline","message":"Failed to create candidate article in production pipeline","context":{"title":"Zölle USA/EU: Schmitt: Zolldeal weit hinter Interessen der EU- Wirtschaft","url":"https://www.stern.de/gesellschaft/regional/rheinland-pfalz-saarland/zoelle-usa-eu--schmitt--zolldeal-weit-hinter-interessen-der-eu--wirtschaft-35929372.html","feedId":"9","feedName":"Stern DE","contentLength":103337,"pipeline":"production","errorCategory":"unknown_error"},"error":{"name":"Error","message":"Failed to create candidate article: TECHNICAL_ERROR: Enhancement system failed to process article \"Zölle USA/EU: Schmitt: Zolldeal weit hinter Interessen der E...\". This may indicate an AI processing issue or content format problem.","stack":"Error: Failed to create candidate article: TECHNICAL_ERROR: Enhancement system failed to process article \"Zölle USA/EU: Schmitt: Zolldeal weit hinter Interessen der E...\". This may indicate an AI processing issue or content format problem.\n    at createCandidateArticle (webpack-internal:///(rsc)/./src/lib/server/create-candidate.ts:585:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async eval (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:509:47)\n    at async Promise.all (index 0)\n    at async RSSProcessingService.processSingleFeed (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:621:17)\n    at async RSSProcessingService.processFeeds (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:145:40)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-production-pipeline/route.ts:102:34)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-28T12:26:23.020Z","level":"ERROR","component":"production-pipeline","message":"Failed to create candidate article in production pipeline","context":{"title":"Nach dem Prognose-Schock: Zoll-Rallye: Evotec-Aktien legen kräftig zu!","url":"https://www.wallstreet-online.de/nachricht/19683148-prognose-schock-zoll-rallye-evotec-aktien-kraeftig-zu","feedId":"2","feedName":"Wallstreet Online","contentLength":121337,"pipeline":"production","errorCategory":"unknown_error"},"error":{"name":"Error","message":"Failed to create candidate article: TECHNICAL_ERROR: Enhancement system failed to process article \"Nach dem Prognose-Schock: Zoll-Rallye: Evotec-Aktien legen k...\". This may indicate an AI processing issue or content format problem.","stack":"Error: Failed to create candidate article: TECHNICAL_ERROR: Enhancement system failed to process article \"Nach dem Prognose-Schock: Zoll-Rallye: Evotec-Aktien legen k...\". This may indicate an AI processing issue or content format problem.\n    at createCandidateArticle (webpack-internal:///(rsc)/./src/lib/server/create-candidate.ts:585:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async eval (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:509:47)\n    at async Promise.all (index 1)\n    at async RSSProcessingService.processSingleFeed (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:621:17)\n    at async RSSProcessingService.processFeeds (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:145:40)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-production-pipeline/route.ts:102:34)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-28T12:26:23.019Z","level":"ERROR","component":"production-pipeline","message":"Failed to create candidate article in production pipeline","context":{"title":"Der neue Online Broker - Smartbroker","url":"https://smartbroker.de?utm_source=wallstreetonline&utm_medium=referral&utm_campaign=intern&utm_content=rss_feed","feedId":"2","feedName":"Wallstreet Online","contentLength":39569009,"pipeline":"production","errorCategory":"unknown_error"},"error":{"name":"Error","message":"Failed to create candidate article: TECHNICAL_ERROR: Enhancement system failed to process article \"Der neue Online Broker - Smartbroker...\". This may indicate an AI processing issue or content format problem.","stack":"Error: Failed to create candidate article: TECHNICAL_ERROR: Enhancement system failed to process article \"Der neue Online Broker - Smartbroker...\". This may indicate an AI processing issue or content format problem.\n    at createCandidateArticle (webpack-internal:///(rsc)/./src/lib/server/create-candidate.ts:585:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async eval (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:509:47)\n    at async Promise.all (index 0)\n    at async RSSProcessingService.processSingleFeed (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:621:17)\n    at async RSSProcessingService.processFeeds (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:145:40)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-production-pipeline/route.ts:102:34)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-28T12:26:25.330Z","level":"ERROR","component":"production-pipeline","message":"Failed to create candidate article in production pipeline","context":{"title":"Kernenergie ist zurück: Diese Atom-Aktien gehen gerade durch die Decke","url":"https://www.wallstreet-online.de/nachricht/19683322-kernenergie-zurueck-atom-aktien-gerade-decke","feedId":"2","feedName":"Wallstreet Online","contentLength":117235,"pipeline":"production","errorCategory":"unknown_error"},"error":{"name":"Error","message":"Failed to create candidate article: TECHNICAL_ERROR: Enhancement system failed to process article \"Kernenergie ist zurück: Diese Atom-Aktien gehen gerade durch...\". This may indicate an AI processing issue or content format problem.","stack":"Error: Failed to create candidate article: TECHNICAL_ERROR: Enhancement system failed to process article \"Kernenergie ist zurück: Diese Atom-Aktien gehen gerade durch...\". This may indicate an AI processing issue or content format problem.\n    at createCandidateArticle (webpack-internal:///(rsc)/./src/lib/server/create-candidate.ts:585:15)\n    at async eval (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:509:47)\n    at async Promise.all (index 1)\n    at async RSSProcessingService.processSingleFeed (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:621:17)\n    at async RSSProcessingService.processFeeds (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:145:40)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-production-pipeline/route.ts:102:34)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-28T12:27:00.640Z","level":"ERROR","component":"production-pipeline","message":"Failed to create candidate article in production pipeline","context":{"title":"*************** US-Dollar: Hier bahnt sich die (vielleicht) größte Fusion aller Zeiten an!","url":"https://www.wallstreet-online.de/nachricht/19681807-197-000-000-000-us-dollar-bahnt-vielleicht-groesste-fusion-zeiten-an","feedId":"2","feedName":"Wallstreet Online","contentLength":177961,"pipeline":"production","errorCategory":"unknown_error"},"error":{"name":"Error","message":"Failed to create candidate article: TECHNICAL_ERROR: Enhancement system failed to process article \"*************** US-Dollar: Hier bahnt sich die (vielleicht) ...\". This may indicate an AI processing issue or content format problem.","stack":"Error: Failed to create candidate article: TECHNICAL_ERROR: Enhancement system failed to process article \"*************** US-Dollar: Hier bahnt sich die (vielleicht) ...\". This may indicate an AI processing issue or content format problem.\n    at createCandidateArticle (webpack-internal:///(rsc)/./src/lib/server/create-candidate.ts:585:15)\n    at async eval (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:509:47)\n    at async Promise.all (index 0)\n    at async RSSProcessingService.processSingleFeed (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:621:17)\n    at async RSSProcessingService.processFeeds (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:145:40)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-production-pipeline/route.ts:102:34)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-28T12:27:00.641Z","level":"ERROR","component":"production-pipeline","message":"Failed to create candidate article in production pipeline","context":{"title":"Gewinnwarnung voraus?: Porsche: Schlechte Nachrichten mit Ansage? - Prognosekürzung am Mittwoch?","url":"https://www.wallstreet-online.de/nachricht/19681510-gewinnwarnung-voraus-porsche-schlechte-nachrichten-ansage-prognosekuerzung-mittwoch","feedId":"2","feedName":"Wallstreet Online","contentLength":107101,"pipeline":"production","errorCategory":"unknown_error"},"error":{"name":"Error","message":"Failed to create candidate article: TECHNICAL_ERROR: Enhancement system failed to process article \"Gewinnwarnung voraus?: Porsche: Schlechte Nachrichten mit An...\". This may indicate an AI processing issue or content format problem.","stack":"Error: Failed to create candidate article: TECHNICAL_ERROR: Enhancement system failed to process article \"Gewinnwarnung voraus?: Porsche: Schlechte Nachrichten mit An...\". This may indicate an AI processing issue or content format problem.\n    at createCandidateArticle (webpack-internal:///(rsc)/./src/lib/server/create-candidate.ts:585:15)\n    at async eval (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:509:47)\n    at async Promise.all (index 1)\n    at async RSSProcessingService.processSingleFeed (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:621:17)\n    at async RSSProcessingService.processFeeds (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:145:40)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-production-pipeline/route.ts:102:34)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-28T12:27:02.890Z","level":"ERROR","component":"production-pipeline","message":"Failed to create candidate article in production pipeline","context":{"title":"Passives Einkommen: Spark bietet 10-Prozent-Dividende – ganz ohne Quellensteuer!","url":"https://www.wallstreet-online.de/nachricht/19681876-passives-einkommen-spark-10-prozent-dividende-quellensteuer","feedId":"2","feedName":"Wallstreet Online","contentLength":125320,"pipeline":"production","errorCategory":"unknown_error"},"error":{"name":"Error","message":"Failed to create candidate article: TECHNICAL_ERROR: Enhancement system failed to process article \"Passives Einkommen: Spark bietet 10-Prozent-Dividende – ganz...\". This may indicate an AI processing issue or content format problem.","stack":"Error: Failed to create candidate article: TECHNICAL_ERROR: Enhancement system failed to process article \"Passives Einkommen: Spark bietet 10-Prozent-Dividende – ganz...\". This may indicate an AI processing issue or content format problem.\n    at createCandidateArticle (webpack-internal:///(rsc)/./src/lib/server/create-candidate.ts:585:15)\n    at async eval (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:509:47)\n    at async Promise.all (index 0)\n    at async RSSProcessingService.processSingleFeed (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:621:17)\n    at async RSSProcessingService.processFeeds (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:145:40)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-production-pipeline/route.ts:102:34)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-28T12:27:24.499Z","level":"ERROR","component":"production-pipeline","message":"Failed to create candidate article in production pipeline","context":{"title":"„Neuer KI-Superzyklus“: 4 KI-Wachstumsaktien, die Multimilliardär Laffont für 731.750.000 USD neu ge","url":"https://www.wallstreet-online.de/nachricht/19675458-neuer-ki-superzyklus-4-ki-wachstumsaktien-multimilliardaer-laffont-731-750-000-usd-gekauft","feedId":"2","feedName":"Wallstreet Online","contentLength":229518,"pipeline":"production","errorCategory":"unknown_error"},"error":{"name":"Error","message":"Failed to create candidate article: TECHNICAL_ERROR: Enhancement system failed to process article \"„Neuer KI-Superzyklus“: 4 KI-Wachstumsaktien, die Multimilli...\". This may indicate an AI processing issue or content format problem.","stack":"Error: Failed to create candidate article: TECHNICAL_ERROR: Enhancement system failed to process article \"„Neuer KI-Superzyklus“: 4 KI-Wachstumsaktien, die Multimilli...\". This may indicate an AI processing issue or content format problem.\n    at createCandidateArticle (webpack-internal:///(rsc)/./src/lib/server/create-candidate.ts:585:15)\n    at async eval (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:509:47)\n    at async Promise.all (index 0)\n    at async RSSProcessingService.processSingleFeed (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:621:17)\n    at async RSSProcessingService.processFeeds (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:145:40)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-production-pipeline/route.ts:102:34)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-28T12:28:15.519Z","level":"ERROR","component":"production-pipeline","message":"Failed to create candidate article in production pipeline","context":{"title":"Kurseinbruch als Kaufchance?: Im SDAX versteckt: Das ist eine der erfolgreichsten deutschen Wachstum","url":"https://www.wallstreet-online.de/nachricht/19681302-kurseinbruch-kaufchance-sdax-versteckt-erfolgreichsten-deutschen-wachstumsaktien","feedId":"2","feedName":"Wallstreet Online","contentLength":119325,"pipeline":"production","errorCategory":"unknown_error"},"error":{"name":"Error","message":"Failed to create candidate article: TECHNICAL_ERROR: Enhancement system failed to process article \"Kurseinbruch als Kaufchance?: Im SDAX versteckt: Das ist ein...\". This may indicate an AI processing issue or content format problem.","stack":"Error: Failed to create candidate article: TECHNICAL_ERROR: Enhancement system failed to process article \"Kurseinbruch als Kaufchance?: Im SDAX versteckt: Das ist ein...\". This may indicate an AI processing issue or content format problem.\n    at createCandidateArticle (webpack-internal:///(rsc)/./src/lib/server/create-candidate.ts:585:15)\n    at async eval (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:509:47)\n    at async Promise.all (index 0)\n    at async RSSProcessingService.processSingleFeed (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:621:17)\n    at async RSSProcessingService.processFeeds (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:145:40)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-production-pipeline/route.ts:102:34)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-28T12:31:17.232Z","level":"ERROR","component":"production-pipeline","message":"Failed to create candidate article in production pipeline","context":{"title":"US-Zölle reißen Milliardenloch: Puma-Aktie crasht nach Horror-Prognose","url":"https://www.wallstreet-online.de/nachricht/19677165-us-zoelle-reissen-milliardenloch-puma-aktie-crasht-horror-prognose","feedId":"2","feedName":"Wallstreet Online","contentLength":117520,"pipeline":"production","errorCategory":"unknown_error"},"error":{"name":"Error","message":"Failed to create candidate article: TECHNICAL_ERROR: Enhancement system failed to process article \"US-Zölle reißen Milliardenloch: Puma-Aktie crasht nach Horro...\". This may indicate an AI processing issue or content format problem.","stack":"Error: Failed to create candidate article: TECHNICAL_ERROR: Enhancement system failed to process article \"US-Zölle reißen Milliardenloch: Puma-Aktie crasht nach Horro...\". This may indicate an AI processing issue or content format problem.\n    at createCandidateArticle (webpack-internal:///(rsc)/./src/lib/server/create-candidate.ts:585:15)\n    at async eval (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:509:47)\n    at async Promise.all (index 1)\n    at async RSSProcessingService.processSingleFeed (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:621:17)\n    at async RSSProcessingService.processFeeds (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:145:40)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-production-pipeline/route.ts:102:34)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-28T12:31:17.232Z","level":"ERROR","component":"production-pipeline","message":"Failed to create candidate article in production pipeline","context":{"title":"Albemarle & Co: Rallye bei Lithium-Aktien läuft auf Hochtouren – noch","url":"https://www.wallstreet-online.de/nachricht/19677405-albemarle-co-rallye-lithium-aktien-laeuft-hochtouren","feedId":"2","feedName":"Wallstreet Online","contentLength":111155,"pipeline":"production","errorCategory":"unknown_error"},"error":{"name":"Error","message":"Failed to create candidate article: TECHNICAL_ERROR: Enhancement system failed to process article \"Albemarle & Co: Rallye bei Lithium-Aktien läuft auf Hochtour...\". This may indicate an AI processing issue or content format problem.","stack":"Error: Failed to create candidate article: TECHNICAL_ERROR: Enhancement system failed to process article \"Albemarle & Co: Rallye bei Lithium-Aktien läuft auf Hochtour...\". This may indicate an AI processing issue or content format problem.\n    at createCandidateArticle (webpack-internal:///(rsc)/./src/lib/server/create-candidate.ts:585:15)\n    at async eval (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:509:47)\n    at async Promise.all (index 0)\n    at async RSSProcessingService.processSingleFeed (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:621:17)\n    at async RSSProcessingService.processFeeds (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:145:40)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-production-pipeline/route.ts:102:34)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-28T12:31:19.495Z","level":"ERROR","component":"production-pipeline","message":"Failed to create candidate article in production pipeline","context":{"title":"Aktie zieht nachbörslich an: Newmont im Goldrausch: Der heimliche Gewinner der Edelmetall-Rallye","url":"https://www.wallstreet-online.de/nachricht/19676415-aktie-nachboerslich-an-newmont-goldrausch-heimliche-gewinner-edelmetall-rallye","feedId":"2","feedName":"Wallstreet Online","contentLength":152144,"pipeline":"production","errorCategory":"unknown_error"},"error":{"name":"Error","message":"Failed to create candidate article: TECHNICAL_ERROR: Enhancement system failed to process article \"Aktie zieht nachbörslich an: Newmont im Goldrausch: Der heim...\". This may indicate an AI processing issue or content format problem.","stack":"Error: Failed to create candidate article: TECHNICAL_ERROR: Enhancement system failed to process article \"Aktie zieht nachbörslich an: Newmont im Goldrausch: Der heim...\". This may indicate an AI processing issue or content format problem.\n    at createCandidateArticle (webpack-internal:///(rsc)/./src/lib/server/create-candidate.ts:585:15)\n    at async eval (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:509:47)\n    at async Promise.all (index 1)\n    at async RSSProcessingService.processSingleFeed (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:621:17)\n    at async RSSProcessingService.processFeeds (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:145:40)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-production-pipeline/route.ts:102:34)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-28T12:31:38.134Z","level":"ERROR","component":"production-pipeline","message":"Failed to create candidate article in production pipeline","context":{"title":"Kursexplosion am Donnerstag: Bloom Energy: Das lässt die Wasserstoff-Aktie jetzt durch die Decke geh","url":"https://www.wallstreet-online.de/nachricht/19674408-kursexplosion-donnerstag-bloom-energy-laesst-wasserstoff-aktie-decke-gehen","feedId":"2","feedName":"Wallstreet Online","contentLength":115163,"pipeline":"production","errorCategory":"unknown_error"},"error":{"name":"Error","message":"Failed to create candidate article: TECHNICAL_ERROR: Enhancement system failed to process article \"Kursexplosion am Donnerstag: Bloom Energy: Das lässt die Was...\". This may indicate an AI processing issue or content format problem.","stack":"Error: Failed to create candidate article: TECHNICAL_ERROR: Enhancement system failed to process article \"Kursexplosion am Donnerstag: Bloom Energy: Das lässt die Was...\". This may indicate an AI processing issue or content format problem.\n    at createCandidateArticle (webpack-internal:///(rsc)/./src/lib/server/create-candidate.ts:585:15)\n    at async eval (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:509:47)\n    at async Promise.all (index 1)\n    at async RSSProcessingService.processSingleFeed (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:621:17)\n    at async RSSProcessingService.processFeeds (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:145:40)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-production-pipeline/route.ts:102:34)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-28T12:32:22.469Z","level":"ERROR","component":"production-pipeline","message":"Failed to create candidate article in production pipeline","context":{"title":"Norwegens Cash-Cow: Peak Oil erreicht? Das steckt hinter dem Downgrade für Dividendenwert Equinor","url":"https://www.wallstreet-online.de/nachricht/19674636-norwegens-cash-cow-peak-oil-erreicht-steckt-downgrade-dividendenwert-equinor","feedId":"2","feedName":"Wallstreet Online","contentLength":117624,"pipeline":"production","errorCategory":"unknown_error"},"error":{"name":"Error","message":"Failed to create candidate article: TECHNICAL_ERROR: Enhancement system failed to process article \"Norwegens Cash-Cow: Peak Oil erreicht? Das steckt hinter dem...\". This may indicate an AI processing issue or content format problem.","stack":"Error: Failed to create candidate article: TECHNICAL_ERROR: Enhancement system failed to process article \"Norwegens Cash-Cow: Peak Oil erreicht? Das steckt hinter dem...\". This may indicate an AI processing issue or content format problem.\n    at createCandidateArticle (webpack-internal:///(rsc)/./src/lib/server/create-candidate.ts:585:15)\n    at async eval (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:509:47)\n    at async Promise.all (index 1)\n    at async RSSProcessingService.processSingleFeed (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:621:17)\n    at async RSSProcessingService.processFeeds (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:145:40)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-production-pipeline/route.ts:102:34)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-28T12:32:45.696Z","level":"ERROR","component":"production-pipeline","message":"Failed to create candidate article in production pipeline","context":{"title":"Schockierende Dividenden-News: Das könnte jetzt Ärger auch für BASF geben!","url":"https://www.wallstreet-online.de/nachricht/19672602-schockierende-dividenden-news-aerger-basf-geben","feedId":"2","feedName":"Wallstreet Online","contentLength":115530,"pipeline":"production","errorCategory":"unknown_error"},"error":{"name":"Error","message":"Failed to create candidate article: TECHNICAL_ERROR: Enhancement system failed to process article \"Schockierende Dividenden-News: Das könnte jetzt Ärger auch f...\". This may indicate an AI processing issue or content format problem.","stack":"Error: Failed to create candidate article: TECHNICAL_ERROR: Enhancement system failed to process article \"Schockierende Dividenden-News: Das könnte jetzt Ärger auch f...\". This may indicate an AI processing issue or content format problem.\n    at createCandidateArticle (webpack-internal:///(rsc)/./src/lib/server/create-candidate.ts:585:15)\n    at async eval (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:509:47)\n    at async Promise.all (index 1)\n    at async RSSProcessingService.processSingleFeed (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:621:17)\n    at async RSSProcessingService.processFeeds (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:145:40)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-production-pipeline/route.ts:102:34)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-28T12:32:45.696Z","level":"ERROR","component":"production-pipeline","message":"Failed to create candidate article in production pipeline","context":{"title":"Börse, Baby!: Goldrallye? Für diese Minenaktie lief es in diesem Jahr noch besser!","url":"https://www.wallstreet-online.de/nachricht/19674066-boerse-baby-goldrallye-minenaktie-lief-jahr-besser","feedId":"2","feedName":"Wallstreet Online","contentLength":152362,"pipeline":"production","errorCategory":"unknown_error"},"error":{"name":"Error","message":"Failed to create candidate article: TECHNICAL_ERROR: Enhancement system failed to process article \"Börse, Baby!: Goldrallye? Für diese Minenaktie lief es in di...\". This may indicate an AI processing issue or content format problem.","stack":"Error: Failed to create candidate article: TECHNICAL_ERROR: Enhancement system failed to process article \"Börse, Baby!: Goldrallye? Für diese Minenaktie lief es in di...\". This may indicate an AI processing issue or content format problem.\n    at createCandidateArticle (webpack-internal:///(rsc)/./src/lib/server/create-candidate.ts:585:15)\n    at async eval (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:509:47)\n    at async Promise.all (index 0)\n    at async RSSProcessingService.processSingleFeed (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:621:17)\n    at async RSSProcessingService.processFeeds (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:145:40)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-production-pipeline/route.ts:102:34)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-28T12:33:41.892Z","level":"ERROR","component":"production-pipeline","message":"Failed to create candidate article in production pipeline","context":{"title":"SDAX-Top-Performer: Vossloh &quot;hält, was es verspricht!&quot; Diese Zahlen können sich sehen lass","url":"https://www.wallstreet-online.de/nachricht/19671591-sdax-top-performer-vossloh-haelt-verspricht-zahlen","feedId":"2","feedName":"Wallstreet Online","contentLength":117277,"pipeline":"production","errorCategory":"unknown_error"},"error":{"name":"Error","message":"Failed to create candidate article: TECHNICAL_ERROR: Enhancement system failed to process article \"SDAX-Top-Performer: Vossloh &quot;hält, was es verspricht!&q...\". This may indicate an AI processing issue or content format problem.","stack":"Error: Failed to create candidate article: TECHNICAL_ERROR: Enhancement system failed to process article \"SDAX-Top-Performer: Vossloh &quot;hält, was es verspricht!&q...\". This may indicate an AI processing issue or content format problem.\n    at createCandidateArticle (webpack-internal:///(rsc)/./src/lib/server/create-candidate.ts:585:15)\n    at async eval (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:509:47)\n    at async Promise.all (index 1)\n    at async RSSProcessingService.processSingleFeed (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:621:17)\n    at async RSSProcessingService.processFeeds (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:145:40)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-production-pipeline/route.ts:102:34)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-28T12:33:41.892Z","level":"ERROR","component":"production-pipeline","message":"Failed to create candidate article in production pipeline","context":{"title":"Kursziel angehoben: Danske Bank: Hier winkt eine Neubewertung und noch mehr Dividende","url":"https://www.wallstreet-online.de/nachricht/********-kursziel-angehoben-danske-bank-winkt-neubewertung-dividende","feedId":"2","feedName":"Wallstreet Online","contentLength":117178,"pipeline":"production","errorCategory":"unknown_error"},"error":{"name":"Error","message":"Failed to create candidate article: TECHNICAL_ERROR: Enhancement system failed to process article \"Kursziel angehoben: Danske Bank: Hier winkt eine Neubewertun...\". This may indicate an AI processing issue or content format problem.","stack":"Error: Failed to create candidate article: TECHNICAL_ERROR: Enhancement system failed to process article \"Kursziel angehoben: Danske Bank: Hier winkt eine Neubewertun...\". This may indicate an AI processing issue or content format problem.\n    at createCandidateArticle (webpack-internal:///(rsc)/./src/lib/server/create-candidate.ts:585:15)\n    at async eval (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:509:47)\n    at async Promise.all (index 0)\n    at async RSSProcessingService.processSingleFeed (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:621:17)\n    at async RSSProcessingService.processFeeds (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:145:40)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-production-pipeline/route.ts:102:34)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-28T12:33:44.345Z","level":"ERROR","component":"production-pipeline","message":"Failed to create candidate article in production pipeline","context":{"title":"Wenn sich zwei streiten ......: Ist Amazon der lachende Dritte beim Streit zwischen Donald Trump und","url":"https://www.wallstreet-online.de/nachricht/19668393-streiten-amazon-lachende-streit-donald-trump-elon-musk","feedId":"2","feedName":"Wallstreet Online","contentLength":106612,"pipeline":"production","errorCategory":"unknown_error"},"error":{"name":"Error","message":"Failed to create candidate article: TECHNICAL_ERROR: Enhancement system failed to process article \"Wenn sich zwei streiten ......: Ist Amazon der lachende Drit...\". This may indicate an AI processing issue or content format problem.","stack":"Error: Failed to create candidate article: TECHNICAL_ERROR: Enhancement system failed to process article \"Wenn sich zwei streiten ......: Ist Amazon der lachende Drit...\". This may indicate an AI processing issue or content format problem.\n    at createCandidateArticle (webpack-internal:///(rsc)/./src/lib/server/create-candidate.ts:585:15)\n    at async eval (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:509:47)\n    at async Promise.all (index 1)\n    at async RSSProcessingService.processSingleFeed (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:621:17)\n    at async RSSProcessingService.processFeeds (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:145:40)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-production-pipeline/route.ts:102:34)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-28T12:33:44.345Z","level":"ERROR","component":"production-pipeline","message":"Failed to create candidate article in production pipeline","context":{"title":"Kurs schnellt hoch: MTU Aero Engines: Starkes Wachstum und optimistische Aussichten","url":"https://www.wallstreet-online.de/nachricht/19671363-kurs-schnellt-hoch-mtu-aero-engines-starkes-wachstum-optimistische-aussichten","feedId":"2","feedName":"Wallstreet Online","contentLength":120617,"pipeline":"production","errorCategory":"unknown_error"},"error":{"name":"Error","message":"Failed to create candidate article: TECHNICAL_ERROR: Enhancement system failed to process article \"Kurs schnellt hoch: MTU Aero Engines: Starkes Wachstum und o...\". This may indicate an AI processing issue or content format problem.","stack":"Error: Failed to create candidate article: TECHNICAL_ERROR: Enhancement system failed to process article \"Kurs schnellt hoch: MTU Aero Engines: Starkes Wachstum und o...\". This may indicate an AI processing issue or content format problem.\n    at createCandidateArticle (webpack-internal:///(rsc)/./src/lib/server/create-candidate.ts:585:15)\n    at async eval (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:509:47)\n    at async Promise.all (index 0)\n    at async RSSProcessingService.processSingleFeed (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:621:17)\n    at async RSSProcessingService.processFeeds (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:145:40)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-production-pipeline/route.ts:102:34)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-28T12:33:46.593Z","level":"ERROR","component":"production-pipeline","message":"Failed to create candidate article in production pipeline","context":{"title":"Nach übertroffenen Prognosen: Alphabet dreht ins Plus – Zahlen überzeugen nach anfänglicher Skepsis ","url":"https://www.wallstreet-online.de/nachricht/19669395-uebertroffenen-prognosen-alphabet-dreht-plus-zahlen-ueberzeugen-anfaenglicher-skepsis-doch","feedId":"2","feedName":"Wallstreet Online","contentLength":121459,"pipeline":"production","errorCategory":"unknown_error"},"error":{"name":"Error","message":"Failed to create candidate article: TECHNICAL_ERROR: Enhancement system failed to process article \"Nach übertroffenen Prognosen: Alphabet dreht ins Plus – Zahl...\". This may indicate an AI processing issue or content format problem.","stack":"Error: Failed to create candidate article: TECHNICAL_ERROR: Enhancement system failed to process article \"Nach übertroffenen Prognosen: Alphabet dreht ins Plus – Zahl...\". This may indicate an AI processing issue or content format problem.\n    at createCandidateArticle (webpack-internal:///(rsc)/./src/lib/server/create-candidate.ts:585:15)\n    at async eval (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:509:47)\n    at async Promise.all (index 1)\n    at async RSSProcessingService.processSingleFeed (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:621:17)\n    at async RSSProcessingService.processFeeds (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:145:40)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-production-pipeline/route.ts:102:34)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-28T12:34:55.562Z","level":"ERROR","component":"production-pipeline","message":"Failed to create candidate article in production pipeline","context":{"title":"Lamb Weston: Eine Value-Qualitäts-Dividendenaktie mit 3,1 % Rendite im Warren-Buffett-Style","url":"https://www.wallstreet-online.de/nachricht/19666479-lamb-weston-value-qualitaets-dividendenaktie-3-1-rendite-warren-buffett-style","feedId":"2","feedName":"Wallstreet Online","contentLength":101237,"pipeline":"production","errorCategory":"unknown_error"},"error":{"name":"Error","message":"Failed to create candidate article: TECHNICAL_ERROR: Enhancement system failed to process article \"Lamb Weston: Eine Value-Qualitäts-Dividendenaktie mit 3,1 % ...\". This may indicate an AI processing issue or content format problem.","stack":"Error: Failed to create candidate article: TECHNICAL_ERROR: Enhancement system failed to process article \"Lamb Weston: Eine Value-Qualitäts-Dividendenaktie mit 3,1 % ...\". This may indicate an AI processing issue or content format problem.\n    at createCandidateArticle (webpack-internal:///(rsc)/./src/lib/server/create-candidate.ts:585:15)\n    at async eval (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:509:47)\n    at async Promise.all (index 1)\n    at async RSSProcessingService.processSingleFeed (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:621:17)\n    at async RSSProcessingService.processFeeds (webpack-internal:///(rsc)/./src/utilities/RSSProcessingService.ts:145:40)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/run-production-pipeline/route.ts:102:34)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
