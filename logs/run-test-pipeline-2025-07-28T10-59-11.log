=== PIPELINE RUN: RUN-TEST-PIPELINE ===
Run ID: 2025-07-28T10-59-11
Start Time: 2025-07-28T10:59:11.203Z
Log File: run-test-pipeline-2025-07-28T10-59-11.log
===================================================
[2025-07-28T10:59:11.217Z] INFO  ✅ Database validation passed:
[2025-07-28T10:59:11.217Z] INFO     - Active keywords: 20
[2025-07-28T10:59:11.217Z] INFO       Keywords: ETF, Investition, Prognose, Wall Street, Dividende, Wachstumsaktien, Small Cap, Fusion, Börsengang, Aktien...
[2025-07-28T10:59:11.217Z] INFO     - Categories: 4
[2025-07-28T10:59:11.217Z] INFO  
🔍 Getting test RSS feed ID...
[2025-07-28T10:59:11.236Z] INFO  ✅ Using RSS feed for testing: DW (ID: 10)
[2025-07-28T10:59:11.237Z] INFO  
🔄 Processing predefined URLs with parallel processing...
[2025-07-28T10:59:11.237Z] INFO  🚀 Using OpenAI-safe parallel processing (2 concurrent URLs)...
[2025-07-28T10:59:11.237Z] INFO  
📦 Processing batch 1/1: 2 URLs
[2025-07-28T10:59:11.237Z] INFO  📰 [1/2] Processing: https://www.finanzen.net/nachricht/aktien/zurueckhaltung-tesla-aktie-unter-druck...
[2025-07-28T10:59:11.237Z] INFO  📰 [2/2] Processing: https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-i...
[2025-07-28T10:59:11.280Z] INFO        🔍 Keyword check result: matched - proceeding anyway for testing
[2025-07-28T10:59:11.281Z] INFO  🚀 Enhanced extraction starting for: https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-daimler-truck-kontron-mercedes-benz-nordex-und-siemens-im-check-20382487.html?feed=directrssfeed
[2025-07-28T10:59:11.281Z] INFO  📋 Using site configuration for: Der Aktionär
[2025-07-28T10:59:11.281Z] INFO  🎯 Known German financial site: Der Aktionär
[2025-07-28T10:59:11.280Z] INFO        🔍 Extracting content with enhanced client...
[2025-07-28T10:59:11.281Z] INFO  🎯 Selected strategy: Structured Data Enhanced
[2025-07-28T10:59:11.281Z] INFO  🎯 Attempting Structured Data Enhanced extraction for: https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-daimler-truck-kontron-mercedes-benz-nordex-und-siemens-im-check-20382487.html?feed=directrssfeed
[2025-07-28T10:59:11.283Z] INFO  📡 Standard extraction with options: {
  "formats": [
    "html"
  ],
  "timeout": 54000,
  "waitFor": 4000,
  "actionsCount": 1,
  "removeBase64Images": true,
  "blockAds": true,
  "excludeTagsCount": 81
}
[2025-07-28T10:59:11.293Z] INFO        🔍 Keyword check result: matched - proceeding anyway for testing
[2025-07-28T10:59:11.293Z] INFO        🔍 Extracting content with enhanced client...
[2025-07-28T10:59:11.293Z] INFO  🚀 Enhanced extraction starting for: https://www.finanzen.net/nachricht/aktien/zurueckhaltung-tesla-aktie-unter-druck-schwache-verkaufszahlen-und-aerger-mit-trump-belasten-14597049
[2025-07-28T10:59:11.293Z] INFO  📋 Using site configuration for: Finanzen.net
[2025-07-28T10:59:11.294Z] INFO  🎯 Known German financial site: Finanzen.net
[2025-07-28T10:59:11.294Z] INFO  🎯 Selected strategy: Structured Data Enhanced
[2025-07-28T10:59:11.294Z] INFO  🎯 Attempting Structured Data Enhanced extraction for: https://www.finanzen.net/nachricht/aktien/zurueckhaltung-tesla-aktie-unter-druck-schwache-verkaufszahlen-und-aerger-mit-trump-belasten-14597049
[2025-07-28T10:59:11.294Z] INFO  📡 Standard extraction with options: {
  "formats": [
    "html"
  ],
  "timeout": 60000,
  "waitFor": 5000,
  "actionsCount": 1,
  "removeBase64Images": true,
  "blockAds": true,
  "excludeTagsCount": 6
}
[2025-07-28T10:59:11.747Z] INFO   [32m[1m✓[22m[39m Compiled in 528ms (1481 modules)
[2025-07-28T10:59:11.994Z] INFO  Generating import map
[2025-07-28T10:59:12.079Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:59:12.200Z] INFO   [32m[1m✓[22m[39m Compiled in 448ms (1481 modules)
[2025-07-28T10:59:12.396Z] INFO  Generating import map
[2025-07-28T10:59:12.404Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:59:12.511Z] INFO   [32m[1m✓[22m[39m Compiled in 307ms (1481 modules)
[2025-07-28T10:59:12.719Z] INFO  Generating import map
[2025-07-28T10:59:12.792Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:59:12.828Z] INFO   [32m[1m✓[22m[39m Compiled in 313ms (1481 modules)
[2025-07-28T10:59:13.065Z] INFO  Generating import map
[2025-07-28T10:59:13.076Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:59:13.203Z] INFO   [32m[1m✓[22m[39m Compiled in 371ms (1481 modules)
[2025-07-28T10:59:13.497Z] INFO  Generating import map
[2025-07-28T10:59:13.513Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:59:13.685Z] INFO   [32m[1m✓[22m[39m Compiled in 473ms (1481 modules)
[2025-07-28T10:59:13.928Z] INFO  Generating import map
[2025-07-28T10:59:13.935Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:59:14.067Z] INFO   [32m[1m✓[22m[39m Compiled in 378ms (1481 modules)
[2025-07-28T10:59:14.480Z] INFO  Generating import map
[2025-07-28T10:59:14.484Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:59:14.603Z] INFO   [32m[1m✓[22m[39m Compiled in 523ms (1481 modules)
[2025-07-28T10:59:14.847Z] INFO  Generating import map
[2025-07-28T10:59:14.851Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:59:14.977Z] INFO   [32m[1m✓[22m[39m Compiled in 367ms (1481 modules)
[2025-07-28T10:59:15.221Z] INFO  Generating import map
[2025-07-28T10:59:15.225Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:59:15.341Z] INFO   [32m[1m✓[22m[39m Compiled in 361ms (1481 modules)
[2025-07-28T10:59:15.565Z] INFO  Generating import map
[2025-07-28T10:59:15.647Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:59:15.683Z] INFO   [32m[1m✓[22m[39m Compiled in 338ms (1481 modules)
[2025-07-28T10:59:16.010Z] INFO  Generating import map
[2025-07-28T10:59:16.020Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:59:16.028Z] INFO   [32m[1m✓[22m[39m Compiled in 342ms (1481 modules)
[2025-07-28T10:59:16.388Z] INFO   [32m[1m✓[22m[39m Compiled in 356ms (1481 modules)
[2025-07-28T10:59:16.414Z] INFO  Generating import map
[2025-07-28T10:59:16.427Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:59:16.746Z] INFO   [32m[1m✓[22m[39m Compiled in 353ms (1481 modules)
[2025-07-28T10:59:16.799Z] INFO  Generating import map
[2025-07-28T10:59:16.961Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:59:17.129Z] INFO   [32m[1m✓[22m[39m Compiled in 380ms (1481 modules)
[2025-07-28T10:59:17.337Z] INFO  Generating import map
[2025-07-28T10:59:17.343Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:59:17.484Z] INFO   [32m[1m✓[22m[39m Compiled in 351ms (1481 modules)
[2025-07-28T10:59:17.702Z] INFO  Generating import map
[2025-07-28T10:59:17.710Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:59:17.846Z] INFO   [32m[1m✓[22m[39m Compiled in 358ms (1481 modules)
[2025-07-28T10:59:18.066Z] INFO  Generating import map
[2025-07-28T10:59:18.076Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:59:18.215Z] INFO   [32m[1m✓[22m[39m Compiled in 365ms (1481 modules)
[2025-07-28T10:59:18.506Z] INFO  Generating import map
[2025-07-28T10:59:18.516Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:59:18.640Z] INFO   [32m[1m✓[22m[39m Compiled in 422ms (1481 modules)
[2025-07-28T10:59:18.940Z] INFO  Generating import map
[2025-07-28T10:59:18.952Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:59:19.069Z] INFO   [32m[1m✓[22m[39m Compiled in 422ms (1481 modules)
[2025-07-28T10:59:19.322Z] INFO  Generating import map
[2025-07-28T10:59:19.329Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:59:19.440Z] INFO   [32m[1m✓[22m[39m Compiled in 367ms (1481 modules)
[2025-07-28T10:59:19.803Z] INFO  Generating import map
[2025-07-28T10:59:19.812Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:59:19.870Z] INFO   [32m[1m✓[22m[39m Compiled in 426ms (1481 modules)
[2025-07-28T10:59:20.411Z] INFO   [32m[1m✓[22m[39m Compiled in 532ms (1481 modules)
[2025-07-28T10:59:20.430Z] INFO  Generating import map
[2025-07-28T10:59:20.441Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:59:20.729Z] INFO   [32m[1m✓[22m[39m Compiled in 315ms (1481 modules)
[2025-07-28T10:59:20.777Z] INFO  Generating import map
[2025-07-28T10:59:20.924Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:59:21.056Z] INFO   [32m[1m✓[22m[39m Compiled in 323ms (1481 modules)
[2025-07-28T10:59:21.248Z] INFO  Generating import map
[2025-07-28T10:59:21.252Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:59:21.390Z] INFO   [32m[1m✓[22m[39m Compiled in 329ms (1481 modules)
[2025-07-28T10:59:21.590Z] INFO  Generating import map
[2025-07-28T10:59:21.595Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:59:21.729Z] INFO   [32m[1m✓[22m[39m Compiled in 334ms (1481 modules)
[2025-07-28T10:59:21.969Z] INFO  Generating import map
[2025-07-28T10:59:22.052Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:59:22.095Z] INFO   [32m[1m✓[22m[39m Compiled in 359ms (1481 modules)
[2025-07-28T10:59:22.399Z] INFO  Generating import map
[2025-07-28T10:59:22.407Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:59:22.427Z] INFO   [32m[1m✓[22m[39m Compiled in 328ms (1481 modules)
[2025-07-28T10:59:22.752Z] INFO   [32m[1m✓[22m[39m Compiled in 320ms (1481 modules)
[2025-07-28T10:59:22.798Z] INFO  Generating import map
[2025-07-28T10:59:22.961Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:59:23.127Z] INFO   [32m[1m✓[22m[39m Compiled in 372ms (1481 modules)
[2025-07-28T10:59:23.367Z] INFO  Generating import map
[2025-07-28T10:59:23.375Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:59:23.553Z] INFO   [32m[1m✓[22m[39m Compiled in 422ms (1481 modules)
[2025-07-28T10:59:23.791Z] INFO  Generating import map
[2025-07-28T10:59:23.801Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:59:23.928Z] INFO   [32m[1m✓[22m[39m Compiled in 372ms (1481 modules)
[2025-07-28T10:59:24.166Z] INFO  Generating import map
[2025-07-28T10:59:24.171Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:59:24.286Z] INFO   [32m[1m✓[22m[39m Compiled in 353ms (1481 modules)
[2025-07-28T10:59:24.535Z] INFO  Generating import map
[2025-07-28T10:59:24.540Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:59:24.667Z] INFO   [32m[1m✓[22m[39m Compiled in 378ms (1481 modules)
[2025-07-28T10:59:24.937Z] INFO  Generating import map
[2025-07-28T10:59:24.946Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:59:25.067Z] INFO   [32m[1m✓[22m[39m Compiled in 397ms (1481 modules)
[2025-07-28T10:59:25.439Z] INFO  Generating import map
[2025-07-28T10:59:25.453Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:59:25.493Z] INFO   [32m[1m✓[22m[39m Compiled in 423ms (1481 modules)
[2025-07-28T10:59:25.995Z] INFO   [32m[1m✓[22m[39m Compiled in 494ms (1481 modules)
[2025-07-28T10:59:26.016Z] INFO  Generating import map
[2025-07-28T10:59:26.023Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:59:26.317Z] INFO   [32m[1m✓[22m[39m Compiled in 318ms (1481 modules)
[2025-07-28T10:59:26.372Z] INFO  Generating import map
[2025-07-28T10:59:26.507Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:59:26.635Z] INFO   [32m[1m✓[22m[39m Compiled in 315ms (1481 modules)
[2025-07-28T10:59:26.827Z] INFO  Generating import map
[2025-07-28T10:59:26.831Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:59:26.949Z] INFO   [32m[1m✓[22m[39m Compiled in 311ms (1481 modules)
[2025-07-28T10:59:27.157Z] INFO  Generating import map
[2025-07-28T10:59:27.167Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:59:27.284Z] INFO   [32m[1m✓[22m[39m Compiled in 332ms (1481 modules)
[2025-07-28T10:59:27.508Z] INFO  Generating import map
[2025-07-28T10:59:27.581Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:59:27.609Z] INFO   [32m[1m✓[22m[39m Compiled in 318ms (1481 modules)
[2025-07-28T10:59:27.864Z] INFO  Generating import map
[2025-07-28T10:59:27.941Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:59:27.975Z] INFO   [32m[1m✓[22m[39m Compiled in 363ms (1481 modules)
[2025-07-28T10:59:28.281Z] INFO   [32m[1m✓[22m[39m Compiled in 302ms (1481 modules)
[2025-07-28T10:59:28.305Z] INFO  Generating import map
[2025-07-28T10:59:28.316Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:59:28.632Z] INFO   [32m[1m✓[22m[39m Compiled in 347ms (1481 modules)
[2025-07-28T10:59:28.678Z] INFO  Generating import map
[2025-07-28T10:59:28.830Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:59:28.975Z] INFO   [32m[1m✓[22m[39m Compiled in 340ms (1481 modules)
[2025-07-28T10:59:29.183Z] INFO  Generating import map
[2025-07-28T10:59:29.190Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:59:29.331Z] INFO   [32m[1m✓[22m[39m Compiled in 351ms (1481 modules)
[2025-07-28T10:59:29.555Z] INFO  Generating import map
[2025-07-28T10:59:29.573Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:59:29.722Z] INFO   [32m[1m✓[22m[39m Compiled in 388ms (1481 modules)
[2025-07-28T10:59:29.948Z] INFO  ✅ Standard extraction successful
[2025-07-28T10:59:29.950Z] INFO  📅 Extracted date from text pattern: 08.02.2024
[2025-07-28T10:59:29.950Z] INFO        📝 Creating candidate article with English-only enhancement
[2025-07-28T10:59:29.950Z] INFO  📅 Extracted date from HTML content: 2024-02-08T00:00:00.000Z
[2025-07-28T10:59:29.955Z] INFO  🚀 Starting English-only content enhancement for: DAX macht 4.000 Punkte im 1. Halbjahr – Bayer, Daimler Truck, Kontron, Mercedes-Benz, Nordex und Siemens im Check - DER AKTIONÄR
[2025-07-28T10:59:29.955Z] INFO  📏 Limits: Content size max 10MB, Token max 25000
[2025-07-28T10:59:29.955Z] INFO  🚀 Using ENGLISH-ONLY ENHANCEMENT SYSTEM...
[2025-07-28T10:59:29.955Z] INFO  🔄 Enhancement attempt 1/3...
[2025-07-28T10:59:29.956Z] INFO  🚀 Starting English-only content enhancement...
[2025-07-28T10:59:29.956Z] INFO  📄 Processing: "DAX macht 4.000 Punkte im 1. Halbjahr – Bayer, Daimler Truck, Kontron, Mercedes-Benz, Nordex und Siemens im Check - DER AKTIONÄR" (55346 chars, 0.05MB)
[2025-07-28T10:59:29.956Z] INFO  📝 Using simplified approach: Plain text generation with completion focus
[2025-07-28T10:59:29.956Z] INFO  📊 Token allocation: Prompt ~14343, Output limit: 16000, Available: 112657
[2025-07-28T10:59:30.064Z] ERROR ❌ English-only enhancement failed: {}
[2025-07-28T10:59:30.065Z] INFO  📊 Unified Enhancement Metrics:
    - Processing Time: 109ms
    - Functions Covered: 
    - API Calls Reduced: 0
    - Tokens Used: Unknown
    - Cost: $Unknown
    - Success: ❌
[2025-07-28T10:59:30.065Z] WARN  ⚠️ Attempt 1: withRateLimit is not defined
[2025-07-28T10:59:30.065Z] INFO  ⏳ Waiting 2000ms before retry...
[2025-07-28T10:59:29.955Z] INFO  🌐 Feed language: DE (German feed)
[2025-07-28T10:59:29.955Z] INFO  📊 Content size check: 55346 characters (0.05MB) → 13837 estimated tokens
[2025-07-28T10:59:29.950Z] INFO  ✅ Enhanced extraction completed in 18669ms
[2025-07-28T10:59:30.241Z] INFO   [32m[1m✓[22m[39m Compiled in 513ms (1481 modules)
[2025-07-28T10:59:30.464Z] INFO  Generating import map
[2025-07-28T10:59:30.468Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:59:30.587Z] INFO   [32m[1m✓[22m[39m Compiled in 342ms (1481 modules)
[2025-07-28T10:59:30.826Z] INFO  Generating import map
[2025-07-28T10:59:30.830Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:59:30.967Z] INFO   [32m[1m✓[22m[39m Compiled in 375ms (1481 modules)
[2025-07-28T10:59:31.482Z] INFO  Generating import map
[2025-07-28T10:59:31.486Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:59:31.504Z] INFO   [32m[1m✓[22m[39m Compiled in 527ms (1481 modules)
[2025-07-28T10:59:31.836Z] INFO   [32m[1m✓[22m[39m Compiled in 329ms (1481 modules)
[2025-07-28T10:59:31.886Z] INFO  Generating import map
[2025-07-28T10:59:32.022Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:59:32.065Z] INFO  🔄 Enhancement attempt 2/3...
[2025-07-28T10:59:32.065Z] INFO  📄 Processing: "DAX macht 4.000 Punkte im 1. Halbjahr – Bayer, Daimler Truck, Kontron, Mercedes-Benz, Nordex und Siemens im Check - DER AKTIONÄR" (55346 chars, 0.05MB)
[2025-07-28T10:59:32.065Z] INFO  📝 Using simplified approach: Plain text generation with completion focus
[2025-07-28T10:59:32.065Z] INFO  📊 Token allocation: Prompt ~14343, Output limit: 16000, Available: 112657
[2025-07-28T10:59:32.135Z] INFO  📊 Unified Enhancement Metrics:
    - Processing Time: 70ms
    - Functions Covered: 
    - API Calls Reduced: 0
    - Tokens Used: Unknown
    - Cost: $Unknown
    - Success: ❌
[2025-07-28T10:59:32.135Z] ERROR ❌ English-only enhancement failed: {}
[2025-07-28T10:59:32.065Z] INFO  🚀 Starting English-only content enhancement...
[2025-07-28T10:59:32.135Z] WARN  ⚠️ Attempt 2: withRateLimit is not defined
[2025-07-28T10:59:32.136Z] INFO  ⏳ Waiting 4000ms before retry...
[2025-07-28T10:59:32.225Z] INFO   [32m[1m✓[22m[39m Compiled in 383ms (1481 modules)
[2025-07-28T10:59:32.420Z] INFO  Generating import map
[2025-07-28T10:59:32.425Z] INFO  No new imports found, skipping writing import map
[2025-07-28T10:59:32.560Z] INFO   [32m[1m✓[22m[39m Compiled in 329ms (1481 modules)
