{"timestamp":"2025-07-27T21:26:37.431Z","level":"INFO","component":"test-content-pipeline","message":"🧪 Test content pipeline started","context":{"startTime":"2025-07-27T21:26:37.431Z","pipeline":"test-content-pipeline","testUrlCount":9,"testUrls":[{"url":"https://www.handelsblatt.com/politik/international/israel-gute-umfragewerte-netanjahu-koennte-neuwah...","domain":"www.handelsblatt.com"},{"url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-d...","domain":"www.deraktionaer.de"},{"url":"https://www.finanzen.net/nachricht/aktien/zurueckhaltung-tesla-aktie-unter-druck-schwache-verkaufsza...","domain":"www.finanzen.net"},{"url":"https://www.onvista.de/news/2025/07-01-dax-vorboerse-dax-duerfte-mit-leichtem-schwung-in-den-juli-st...","domain":"www.onvista.de"},{"url":"https://www.wallstreet-online.de/nachricht/********-oelpreise-fallen-warten-foerderbeschluss-opec","domain":"www.wallstreet-online.de"},{"url":"https://www.wallstreet-online.de/nachricht/********-apple-plant-siri-neustart-paukenschlag-apple-den...","domain":"www.wallstreet-online.de"},{"url":"https://www.manager-magazin.de/politik/deutschland/deutschland-braucht-neue-schulden-regeln-und-mehr...","domain":"www.manager-magazin.de"},{"url":"https://www.spiegel.de/wirtschaft/unternehmen/deutsche-bank-meldet-rekordgewinne-im-dritten-quartal-...","domain":"www.spiegel.de"},{"url":"https://www.bbc.co.uk/news/articles/c79q8g7q283o","domain":"www.bbc.co.uk"}]}}
{"timestamp":"2025-07-27T21:26:37.431Z","level":"INFO","component":"test-content-pipeline","message":"🔍 Validating database state"}
{"timestamp":"2025-07-27T21:26:37.443Z","level":"INFO","component":"test-content-pipeline","message":"✅ Database validation passed","context":{"pipeline":"test-content-pipeline","activeKeywords":20,"totalCategories":4,"keywordSample":[{"keyword":"ETF","englishKeyword":"ETF","weight":5},{"keyword":"Investition","englishKeyword":"Investment","weight":5},{"keyword":"Prognose","englishKeyword":"Forecast","weight":5},{"keyword":"Wall Street","englishKeyword":"Wall Street","weight":5},{"keyword":"Dividende","englishKeyword":"Dividend","weight":5},{"keyword":"Wachstumsaktien","englishKeyword":"Growth Stocks","weight":5},{"keyword":"Small Cap","englishKeyword":"Small Cap","weight":5},{"keyword":"Fusion","englishKeyword":"Merger","weight":5},{"keyword":"Börsengang","englishKeyword":"IPO","weight":5},{"keyword":"Aktien","englishKeyword":"Stocks","weight":5}]}}
{"timestamp":"2025-07-27T21:26:37.443Z","level":"INFO","component":"test-content-pipeline","message":"🔍 Getting test RSS feed ID"}
{"timestamp":"2025-07-27T21:26:37.450Z","level":"INFO","component":"test-content-pipeline","message":"🔄 Starting parallel URL processing","context":{"pipeline":"test-content-pipeline","processingMode":"parallel-openai-safe","concurrency":2,"rateLimiting":true,"totalUrls":9}}
{"timestamp":"2025-07-27T21:26:37.450Z","level":"INFO","component":"test-content-pipeline","message":"📦 Processing URL batch","context":{"pipeline":"test-content-pipeline","batchNumber":1,"totalBatches":5,"batchSize":2,"batchUrls":[{"url":"https://www.handelsblatt.com/politik/international/israel-gute-umfragewerte-neta...","domain":"www.handelsblatt.com"},{"url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-i...","domain":"www.deraktionaer.de"}]}}
{"timestamp":"2025-07-27T21:26:37.451Z","level":"INFO","component":"test-content-pipeline","message":"🚀 Processing started","context":{"url":"https://www.handelsblatt.com/politik/international/israel-gute-umfragewerte-netanjahu-koennte-neuwahlen-provozieren/100138438.html","processingId":"test-url-1","pipeline":"test-content-pipeline","urlIndex":1,"extractedTitle":"Test Article","domain":"www.handelsblatt.com"}}
{"timestamp":"2025-07-27T21:26:37.450Z","level":"INFO","component":"test-content-pipeline","message":"📰 Processing URL","context":{"pipeline":"test-content-pipeline","urlIndex":1,"totalUrls":9,"url":"https://www.handelsblatt.com/politik/international/israel-gute-umfragewerte-netanjahu-koennte-neuwah...","domain":"www.handelsblatt.com"}}
{"timestamp":"2025-07-27T21:26:37.450Z","level":"INFO","component":"test-content-pipeline","message":"✅ Test RSS feed selected","context":{"pipeline":"test-content-pipeline","testFeed":{"id":"10","name":"DW","url":"https://rss.dw.com/rdf/rss-en-all"}}}
{"timestamp":"2025-07-27T21:26:37.451Z","level":"INFO","component":"test-content-pipeline","message":"🚀 Processing started","context":{"url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-daimler-truck-kontron-mercedes-benz-nordex-und-siemens-im-check-20382487.html?feed=directrssfeed","processingId":"test-url-2","pipeline":"test-content-pipeline","urlIndex":2,"extractedTitle":"dax macht  punkte im  halbjahr bayer daimler truck kontron mercedes benz nordex und siemens im check","domain":"www.deraktionaer.de"}}
{"timestamp":"2025-07-27T21:26:37.451Z","level":"INFO","component":"test-content-pipeline","message":"📰 Processing URL","context":{"pipeline":"test-content-pipeline","urlIndex":2,"totalUrls":9,"url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-d...","domain":"www.deraktionaer.de"}}
{"timestamp":"2025-07-27T21:26:37.476Z","level":"INFO","component":"test-content-pipeline","message":"🔍 Keyword filtering result","context":{"pipeline":"test-content-pipeline","url":"https://www.handelsblatt.com/politik/international/israel-gute-umfragewerte-netanjahu-koennte-neuwah","hasRelevantKeywords":false,"proceedingAnyway":true,"reason":"test-pipeline-override"}}
{"timestamp":"2025-07-27T21:26:37.476Z","level":"INFO","component":"test-content-pipeline","message":"🔍 Starting content extraction","context":{"pipeline":"test-content-pipeline","url":"https://www.handelsblatt.com/politik/international/israel-gute-umfragewerte-netanjahu-koennte-neuwah","extractionMethod":"enhanced-firecrawl-html-first"}}
{"timestamp":"2025-07-27T21:26:37.485Z","level":"INFO","component":"test-content-pipeline","message":"🔍 Keyword filtering result","context":{"pipeline":"test-content-pipeline","url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-d","hasRelevantKeywords":true,"proceedingAnyway":true,"reason":"test-pipeline-override"}}
{"timestamp":"2025-07-27T21:26:37.485Z","level":"INFO","component":"test-content-pipeline","message":"🔍 Starting content extraction","context":{"pipeline":"test-content-pipeline","url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-d","extractionMethod":"enhanced-firecrawl-html-first"}}
{"timestamp":"2025-07-27T21:29:04.351Z","level":"INFO","component":"test-content-pipeline","message":"📝 Starting candidate article creation","context":{"pipeline":"test-content-pipeline","url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-d","contentLength":48,"hasPublishedDate":false,"enhancementType":"english-only-unified"}}
{"timestamp":"2025-07-27T21:29:04.350Z","level":"INFO","component":"test-content-pipeline","message":"📊 📡 Firecrawl extraction completed","context":{"metrics":{"firecrawlDurationMs":146865,"success":true,"contentLength":48,"extractionMethod":"structured","wordCount":58},"pipeline":"test-content-pipeline","url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-d"}}
{"timestamp":"2025-07-27T21:29:52.009Z","level":"ERROR","component":"test-content-pipeline","message":"Failed to process test URL","context":{"pipeline":"test-content-pipeline","url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-d","processingDuration":194557,"urlIndex":2,"errorCategory":"conversion_error"},"error":{"name":"Error","message":"Failed to create candidate article: Enhancement failed for article \"dax macht  punkte im  halbjahr bayer daimler truck kontron mercedes benz nordex und siemens im check\": All 3 enhancement attempts failed. Last error: Content appears incomplete despite normal token usage (13.4%). Check content quality and prompt instructions. Issues: 🚨 CRITICAL: Content appears truncated - ends with \"observing how these efforts to\" instead of proper punctuation, 🚨 CRITICAL: HTML structure incomplete - 19 open tags vs 18 close tags (likely truncated), 🚨 CRITICAL: Content severely truncated (251 words, expected 600-750) - likely token limit issue","stack":"Error: Failed to create candidate article: Enhancement failed for article \"dax macht  punkte im  halbjahr bayer daimler truck kontron mercedes benz nordex und siemens im check\": All 3 enhancement attempts failed. Last error: Content appears incomplete despite normal token usage (13.4%). Check content quality and prompt instructions. Issues: 🚨 CRITICAL: Content appears truncated - ends with \"observing how these efforts to\" instead of proper punctuation, 🚨 CRITICAL: HTML structure incomplete - 19 open tags vs 18 close tags (likely truncated), 🚨 CRITICAL: Content severely truncated (251 words, expected 600-750) - likely token limit issue\n    at createCandidateArticle (webpack-internal:///(rsc)/./src/lib/server/create-candidate.ts:475:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async processTestUrl (webpack-internal:///(rsc)/./src/app/api/test-content-pipeline/route.ts:676:31)\n    at async eval (webpack-internal:///(rsc)/./src/app/api/test-content-pipeline/route.ts:209:46)\n    at async Promise.all (index 1)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/test-content-pipeline/route.ts:276:13)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
{"timestamp":"2025-07-27T21:29:52.049Z","level":"ERROR","component":"test-content-pipeline","message":"❌ Processing failed","context":{"url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-daimler-truck-kontron-mercedes-benz-nordex-und-siemens-im-check-20382487.html?feed=directrssfeed","processingId":"test-url-2","duration":194557,"success":false,"pipeline":"test-content-pipeline","error":"Failed to create candidate article: Enhancement failed for article \"dax macht  punkte im  halbjahr bayer daimler truck kontron mercedes benz nordex und siemens im check\": All 3 enhancement attempts failed. Last error: Content appears incomplete despite normal token usage (13.4%). Check content quality and prompt instructions. Issues: 🚨 CRITICAL: Content appears truncated - ends with \"observing how these efforts to\" instead of proper punctuation, 🚨 CRITICAL: HTML structure incomplete - 19 open tags vs 18 close tags (likely truncated), 🚨 CRITICAL: Content severely truncated (251 words, expected 600-750) - likely token limit issue"}}
{"timestamp":"2025-07-27T21:29:52.049Z","level":"ERROR","component":"test-content-pipeline","message":"💥 URL processing error","context":{"pipeline":"test-content-pipeline","urlIndex":2,"url":"https://www.deraktionaer.de/artikel/maerkte-forex-zinsen/dax-macht-4000-punkte-im-1-halbjahr-bayer-d","reason":"Failed to create candidate article: Enhancement failed for article \"dax macht  punkte im  halbjahr bayer daimler truck kontron mercedes benz nordex und siemens im check\": All 3 enhancement attempts failed. Last error: Content appears incomplete despite normal token usage (13.4%). Check content quality and prompt instructions. Issues: 🚨 CRITICAL: Content appears truncated - ends with \"observing how these efforts to\" instead of proper punctuation, 🚨 CRITICAL: HTML structure incomplete - 19 open tags vs 18 close tags (likely truncated), 🚨 CRITICAL: Content severely truncated (251 words, expected 600-750) - likely token limit issue","errorCategory":"conversion_error"},"error":{"name":"Error","message":"Failed to create candidate article: Enhancement failed for article \"dax macht  punkte im  halbjahr bayer daimler truck kontron mercedes benz nordex und siemens im check\": All 3 enhancement attempts failed. Last error: Content appears incomplete despite normal token usage (13.4%). Check content quality and prompt instructions. Issues: 🚨 CRITICAL: Content appears truncated - ends with \"observing how these efforts to\" instead of proper punctuation, 🚨 CRITICAL: HTML structure incomplete - 19 open tags vs 18 close tags (likely truncated), 🚨 CRITICAL: Content severely truncated (251 words, expected 600-750) - likely token limit issue","stack":"Error: Failed to create candidate article: Enhancement failed for article \"dax macht  punkte im  halbjahr bayer daimler truck kontron mercedes benz nordex und siemens im check\": All 3 enhancement attempts failed. Last error: Content appears incomplete despite normal token usage (13.4%). Check content quality and prompt instructions. Issues: 🚨 CRITICAL: Content appears truncated - ends with \"observing how these efforts to\" instead of proper punctuation, 🚨 CRITICAL: HTML structure incomplete - 19 open tags vs 18 close tags (likely truncated), 🚨 CRITICAL: Content severely truncated (251 words, expected 600-750) - likely token limit issue\n    at eval (webpack-internal:///(rsc)/./src/app/api/test-content-pipeline/route.ts:234:65)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 1)\n    at async _POST (webpack-internal:///(rsc)/./src/app/api/test-content-pipeline/route.ts:276:13)\n    at async AppRouteRouteModule.do (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:34112)\n    at async AppRouteRouteModule.handle (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/compiled/next-server/app-route.runtime.dev.js:26:41338)\n    at async doRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1513:42)\n    at async DevServer.renderToResponseWithComponentsImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1915:28)\n    at async DevServer.renderPageComponent (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2403:24)\n    at async DevServer.renderToResponseImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:2440:32)\n    at async DevServer.pipeImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:1007:25)\n    at async NextNodeServer.handleCatchallRenderRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/next-server.js:305:17)\n    at async DevServer.handleRequestImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/base-server.js:899:17)\n    at async /Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:371:20\n    at async Span.traceAsyncFn (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/trace/trace.js:157:20)\n    at async DevServer.handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/dev/next-dev-server.js:368:24)\n    at async invokeRender (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:237:21)\n    at async handleRequest (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:428:24)\n    at async requestHandlerImpl (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/router-server.js:452:13)\n    at async Server.requestListener (/Users/<USER>/Developer/Projects/Borsen-Blick/borsenblick/node_modules/.pnpm/next@15.3.0_@babel+core@7.28.0_react-dom@19.1.0_react@19.1.0__react@19.1.0_sass@1.77.4/node_modules/next/dist/server/lib/start-server.js:158:13)"}}
