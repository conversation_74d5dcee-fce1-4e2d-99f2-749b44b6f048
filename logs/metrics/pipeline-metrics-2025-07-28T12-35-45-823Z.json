{"timing": {"startTime": 1753703494972, "totalDurationMs": 2650846, "phases": {"rssParsingMs": 2650627, "firecrawlProcessingMs": 0, "openaiProcessingMs": 0, "databaseOperationsMs": 0}, "endTime": 1753706145818}, "processing": {"feedsProcessed": 0, "articlesScraped": 0, "articlesAccepted": 0, "articlesRejected": 0, "successRate": 0, "throughputPerMinute": 0}, "apiUsage": {"firecrawl": {"requestsCount": 106, "successCount": 106, "rateLimitedCount": 0, "errorCount": 0, "averageResponseTimeMs": 2000, "estimatedCost": 0.212}, "openai": {"requestsCount": 0, "tokensUsed": 0, "averageResponseTimeMs": 0, "estimatedCost": 0, "enhancementSuccessRate": 0}}, "feedMetrics": [], "system": {"memoryUsageMB": 1530, "cpuUsagePercent": 0, "parallelProcessingEfficiency": 99.99173848650582}, "quality": {"averageContentQuality": 0, "duplicateFilterEfficiency": 0, "keywordMatchAccuracy": 0}}