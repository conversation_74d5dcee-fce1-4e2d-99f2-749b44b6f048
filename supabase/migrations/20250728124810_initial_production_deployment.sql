create type "public"."enum__articles_v_version_article_type" as enum ('generated', 'curated');

create type "public"."enum__articles_v_version_placement" as enum ('tier-1', 'tier-2', 'tier-3');

create type "public"."enum__articles_v_version_related_companies_relevance" as enum ('high', 'medium', 'low');

create type "public"."enum__articles_v_version_status" as enum ('draft', 'published');

create type "public"."enum__articles_v_version_workflow_stage" as enum ('curated-draft', 'candidate-article', 'translated', 'ready-for-review');

create type "public"."enum__pages_v_version_status" as enum ('draft', 'published');

create type "public"."enum_articles_article_type" as enum ('generated', 'curated');

create type "public"."enum_articles_placement" as enum ('tier-1', 'tier-2', 'tier-3');

create type "public"."enum_articles_related_companies_relevance" as enum ('high', 'medium', 'low');

create type "public"."enum_articles_status" as enum ('draft', 'published');

create type "public"."enum_articles_workflow_stage" as enum ('curated-draft', 'candidate-article', 'translated', 'ready-for-review');

create type "public"."enum_footer_legal_links_link_type" as enum ('reference', 'custom');

create type "public"."enum_footer_navigation_sections_links_link_type" as enum ('reference', 'custom');

create type "public"."enum_footer_social_links_platform" as enum ('instagram', 'facebook', 'twitter', 'linkedin', 'youtube', 'github', 'threads', 'email');

create type "public"."enum_header_nav_items_link_type" as enum ('reference', 'custom');

create type "public"."enum_pages_status" as enum ('draft', 'published');

create type "public"."enum_processed_urls_status" as enum ('pending', 'accepted', 'rejected', 'error');

create type "public"."enum_rss_feeds_language" as enum ('de', 'en');

create type "public"."enum_rss_feeds_priority" as enum ('low', 'medium', 'high');

create sequence "public"."_articles_v_id_seq";

create sequence "public"."_articles_v_rels_id_seq";

create sequence "public"."_articles_v_version_english_tab_enhanced_key_insights_id_seq";

create sequence "public"."_articles_v_version_english_tab_keywords_id_seq";

create sequence "public"."_articles_v_version_german_tab_german_key_insights_id_seq";

create sequence "public"."_articles_v_version_german_tab_german_keywords_id_seq";

create sequence "public"."_articles_v_version_related_companies_id_seq";

create sequence "public"."_pages_v_id_seq";

create sequence "public"."articles_id_seq";

create sequence "public"."articles_rels_id_seq";

create sequence "public"."categories_id_seq";

create sequence "public"."footer_id_seq";

create sequence "public"."footer_rels_id_seq";

create sequence "public"."header_id_seq";

create sequence "public"."header_rels_id_seq";

create sequence "public"."keywords_id_seq";

create sequence "public"."media_id_seq";

create sequence "public"."pages_id_seq";

create sequence "public"."payload_locked_documents_id_seq";

create sequence "public"."payload_locked_documents_rels_id_seq";

create sequence "public"."payload_migrations_id_seq";

create sequence "public"."payload_preferences_id_seq";

create sequence "public"."payload_preferences_rels_id_seq";

create sequence "public"."processed_urls_id_seq";

create sequence "public"."rss_feeds_id_seq";

create sequence "public"."users_id_seq";

create table "public"."_articles_v" (
    "id" integer not null default nextval('_articles_v_id_seq'::regclass),
    "parent_id" integer,
    "version_title" character varying,
    "version_slug" character varying,
    "version_featured_image_id" integer,
    "version_article_type" enum__articles_v_version_article_type default 'curated'::enum__articles_v_version_article_type,
    "version_workflow_stage" enum__articles_v_version_workflow_stage default 'curated-draft'::enum__articles_v_version_workflow_stage,
    "version_placement" enum__articles_v_version_placement,
    "version_pinned" boolean default false,
    "version_trending" boolean default false,
    "version_read_time_minutes" numeric,
    "version_published_by_id" integer,
    "version_published_at" timestamp(3) with time zone,
    "version_has_been_enhanced" boolean default false,
    "version_has_german_translation" boolean default false,
    "version_has_original_source" boolean default false,
    "version_english_tab_enhanced_title" character varying,
    "version_english_tab_enhanced_summary" character varying,
    "version_english_tab_enhanced_content" jsonb,
    "version_sources_tab_source_feed_id" integer,
    "version_sources_tab_source_url" character varying,
    "version_sources_tab_original_published_at" timestamp(3) with time zone,
    "version_sources_tab_original_title" character varying,
    "version_sources_tab_original_summary" character varying,
    "version_sources_tab_original_content" jsonb,
    "version_german_tab_german_title" character varying,
    "version_german_tab_german_summary" character varying,
    "version_german_tab_german_content" jsonb,
    "version_meta_title" character varying,
    "version_meta_description" character varying,
    "version_meta_image_id" integer,
    "version_updated_at" timestamp(3) with time zone,
    "version_created_at" timestamp(3) with time zone,
    "version__status" enum__articles_v_version_status default 'draft'::enum__articles_v_version_status,
    "created_at" timestamp(3) with time zone not null default now(),
    "updated_at" timestamp(3) with time zone not null default now(),
    "latest" boolean
);


create table "public"."_articles_v_rels" (
    "id" integer not null default nextval('_articles_v_rels_id_seq'::regclass),
    "order" integer,
    "parent_id" integer not null,
    "path" character varying not null,
    "categories_id" integer
);


create table "public"."_articles_v_version_english_tab_enhanced_key_insights" (
    "_order" integer not null,
    "_parent_id" integer not null,
    "id" integer not null default nextval('_articles_v_version_english_tab_enhanced_key_insights_id_seq'::regclass),
    "insight" character varying,
    "_uuid" character varying
);


create table "public"."_articles_v_version_english_tab_keywords" (
    "_order" integer not null,
    "_parent_id" integer not null,
    "id" integer not null default nextval('_articles_v_version_english_tab_keywords_id_seq'::regclass),
    "keyword" character varying,
    "_uuid" character varying
);


create table "public"."_articles_v_version_german_tab_german_key_insights" (
    "_order" integer not null,
    "_parent_id" integer not null,
    "id" integer not null default nextval('_articles_v_version_german_tab_german_key_insights_id_seq'::regclass),
    "insight" character varying,
    "_uuid" character varying
);


create table "public"."_articles_v_version_german_tab_german_keywords" (
    "_order" integer not null,
    "_parent_id" integer not null,
    "id" integer not null default nextval('_articles_v_version_german_tab_german_keywords_id_seq'::regclass),
    "keyword" character varying,
    "_uuid" character varying
);


create table "public"."_articles_v_version_related_companies" (
    "_order" integer not null,
    "_parent_id" integer not null,
    "id" integer not null default nextval('_articles_v_version_related_companies_id_seq'::regclass),
    "name" character varying,
    "ticker" character varying,
    "exchange" character varying,
    "relevance" enum__articles_v_version_related_companies_relevance default 'medium'::enum__articles_v_version_related_companies_relevance,
    "confidence" numeric default 100,
    "featured" boolean default false,
    "_uuid" character varying
);


create table "public"."_pages_v" (
    "id" integer not null default nextval('_pages_v_id_seq'::regclass),
    "parent_id" integer,
    "version_title" character varying,
    "version_has_german_translation" boolean default false,
    "version_english_tab_title" character varying,
    "version_english_tab_content" jsonb,
    "version_german_tab_german_title" character varying,
    "version_german_tab_german_content" jsonb,
    "version_meta_title" character varying,
    "version_meta_image_id" integer,
    "version_meta_description" character varying,
    "version_published_at" timestamp(3) with time zone,
    "version_slug" character varying,
    "version_slug_lock" boolean default true,
    "version_updated_at" timestamp(3) with time zone,
    "version_created_at" timestamp(3) with time zone,
    "version__status" enum__pages_v_version_status default 'draft'::enum__pages_v_version_status,
    "created_at" timestamp(3) with time zone not null default now(),
    "updated_at" timestamp(3) with time zone not null default now(),
    "latest" boolean,
    "version_featured_image_id" integer,
    "version_parent_id" integer,
    "version_enable_breadcrumbs" boolean default true
);


create table "public"."articles" (
    "id" integer not null default nextval('articles_id_seq'::regclass),
    "title" character varying,
    "slug" character varying,
    "featured_image_id" integer,
    "article_type" enum_articles_article_type default 'curated'::enum_articles_article_type,
    "workflow_stage" enum_articles_workflow_stage default 'curated-draft'::enum_articles_workflow_stage,
    "placement" enum_articles_placement,
    "pinned" boolean default false,
    "trending" boolean default false,
    "read_time_minutes" numeric,
    "published_by_id" integer,
    "published_at" timestamp(3) with time zone,
    "has_been_enhanced" boolean default false,
    "has_german_translation" boolean default false,
    "has_original_source" boolean default false,
    "english_tab_enhanced_title" character varying,
    "english_tab_enhanced_summary" character varying,
    "english_tab_enhanced_content" jsonb,
    "sources_tab_source_feed_id" integer,
    "sources_tab_source_url" character varying,
    "sources_tab_original_published_at" timestamp(3) with time zone,
    "sources_tab_original_title" character varying,
    "sources_tab_original_summary" character varying,
    "sources_tab_original_content" jsonb,
    "german_tab_german_title" character varying,
    "german_tab_german_summary" character varying,
    "german_tab_german_content" jsonb,
    "meta_title" character varying,
    "meta_description" character varying,
    "meta_image_id" integer,
    "updated_at" timestamp(3) with time zone not null default now(),
    "created_at" timestamp(3) with time zone not null default now(),
    "_status" enum_articles_status default 'draft'::enum_articles_status
);


create table "public"."articles_english_tab_enhanced_key_insights" (
    "_order" integer not null,
    "_parent_id" integer not null,
    "id" character varying not null,
    "insight" character varying
);


create table "public"."articles_english_tab_keywords" (
    "_order" integer not null,
    "_parent_id" integer not null,
    "id" character varying not null,
    "keyword" character varying
);


create table "public"."articles_german_tab_german_key_insights" (
    "_order" integer not null,
    "_parent_id" integer not null,
    "id" character varying not null,
    "insight" character varying
);


create table "public"."articles_german_tab_german_keywords" (
    "_order" integer not null,
    "_parent_id" integer not null,
    "id" character varying not null,
    "keyword" character varying
);


create table "public"."articles_related_companies" (
    "_order" integer not null,
    "_parent_id" integer not null,
    "id" character varying not null,
    "name" character varying,
    "ticker" character varying,
    "exchange" character varying,
    "relevance" enum_articles_related_companies_relevance default 'medium'::enum_articles_related_companies_relevance,
    "confidence" numeric default 100,
    "featured" boolean default false
);


create table "public"."articles_rels" (
    "id" integer not null default nextval('articles_rels_id_seq'::regclass),
    "order" integer,
    "parent_id" integer not null,
    "path" character varying not null,
    "categories_id" integer
);


create table "public"."categories" (
    "id" integer not null default nextval('categories_id_seq'::regclass),
    "title" character varying not null,
    "english" character varying not null,
    "slug" character varying,
    "slug_lock" boolean default true,
    "updated_at" timestamp(3) with time zone not null default now(),
    "created_at" timestamp(3) with time zone not null default now()
);


create table "public"."footer" (
    "id" integer not null default nextval('footer_id_seq'::regclass),
    "logo_image_id" integer,
    "logo_title" character varying not null default 'Börsen Blick'::character varying,
    "logo_url" character varying default '/'::character varying,
    "description" character varying,
    "copyright_company_name" character varying not null default 'börsenblick.de'::character varying,
    "copyright_custom_text" character varying,
    "updated_at" timestamp(3) with time zone,
    "created_at" timestamp(3) with time zone
);


create table "public"."footer_legal_links" (
    "_order" integer not null,
    "_parent_id" integer not null,
    "id" character varying not null,
    "link_type" enum_footer_legal_links_link_type default 'reference'::enum_footer_legal_links_link_type,
    "link_new_tab" boolean,
    "link_url" character varying,
    "link_label" character varying not null
);


create table "public"."footer_navigation_sections" (
    "_order" integer not null,
    "_parent_id" integer not null,
    "id" character varying not null,
    "title" character varying not null
);


create table "public"."footer_navigation_sections_links" (
    "_order" integer not null,
    "_parent_id" character varying not null,
    "id" character varying not null,
    "link_type" enum_footer_navigation_sections_links_link_type default 'reference'::enum_footer_navigation_sections_links_link_type,
    "link_new_tab" boolean,
    "link_url" character varying,
    "link_label" character varying not null
);


create table "public"."footer_rels" (
    "id" integer not null default nextval('footer_rels_id_seq'::regclass),
    "order" integer,
    "parent_id" integer not null,
    "path" character varying not null,
    "articles_id" integer,
    "categories_id" integer,
    "pages_id" integer
);


create table "public"."footer_social_links" (
    "_order" integer not null,
    "_parent_id" integer not null,
    "id" character varying not null,
    "platform" enum_footer_social_links_platform not null,
    "url" character varying not null,
    "label" character varying not null
);


create table "public"."header" (
    "id" integer not null default nextval('header_id_seq'::regclass),
    "updated_at" timestamp(3) with time zone,
    "created_at" timestamp(3) with time zone
);


create table "public"."header_nav_items" (
    "_order" integer not null,
    "_parent_id" integer not null,
    "id" character varying not null,
    "link_type" enum_header_nav_items_link_type default 'reference'::enum_header_nav_items_link_type,
    "link_new_tab" boolean,
    "link_url" character varying,
    "link_label" character varying not null
);


create table "public"."header_rels" (
    "id" integer not null default nextval('header_rels_id_seq'::regclass),
    "order" integer,
    "parent_id" integer not null,
    "path" character varying not null,
    "articles_id" integer,
    "categories_id" integer,
    "pages_id" integer
);


create table "public"."keywords" (
    "id" integer not null default nextval('keywords_id_seq'::regclass),
    "keyword" character varying not null,
    "english_keyword" character varying not null,
    "is_active" boolean default true,
    "usage_count" numeric default 0,
    "description" character varying,
    "updated_at" timestamp(3) with time zone not null default now(),
    "created_at" timestamp(3) with time zone not null default now()
);


create table "public"."media" (
    "id" integer not null default nextval('media_id_seq'::regclass),
    "alt" character varying not null,
    "updated_at" timestamp(3) with time zone not null default now(),
    "created_at" timestamp(3) with time zone not null default now(),
    "url" character varying,
    "thumbnail_u_r_l" character varying,
    "filename" character varying,
    "mime_type" character varying,
    "filesize" numeric,
    "width" numeric,
    "height" numeric,
    "focal_x" numeric,
    "focal_y" numeric,
    "sizes_thumbnail_url" character varying,
    "sizes_thumbnail_width" numeric,
    "sizes_thumbnail_height" numeric,
    "sizes_thumbnail_mime_type" character varying,
    "sizes_thumbnail_filesize" numeric,
    "sizes_thumbnail_filename" character varying,
    "sizes_card_url" character varying,
    "sizes_card_width" numeric,
    "sizes_card_height" numeric,
    "sizes_card_mime_type" character varying,
    "sizes_card_filesize" numeric,
    "sizes_card_filename" character varying,
    "sizes_feature_url" character varying,
    "sizes_feature_width" numeric,
    "sizes_feature_height" numeric,
    "sizes_feature_mime_type" character varying,
    "sizes_feature_filesize" numeric,
    "sizes_feature_filename" character varying,
    "sizes_horizontal_url" character varying,
    "sizes_horizontal_width" numeric,
    "sizes_horizontal_height" numeric,
    "sizes_horizontal_mime_type" character varying,
    "sizes_horizontal_filesize" numeric,
    "sizes_horizontal_filename" character varying,
    "sizes_social_url" character varying,
    "sizes_social_width" numeric,
    "sizes_social_height" numeric,
    "sizes_social_mime_type" character varying,
    "sizes_social_filesize" numeric,
    "sizes_social_filename" character varying,
    "sizes_hero_url" character varying,
    "sizes_hero_width" numeric,
    "sizes_hero_height" numeric,
    "sizes_hero_mime_type" character varying,
    "sizes_hero_filesize" numeric,
    "sizes_hero_filename" character varying
);


create table "public"."pages" (
    "id" integer not null default nextval('pages_id_seq'::regclass),
    "title" character varying,
    "has_german_translation" boolean default false,
    "english_tab_title" character varying,
    "english_tab_content" jsonb,
    "german_tab_german_title" character varying,
    "german_tab_german_content" jsonb,
    "meta_title" character varying,
    "meta_image_id" integer,
    "meta_description" character varying,
    "published_at" timestamp(3) with time zone,
    "slug" character varying,
    "slug_lock" boolean default true,
    "updated_at" timestamp(3) with time zone not null default now(),
    "created_at" timestamp(3) with time zone not null default now(),
    "_status" enum_pages_status default 'draft'::enum_pages_status,
    "featured_image_id" integer,
    "parent_id" integer,
    "enable_breadcrumbs" boolean default true
);


create table "public"."payload_locked_documents" (
    "id" integer not null default nextval('payload_locked_documents_id_seq'::regclass),
    "global_slug" character varying,
    "updated_at" timestamp(3) with time zone not null default now(),
    "created_at" timestamp(3) with time zone not null default now()
);


create table "public"."payload_locked_documents_rels" (
    "id" integer not null default nextval('payload_locked_documents_rels_id_seq'::regclass),
    "order" integer,
    "parent_id" integer not null,
    "path" character varying not null,
    "users_id" integer,
    "articles_id" integer,
    "categories_id" integer,
    "pages_id" integer,
    "keywords_id" integer,
    "processed_urls_id" integer,
    "rss_feeds_id" integer,
    "media_id" integer
);


create table "public"."payload_migrations" (
    "id" integer not null default nextval('payload_migrations_id_seq'::regclass),
    "name" character varying,
    "batch" numeric,
    "updated_at" timestamp(3) with time zone not null default now(),
    "created_at" timestamp(3) with time zone not null default now()
);


create table "public"."payload_preferences" (
    "id" integer not null default nextval('payload_preferences_id_seq'::regclass),
    "key" character varying,
    "value" jsonb,
    "updated_at" timestamp(3) with time zone not null default now(),
    "created_at" timestamp(3) with time zone not null default now()
);


create table "public"."payload_preferences_rels" (
    "id" integer not null default nextval('payload_preferences_rels_id_seq'::regclass),
    "order" integer,
    "parent_id" integer not null,
    "path" character varying not null,
    "users_id" integer
);


create table "public"."processed_urls" (
    "id" integer not null default nextval('processed_urls_id_seq'::regclass),
    "url" character varying not null,
    "status" enum_processed_urls_status not null default 'pending'::enum_processed_urls_status,
    "title" character varying,
    "publication_date" timestamp(3) with time zone,
    "feed_id_id" integer,
    "processed_at" timestamp(3) with time zone,
    "reason" character varying,
    "article_id_id" integer,
    "attempt_count" numeric default 0,
    "last_attempt_at" timestamp(3) with time zone,
    "updated_at" timestamp(3) with time zone not null default now(),
    "created_at" timestamp(3) with time zone not null default now()
);


create table "public"."rss_feeds" (
    "id" integer not null default nextval('rss_feeds_id_seq'::regclass),
    "name" character varying not null,
    "url" character varying not null,
    "is_active" boolean default true,
    "language" enum_rss_feeds_language default 'de'::enum_rss_feeds_language,
    "last_processed" timestamp(3) with time zone,
    "last_checked" timestamp(3) with time zone,
    "last_successful_check" timestamp(3) with time zone,
    "items_processed" numeric default 0,
    "items_accepted" numeric default 0,
    "articles_found_since_last_successful" numeric,
    "total_articles_accepted" numeric,
    "error_count" numeric default 0,
    "last_error_message" character varying,
    "processing_frequency" numeric default 60,
    "priority" enum_rss_feeds_priority default 'medium'::enum_rss_feeds_priority,
    "firecrawl_options_remove_base64_images" boolean default true,
    "firecrawl_options_block_ads" boolean default true,
    "keyword_filtering_strict_keyword_matching" boolean,
    "processing_options_max_firecrawl_scrape" numeric,
    "processing_options_max_articles_per_run" numeric,
    "processing_options_skip_translation" boolean,
    "processing_options_skip_enhancement" boolean,
    "processing_options_custom_timeout" numeric,
    "processing_options_enable_stealth" boolean,
    "updated_at" timestamp(3) with time zone not null default now(),
    "created_at" timestamp(3) with time zone not null default now()
);


create table "public"."rss_feeds_firecrawl_options_exclude_tags" (
    "_order" integer not null,
    "_parent_id" integer not null,
    "id" character varying not null,
    "tag" character varying not null
);


create table "public"."rss_feeds_firecrawl_options_include_tags" (
    "_order" integer not null,
    "_parent_id" integer not null,
    "id" character varying not null,
    "tag" character varying not null
);


create table "public"."rss_feeds_keyword_filtering_custom_keywords" (
    "_order" integer not null,
    "_parent_id" integer not null,
    "id" character varying not null,
    "keyword" character varying not null,
    "english_keyword" character varying not null,
    "weight" numeric default 5
);


create table "public"."users" (
    "id" integer not null default nextval('users_id_seq'::regclass),
    "name" character varying,
    "updated_at" timestamp(3) with time zone not null default now(),
    "created_at" timestamp(3) with time zone not null default now(),
    "email" character varying not null,
    "reset_password_token" character varying,
    "reset_password_expiration" timestamp(3) with time zone,
    "salt" character varying,
    "hash" character varying,
    "login_attempts" numeric default 0,
    "lock_until" timestamp(3) with time zone
);


alter sequence "public"."_articles_v_id_seq" owned by "public"."_articles_v"."id";

alter sequence "public"."_articles_v_rels_id_seq" owned by "public"."_articles_v_rels"."id";

alter sequence "public"."_articles_v_version_english_tab_enhanced_key_insights_id_seq" owned by "public"."_articles_v_version_english_tab_enhanced_key_insights"."id";

alter sequence "public"."_articles_v_version_english_tab_keywords_id_seq" owned by "public"."_articles_v_version_english_tab_keywords"."id";

alter sequence "public"."_articles_v_version_german_tab_german_key_insights_id_seq" owned by "public"."_articles_v_version_german_tab_german_key_insights"."id";

alter sequence "public"."_articles_v_version_german_tab_german_keywords_id_seq" owned by "public"."_articles_v_version_german_tab_german_keywords"."id";

alter sequence "public"."_articles_v_version_related_companies_id_seq" owned by "public"."_articles_v_version_related_companies"."id";

alter sequence "public"."_pages_v_id_seq" owned by "public"."_pages_v"."id";

alter sequence "public"."articles_id_seq" owned by "public"."articles"."id";

alter sequence "public"."articles_rels_id_seq" owned by "public"."articles_rels"."id";

alter sequence "public"."categories_id_seq" owned by "public"."categories"."id";

alter sequence "public"."footer_id_seq" owned by "public"."footer"."id";

alter sequence "public"."footer_rels_id_seq" owned by "public"."footer_rels"."id";

alter sequence "public"."header_id_seq" owned by "public"."header"."id";

alter sequence "public"."header_rels_id_seq" owned by "public"."header_rels"."id";

alter sequence "public"."keywords_id_seq" owned by "public"."keywords"."id";

alter sequence "public"."media_id_seq" owned by "public"."media"."id";

alter sequence "public"."pages_id_seq" owned by "public"."pages"."id";

alter sequence "public"."payload_locked_documents_id_seq" owned by "public"."payload_locked_documents"."id";

alter sequence "public"."payload_locked_documents_rels_id_seq" owned by "public"."payload_locked_documents_rels"."id";

alter sequence "public"."payload_migrations_id_seq" owned by "public"."payload_migrations"."id";

alter sequence "public"."payload_preferences_id_seq" owned by "public"."payload_preferences"."id";

alter sequence "public"."payload_preferences_rels_id_seq" owned by "public"."payload_preferences_rels"."id";

alter sequence "public"."processed_urls_id_seq" owned by "public"."processed_urls"."id";

alter sequence "public"."rss_feeds_id_seq" owned by "public"."rss_feeds"."id";

alter sequence "public"."users_id_seq" owned by "public"."users"."id";

CREATE INDEX _articles_v_created_at_idx ON public._articles_v USING btree (created_at);

CREATE INDEX _articles_v_latest_idx ON public._articles_v USING btree (latest);

CREATE INDEX _articles_v_parent_idx ON public._articles_v USING btree (parent_id);

CREATE UNIQUE INDEX _articles_v_pkey ON public._articles_v USING btree (id);

CREATE INDEX _articles_v_rels_categories_id_idx ON public._articles_v_rels USING btree (categories_id);

CREATE INDEX _articles_v_rels_order_idx ON public._articles_v_rels USING btree ("order");

CREATE INDEX _articles_v_rels_parent_idx ON public._articles_v_rels USING btree (parent_id);

CREATE INDEX _articles_v_rels_path_idx ON public._articles_v_rels USING btree (path);

CREATE UNIQUE INDEX _articles_v_rels_pkey ON public._articles_v_rels USING btree (id);

CREATE INDEX _articles_v_updated_at_idx ON public._articles_v USING btree (updated_at);

CREATE INDEX _articles_v_version_english_tab_enhanced_key_insights_order_idx ON public._articles_v_version_english_tab_enhanced_key_insights USING btree (_order);

CREATE INDEX _articles_v_version_english_tab_enhanced_key_insights_parent_id ON public._articles_v_version_english_tab_enhanced_key_insights USING btree (_parent_id);

CREATE UNIQUE INDEX _articles_v_version_english_tab_enhanced_key_insights_pkey ON public._articles_v_version_english_tab_enhanced_key_insights USING btree (id);

CREATE INDEX _articles_v_version_english_tab_keywords_order_idx ON public._articles_v_version_english_tab_keywords USING btree (_order);

CREATE INDEX _articles_v_version_english_tab_keywords_parent_id_idx ON public._articles_v_version_english_tab_keywords USING btree (_parent_id);

CREATE UNIQUE INDEX _articles_v_version_english_tab_keywords_pkey ON public._articles_v_version_english_tab_keywords USING btree (id);

CREATE INDEX _articles_v_version_german_tab_german_key_insights_order_idx ON public._articles_v_version_german_tab_german_key_insights USING btree (_order);

CREATE INDEX _articles_v_version_german_tab_german_key_insights_parent_id_id ON public._articles_v_version_german_tab_german_key_insights USING btree (_parent_id);

CREATE UNIQUE INDEX _articles_v_version_german_tab_german_key_insights_pkey ON public._articles_v_version_german_tab_german_key_insights USING btree (id);

CREATE INDEX _articles_v_version_german_tab_german_keywords_order_idx ON public._articles_v_version_german_tab_german_keywords USING btree (_order);

CREATE INDEX _articles_v_version_german_tab_german_keywords_parent_id_idx ON public._articles_v_version_german_tab_german_keywords USING btree (_parent_id);

CREATE UNIQUE INDEX _articles_v_version_german_tab_german_keywords_pkey ON public._articles_v_version_german_tab_german_keywords USING btree (id);

CREATE INDEX _articles_v_version_meta_version_meta_image_idx ON public._articles_v USING btree (version_meta_image_id);

CREATE INDEX _articles_v_version_related_companies_order_idx ON public._articles_v_version_related_companies USING btree (_order);

CREATE INDEX _articles_v_version_related_companies_parent_id_idx ON public._articles_v_version_related_companies USING btree (_parent_id);

CREATE UNIQUE INDEX _articles_v_version_related_companies_pkey ON public._articles_v_version_related_companies USING btree (id);

CREATE INDEX _articles_v_version_sources_tab_version_sources_tab_source_feed ON public._articles_v USING btree (version_sources_tab_source_feed_id);

CREATE INDEX _articles_v_version_version__status_idx ON public._articles_v USING btree (version__status);

CREATE INDEX _articles_v_version_version_created_at_idx ON public._articles_v USING btree (version_created_at);

CREATE INDEX _articles_v_version_version_featured_image_idx ON public._articles_v USING btree (version_featured_image_id);

CREATE INDEX _articles_v_version_version_published_by_idx ON public._articles_v USING btree (version_published_by_id);

CREATE INDEX _articles_v_version_version_trending_idx ON public._articles_v USING btree (version_trending);

CREATE INDEX _articles_v_version_version_updated_at_idx ON public._articles_v USING btree (version_updated_at);

CREATE INDEX _pages_v_created_at_idx ON public._pages_v USING btree (created_at);

CREATE INDEX _pages_v_latest_idx ON public._pages_v USING btree (latest);

CREATE INDEX _pages_v_parent_idx ON public._pages_v USING btree (parent_id);

CREATE UNIQUE INDEX _pages_v_pkey ON public._pages_v USING btree (id);

CREATE INDEX _pages_v_updated_at_idx ON public._pages_v USING btree (updated_at);

CREATE INDEX _pages_v_version_meta_version_meta_image_idx ON public._pages_v USING btree (version_meta_image_id);

CREATE INDEX _pages_v_version_version__status_idx ON public._pages_v USING btree (version__status);

CREATE INDEX _pages_v_version_version_created_at_idx ON public._pages_v USING btree (version_created_at);

CREATE INDEX _pages_v_version_version_featured_image_idx ON public._pages_v USING btree (version_featured_image_id);

CREATE INDEX _pages_v_version_version_parent_idx ON public._pages_v USING btree (version_parent_id);

CREATE INDEX _pages_v_version_version_slug_idx ON public._pages_v USING btree (version_slug);

CREATE INDEX _pages_v_version_version_updated_at_idx ON public._pages_v USING btree (version_updated_at);

CREATE INDEX articles__status_idx ON public.articles USING btree (_status);

CREATE INDEX articles_created_at_idx ON public.articles USING btree (created_at);

CREATE INDEX articles_english_tab_enhanced_key_insights_order_idx ON public.articles_english_tab_enhanced_key_insights USING btree (_order);

CREATE INDEX articles_english_tab_enhanced_key_insights_parent_id_idx ON public.articles_english_tab_enhanced_key_insights USING btree (_parent_id);

CREATE UNIQUE INDEX articles_english_tab_enhanced_key_insights_pkey ON public.articles_english_tab_enhanced_key_insights USING btree (id);

CREATE INDEX articles_english_tab_keywords_order_idx ON public.articles_english_tab_keywords USING btree (_order);

CREATE INDEX articles_english_tab_keywords_parent_id_idx ON public.articles_english_tab_keywords USING btree (_parent_id);

CREATE UNIQUE INDEX articles_english_tab_keywords_pkey ON public.articles_english_tab_keywords USING btree (id);

CREATE INDEX articles_featured_image_idx ON public.articles USING btree (featured_image_id);

CREATE INDEX articles_german_tab_german_key_insights_order_idx ON public.articles_german_tab_german_key_insights USING btree (_order);

CREATE INDEX articles_german_tab_german_key_insights_parent_id_idx ON public.articles_german_tab_german_key_insights USING btree (_parent_id);

CREATE UNIQUE INDEX articles_german_tab_german_key_insights_pkey ON public.articles_german_tab_german_key_insights USING btree (id);

CREATE INDEX articles_german_tab_german_keywords_order_idx ON public.articles_german_tab_german_keywords USING btree (_order);

CREATE INDEX articles_german_tab_german_keywords_parent_id_idx ON public.articles_german_tab_german_keywords USING btree (_parent_id);

CREATE UNIQUE INDEX articles_german_tab_german_keywords_pkey ON public.articles_german_tab_german_keywords USING btree (id);

CREATE INDEX articles_meta_meta_image_idx ON public.articles USING btree (meta_image_id);

CREATE UNIQUE INDEX articles_pkey ON public.articles USING btree (id);

CREATE INDEX articles_published_by_idx ON public.articles USING btree (published_by_id);

CREATE INDEX articles_related_companies_order_idx ON public.articles_related_companies USING btree (_order);

CREATE INDEX articles_related_companies_parent_id_idx ON public.articles_related_companies USING btree (_parent_id);

CREATE UNIQUE INDEX articles_related_companies_pkey ON public.articles_related_companies USING btree (id);

CREATE INDEX articles_rels_categories_id_idx ON public.articles_rels USING btree (categories_id);

CREATE INDEX articles_rels_order_idx ON public.articles_rels USING btree ("order");

CREATE INDEX articles_rels_parent_idx ON public.articles_rels USING btree (parent_id);

CREATE INDEX articles_rels_path_idx ON public.articles_rels USING btree (path);

CREATE UNIQUE INDEX articles_rels_pkey ON public.articles_rels USING btree (id);

CREATE INDEX articles_sources_tab_sources_tab_source_feed_idx ON public.articles USING btree (sources_tab_source_feed_id);

CREATE INDEX articles_trending_idx ON public.articles USING btree (trending);

CREATE INDEX articles_updated_at_idx ON public.articles USING btree (updated_at);

CREATE INDEX categories_created_at_idx ON public.categories USING btree (created_at);

CREATE UNIQUE INDEX categories_pkey ON public.categories USING btree (id);

CREATE UNIQUE INDEX categories_slug_idx ON public.categories USING btree (slug);

CREATE INDEX categories_updated_at_idx ON public.categories USING btree (updated_at);

CREATE INDEX footer_legal_links_order_idx ON public.footer_legal_links USING btree (_order);

CREATE INDEX footer_legal_links_parent_id_idx ON public.footer_legal_links USING btree (_parent_id);

CREATE UNIQUE INDEX footer_legal_links_pkey ON public.footer_legal_links USING btree (id);

CREATE INDEX footer_logo_logo_image_idx ON public.footer USING btree (logo_image_id);

CREATE INDEX footer_navigation_sections_links_order_idx ON public.footer_navigation_sections_links USING btree (_order);

CREATE INDEX footer_navigation_sections_links_parent_id_idx ON public.footer_navigation_sections_links USING btree (_parent_id);

CREATE UNIQUE INDEX footer_navigation_sections_links_pkey ON public.footer_navigation_sections_links USING btree (id);

CREATE INDEX footer_navigation_sections_order_idx ON public.footer_navigation_sections USING btree (_order);

CREATE INDEX footer_navigation_sections_parent_id_idx ON public.footer_navigation_sections USING btree (_parent_id);

CREATE UNIQUE INDEX footer_navigation_sections_pkey ON public.footer_navigation_sections USING btree (id);

CREATE UNIQUE INDEX footer_pkey ON public.footer USING btree (id);

CREATE INDEX footer_rels_articles_id_idx ON public.footer_rels USING btree (articles_id);

CREATE INDEX footer_rels_categories_id_idx ON public.footer_rels USING btree (categories_id);

CREATE INDEX footer_rels_order_idx ON public.footer_rels USING btree ("order");

CREATE INDEX footer_rels_pages_id_idx ON public.footer_rels USING btree (pages_id);

CREATE INDEX footer_rels_parent_idx ON public.footer_rels USING btree (parent_id);

CREATE INDEX footer_rels_path_idx ON public.footer_rels USING btree (path);

CREATE UNIQUE INDEX footer_rels_pkey ON public.footer_rels USING btree (id);

CREATE INDEX footer_social_links_order_idx ON public.footer_social_links USING btree (_order);

CREATE INDEX footer_social_links_parent_id_idx ON public.footer_social_links USING btree (_parent_id);

CREATE UNIQUE INDEX footer_social_links_pkey ON public.footer_social_links USING btree (id);

CREATE INDEX header_nav_items_order_idx ON public.header_nav_items USING btree (_order);

CREATE INDEX header_nav_items_parent_id_idx ON public.header_nav_items USING btree (_parent_id);

CREATE UNIQUE INDEX header_nav_items_pkey ON public.header_nav_items USING btree (id);

CREATE UNIQUE INDEX header_pkey ON public.header USING btree (id);

CREATE INDEX header_rels_articles_id_idx ON public.header_rels USING btree (articles_id);

CREATE INDEX header_rels_categories_id_idx ON public.header_rels USING btree (categories_id);

CREATE INDEX header_rels_order_idx ON public.header_rels USING btree ("order");

CREATE INDEX header_rels_pages_id_idx ON public.header_rels USING btree (pages_id);

CREATE INDEX header_rels_parent_idx ON public.header_rels USING btree (parent_id);

CREATE INDEX header_rels_path_idx ON public.header_rels USING btree (path);

CREATE UNIQUE INDEX header_rels_pkey ON public.header_rels USING btree (id);

CREATE INDEX keywords_created_at_idx ON public.keywords USING btree (created_at);

CREATE UNIQUE INDEX keywords_keyword_idx ON public.keywords USING btree (keyword);

CREATE UNIQUE INDEX keywords_pkey ON public.keywords USING btree (id);

CREATE INDEX keywords_updated_at_idx ON public.keywords USING btree (updated_at);

CREATE INDEX media_created_at_idx ON public.media USING btree (created_at);

CREATE UNIQUE INDEX media_filename_idx ON public.media USING btree (filename);

CREATE UNIQUE INDEX media_pkey ON public.media USING btree (id);

CREATE INDEX media_sizes_card_sizes_card_filename_idx ON public.media USING btree (sizes_card_filename);

CREATE INDEX media_sizes_feature_sizes_feature_filename_idx ON public.media USING btree (sizes_feature_filename);

CREATE INDEX media_sizes_hero_sizes_hero_filename_idx ON public.media USING btree (sizes_hero_filename);

CREATE INDEX media_sizes_horizontal_sizes_horizontal_filename_idx ON public.media USING btree (sizes_horizontal_filename);

CREATE INDEX media_sizes_social_sizes_social_filename_idx ON public.media USING btree (sizes_social_filename);

CREATE INDEX media_sizes_thumbnail_sizes_thumbnail_filename_idx ON public.media USING btree (sizes_thumbnail_filename);

CREATE INDEX media_updated_at_idx ON public.media USING btree (updated_at);

CREATE INDEX pages__status_idx ON public.pages USING btree (_status);

CREATE INDEX pages_created_at_idx ON public.pages USING btree (created_at);

CREATE INDEX pages_featured_image_idx ON public.pages USING btree (featured_image_id);

CREATE INDEX pages_meta_meta_image_idx ON public.pages USING btree (meta_image_id);

CREATE INDEX pages_parent_idx ON public.pages USING btree (parent_id);

CREATE UNIQUE INDEX pages_pkey ON public.pages USING btree (id);

CREATE UNIQUE INDEX pages_slug_idx ON public.pages USING btree (slug);

CREATE INDEX pages_updated_at_idx ON public.pages USING btree (updated_at);

CREATE INDEX payload_locked_documents_created_at_idx ON public.payload_locked_documents USING btree (created_at);

CREATE INDEX payload_locked_documents_global_slug_idx ON public.payload_locked_documents USING btree (global_slug);

CREATE UNIQUE INDEX payload_locked_documents_pkey ON public.payload_locked_documents USING btree (id);

CREATE INDEX payload_locked_documents_rels_articles_id_idx ON public.payload_locked_documents_rels USING btree (articles_id);

CREATE INDEX payload_locked_documents_rels_categories_id_idx ON public.payload_locked_documents_rels USING btree (categories_id);

CREATE INDEX payload_locked_documents_rels_keywords_id_idx ON public.payload_locked_documents_rels USING btree (keywords_id);

CREATE INDEX payload_locked_documents_rels_media_id_idx ON public.payload_locked_documents_rels USING btree (media_id);

CREATE INDEX payload_locked_documents_rels_order_idx ON public.payload_locked_documents_rels USING btree ("order");

CREATE INDEX payload_locked_documents_rels_pages_id_idx ON public.payload_locked_documents_rels USING btree (pages_id);

CREATE INDEX payload_locked_documents_rels_parent_idx ON public.payload_locked_documents_rels USING btree (parent_id);

CREATE INDEX payload_locked_documents_rels_path_idx ON public.payload_locked_documents_rels USING btree (path);

CREATE UNIQUE INDEX payload_locked_documents_rels_pkey ON public.payload_locked_documents_rels USING btree (id);

CREATE INDEX payload_locked_documents_rels_processed_urls_id_idx ON public.payload_locked_documents_rels USING btree (processed_urls_id);

CREATE INDEX payload_locked_documents_rels_rss_feeds_id_idx ON public.payload_locked_documents_rels USING btree (rss_feeds_id);

CREATE INDEX payload_locked_documents_rels_users_id_idx ON public.payload_locked_documents_rels USING btree (users_id);

CREATE INDEX payload_locked_documents_updated_at_idx ON public.payload_locked_documents USING btree (updated_at);

CREATE INDEX payload_migrations_created_at_idx ON public.payload_migrations USING btree (created_at);

CREATE UNIQUE INDEX payload_migrations_pkey ON public.payload_migrations USING btree (id);

CREATE INDEX payload_migrations_updated_at_idx ON public.payload_migrations USING btree (updated_at);

CREATE INDEX payload_preferences_created_at_idx ON public.payload_preferences USING btree (created_at);

CREATE INDEX payload_preferences_key_idx ON public.payload_preferences USING btree (key);

CREATE UNIQUE INDEX payload_preferences_pkey ON public.payload_preferences USING btree (id);

CREATE INDEX payload_preferences_rels_order_idx ON public.payload_preferences_rels USING btree ("order");

CREATE INDEX payload_preferences_rels_parent_idx ON public.payload_preferences_rels USING btree (parent_id);

CREATE INDEX payload_preferences_rels_path_idx ON public.payload_preferences_rels USING btree (path);

CREATE UNIQUE INDEX payload_preferences_rels_pkey ON public.payload_preferences_rels USING btree (id);

CREATE INDEX payload_preferences_rels_users_id_idx ON public.payload_preferences_rels USING btree (users_id);

CREATE INDEX payload_preferences_updated_at_idx ON public.payload_preferences USING btree (updated_at);

CREATE INDEX processed_urls_article_id_idx ON public.processed_urls USING btree (article_id_id);

CREATE INDEX processed_urls_created_at_idx ON public.processed_urls USING btree (created_at);

CREATE INDEX processed_urls_feed_id_idx ON public.processed_urls USING btree (feed_id_id);

CREATE UNIQUE INDEX processed_urls_pkey ON public.processed_urls USING btree (id);

CREATE INDEX processed_urls_updated_at_idx ON public.processed_urls USING btree (updated_at);

CREATE UNIQUE INDEX processed_urls_url_idx ON public.processed_urls USING btree (url);

CREATE INDEX rss_feeds_created_at_idx ON public.rss_feeds USING btree (created_at);

CREATE INDEX rss_feeds_firecrawl_options_exclude_tags_order_idx ON public.rss_feeds_firecrawl_options_exclude_tags USING btree (_order);

CREATE INDEX rss_feeds_firecrawl_options_exclude_tags_parent_id_idx ON public.rss_feeds_firecrawl_options_exclude_tags USING btree (_parent_id);

CREATE UNIQUE INDEX rss_feeds_firecrawl_options_exclude_tags_pkey ON public.rss_feeds_firecrawl_options_exclude_tags USING btree (id);

CREATE INDEX rss_feeds_firecrawl_options_include_tags_order_idx ON public.rss_feeds_firecrawl_options_include_tags USING btree (_order);

CREATE INDEX rss_feeds_firecrawl_options_include_tags_parent_id_idx ON public.rss_feeds_firecrawl_options_include_tags USING btree (_parent_id);

CREATE UNIQUE INDEX rss_feeds_firecrawl_options_include_tags_pkey ON public.rss_feeds_firecrawl_options_include_tags USING btree (id);

CREATE INDEX rss_feeds_keyword_filtering_custom_keywords_order_idx ON public.rss_feeds_keyword_filtering_custom_keywords USING btree (_order);

CREATE INDEX rss_feeds_keyword_filtering_custom_keywords_parent_id_idx ON public.rss_feeds_keyword_filtering_custom_keywords USING btree (_parent_id);

CREATE UNIQUE INDEX rss_feeds_keyword_filtering_custom_keywords_pkey ON public.rss_feeds_keyword_filtering_custom_keywords USING btree (id);

CREATE UNIQUE INDEX rss_feeds_pkey ON public.rss_feeds USING btree (id);

CREATE INDEX rss_feeds_updated_at_idx ON public.rss_feeds USING btree (updated_at);

CREATE UNIQUE INDEX rss_feeds_url_idx ON public.rss_feeds USING btree (url);

CREATE INDEX users_created_at_idx ON public.users USING btree (created_at);

CREATE UNIQUE INDEX users_email_idx ON public.users USING btree (email);

CREATE UNIQUE INDEX users_pkey ON public.users USING btree (id);

CREATE INDEX users_updated_at_idx ON public.users USING btree (updated_at);

alter table "public"."_articles_v" add constraint "_articles_v_pkey" PRIMARY KEY using index "_articles_v_pkey";

alter table "public"."_articles_v_rels" add constraint "_articles_v_rels_pkey" PRIMARY KEY using index "_articles_v_rels_pkey";

alter table "public"."_articles_v_version_english_tab_enhanced_key_insights" add constraint "_articles_v_version_english_tab_enhanced_key_insights_pkey" PRIMARY KEY using index "_articles_v_version_english_tab_enhanced_key_insights_pkey";

alter table "public"."_articles_v_version_english_tab_keywords" add constraint "_articles_v_version_english_tab_keywords_pkey" PRIMARY KEY using index "_articles_v_version_english_tab_keywords_pkey";

alter table "public"."_articles_v_version_german_tab_german_key_insights" add constraint "_articles_v_version_german_tab_german_key_insights_pkey" PRIMARY KEY using index "_articles_v_version_german_tab_german_key_insights_pkey";

alter table "public"."_articles_v_version_german_tab_german_keywords" add constraint "_articles_v_version_german_tab_german_keywords_pkey" PRIMARY KEY using index "_articles_v_version_german_tab_german_keywords_pkey";

alter table "public"."_articles_v_version_related_companies" add constraint "_articles_v_version_related_companies_pkey" PRIMARY KEY using index "_articles_v_version_related_companies_pkey";

alter table "public"."_pages_v" add constraint "_pages_v_pkey" PRIMARY KEY using index "_pages_v_pkey";

alter table "public"."articles" add constraint "articles_pkey" PRIMARY KEY using index "articles_pkey";

alter table "public"."articles_english_tab_enhanced_key_insights" add constraint "articles_english_tab_enhanced_key_insights_pkey" PRIMARY KEY using index "articles_english_tab_enhanced_key_insights_pkey";

alter table "public"."articles_english_tab_keywords" add constraint "articles_english_tab_keywords_pkey" PRIMARY KEY using index "articles_english_tab_keywords_pkey";

alter table "public"."articles_german_tab_german_key_insights" add constraint "articles_german_tab_german_key_insights_pkey" PRIMARY KEY using index "articles_german_tab_german_key_insights_pkey";

alter table "public"."articles_german_tab_german_keywords" add constraint "articles_german_tab_german_keywords_pkey" PRIMARY KEY using index "articles_german_tab_german_keywords_pkey";

alter table "public"."articles_related_companies" add constraint "articles_related_companies_pkey" PRIMARY KEY using index "articles_related_companies_pkey";

alter table "public"."articles_rels" add constraint "articles_rels_pkey" PRIMARY KEY using index "articles_rels_pkey";

alter table "public"."categories" add constraint "categories_pkey" PRIMARY KEY using index "categories_pkey";

alter table "public"."footer" add constraint "footer_pkey" PRIMARY KEY using index "footer_pkey";

alter table "public"."footer_legal_links" add constraint "footer_legal_links_pkey" PRIMARY KEY using index "footer_legal_links_pkey";

alter table "public"."footer_navigation_sections" add constraint "footer_navigation_sections_pkey" PRIMARY KEY using index "footer_navigation_sections_pkey";

alter table "public"."footer_navigation_sections_links" add constraint "footer_navigation_sections_links_pkey" PRIMARY KEY using index "footer_navigation_sections_links_pkey";

alter table "public"."footer_rels" add constraint "footer_rels_pkey" PRIMARY KEY using index "footer_rels_pkey";

alter table "public"."footer_social_links" add constraint "footer_social_links_pkey" PRIMARY KEY using index "footer_social_links_pkey";

alter table "public"."header" add constraint "header_pkey" PRIMARY KEY using index "header_pkey";

alter table "public"."header_nav_items" add constraint "header_nav_items_pkey" PRIMARY KEY using index "header_nav_items_pkey";

alter table "public"."header_rels" add constraint "header_rels_pkey" PRIMARY KEY using index "header_rels_pkey";

alter table "public"."keywords" add constraint "keywords_pkey" PRIMARY KEY using index "keywords_pkey";

alter table "public"."media" add constraint "media_pkey" PRIMARY KEY using index "media_pkey";

alter table "public"."pages" add constraint "pages_pkey" PRIMARY KEY using index "pages_pkey";

alter table "public"."payload_locked_documents" add constraint "payload_locked_documents_pkey" PRIMARY KEY using index "payload_locked_documents_pkey";

alter table "public"."payload_locked_documents_rels" add constraint "payload_locked_documents_rels_pkey" PRIMARY KEY using index "payload_locked_documents_rels_pkey";

alter table "public"."payload_migrations" add constraint "payload_migrations_pkey" PRIMARY KEY using index "payload_migrations_pkey";

alter table "public"."payload_preferences" add constraint "payload_preferences_pkey" PRIMARY KEY using index "payload_preferences_pkey";

alter table "public"."payload_preferences_rels" add constraint "payload_preferences_rels_pkey" PRIMARY KEY using index "payload_preferences_rels_pkey";

alter table "public"."processed_urls" add constraint "processed_urls_pkey" PRIMARY KEY using index "processed_urls_pkey";

alter table "public"."rss_feeds" add constraint "rss_feeds_pkey" PRIMARY KEY using index "rss_feeds_pkey";

alter table "public"."rss_feeds_firecrawl_options_exclude_tags" add constraint "rss_feeds_firecrawl_options_exclude_tags_pkey" PRIMARY KEY using index "rss_feeds_firecrawl_options_exclude_tags_pkey";

alter table "public"."rss_feeds_firecrawl_options_include_tags" add constraint "rss_feeds_firecrawl_options_include_tags_pkey" PRIMARY KEY using index "rss_feeds_firecrawl_options_include_tags_pkey";

alter table "public"."rss_feeds_keyword_filtering_custom_keywords" add constraint "rss_feeds_keyword_filtering_custom_keywords_pkey" PRIMARY KEY using index "rss_feeds_keyword_filtering_custom_keywords_pkey";

alter table "public"."users" add constraint "users_pkey" PRIMARY KEY using index "users_pkey";

alter table "public"."_articles_v" add constraint "_articles_v_parent_id_articles_id_fk" FOREIGN KEY (parent_id) REFERENCES articles(id) ON DELETE SET NULL not valid;

alter table "public"."_articles_v" validate constraint "_articles_v_parent_id_articles_id_fk";

alter table "public"."_articles_v" add constraint "_articles_v_version_featured_image_id_media_id_fk" FOREIGN KEY (version_featured_image_id) REFERENCES media(id) ON DELETE SET NULL not valid;

alter table "public"."_articles_v" validate constraint "_articles_v_version_featured_image_id_media_id_fk";

alter table "public"."_articles_v" add constraint "_articles_v_version_meta_image_id_media_id_fk" FOREIGN KEY (version_meta_image_id) REFERENCES media(id) ON DELETE SET NULL not valid;

alter table "public"."_articles_v" validate constraint "_articles_v_version_meta_image_id_media_id_fk";

alter table "public"."_articles_v" add constraint "_articles_v_version_published_by_id_users_id_fk" FOREIGN KEY (version_published_by_id) REFERENCES users(id) ON DELETE SET NULL not valid;

alter table "public"."_articles_v" validate constraint "_articles_v_version_published_by_id_users_id_fk";

alter table "public"."_articles_v" add constraint "_articles_v_version_sources_tab_source_feed_id_rss_feeds_id_fk" FOREIGN KEY (version_sources_tab_source_feed_id) REFERENCES rss_feeds(id) ON DELETE SET NULL not valid;

alter table "public"."_articles_v" validate constraint "_articles_v_version_sources_tab_source_feed_id_rss_feeds_id_fk";

alter table "public"."_articles_v_rels" add constraint "_articles_v_rels_categories_fk" FOREIGN KEY (categories_id) REFERENCES categories(id) ON DELETE CASCADE not valid;

alter table "public"."_articles_v_rels" validate constraint "_articles_v_rels_categories_fk";

alter table "public"."_articles_v_rels" add constraint "_articles_v_rels_parent_fk" FOREIGN KEY (parent_id) REFERENCES _articles_v(id) ON DELETE CASCADE not valid;

alter table "public"."_articles_v_rels" validate constraint "_articles_v_rels_parent_fk";

alter table "public"."_articles_v_version_english_tab_enhanced_key_insights" add constraint "_articles_v_version_english_tab_enhanced_key_insights_parent_id" FOREIGN KEY using index "_articles_v_version_english_tab_enhanced_key_insights_parent_id";

alter table "public"."_articles_v_version_english_tab_keywords" add constraint "_articles_v_version_english_tab_keywords_parent_id_fk" FOREIGN KEY (_parent_id) REFERENCES _articles_v(id) ON DELETE CASCADE not valid;

alter table "public"."_articles_v_version_english_tab_keywords" validate constraint "_articles_v_version_english_tab_keywords_parent_id_fk";

alter table "public"."_articles_v_version_german_tab_german_key_insights" add constraint "_articles_v_version_german_tab_german_key_insights_parent_id_fk" FOREIGN KEY (_parent_id) REFERENCES _articles_v(id) ON DELETE CASCADE not valid;

alter table "public"."_articles_v_version_german_tab_german_key_insights" validate constraint "_articles_v_version_german_tab_german_key_insights_parent_id_fk";

alter table "public"."_articles_v_version_german_tab_german_keywords" add constraint "_articles_v_version_german_tab_german_keywords_parent_id_fk" FOREIGN KEY (_parent_id) REFERENCES _articles_v(id) ON DELETE CASCADE not valid;

alter table "public"."_articles_v_version_german_tab_german_keywords" validate constraint "_articles_v_version_german_tab_german_keywords_parent_id_fk";

alter table "public"."_articles_v_version_related_companies" add constraint "_articles_v_version_related_companies_parent_id_fk" FOREIGN KEY (_parent_id) REFERENCES _articles_v(id) ON DELETE CASCADE not valid;

alter table "public"."_articles_v_version_related_companies" validate constraint "_articles_v_version_related_companies_parent_id_fk";

alter table "public"."_pages_v" add constraint "_pages_v_parent_id_pages_id_fk" FOREIGN KEY (parent_id) REFERENCES pages(id) ON DELETE SET NULL not valid;

alter table "public"."_pages_v" validate constraint "_pages_v_parent_id_pages_id_fk";

alter table "public"."_pages_v" add constraint "_pages_v_version_featured_image_id_media_id_fk" FOREIGN KEY (version_featured_image_id) REFERENCES media(id) ON DELETE SET NULL not valid;

alter table "public"."_pages_v" validate constraint "_pages_v_version_featured_image_id_media_id_fk";

alter table "public"."_pages_v" add constraint "_pages_v_version_meta_image_id_media_id_fk" FOREIGN KEY (version_meta_image_id) REFERENCES media(id) ON DELETE SET NULL not valid;

alter table "public"."_pages_v" validate constraint "_pages_v_version_meta_image_id_media_id_fk";

alter table "public"."_pages_v" add constraint "_pages_v_version_parent_id_pages_id_fk" FOREIGN KEY (version_parent_id) REFERENCES pages(id) ON DELETE SET NULL not valid;

alter table "public"."_pages_v" validate constraint "_pages_v_version_parent_id_pages_id_fk";

alter table "public"."articles" add constraint "articles_featured_image_id_media_id_fk" FOREIGN KEY (featured_image_id) REFERENCES media(id) ON DELETE SET NULL not valid;

alter table "public"."articles" validate constraint "articles_featured_image_id_media_id_fk";

alter table "public"."articles" add constraint "articles_meta_image_id_media_id_fk" FOREIGN KEY (meta_image_id) REFERENCES media(id) ON DELETE SET NULL not valid;

alter table "public"."articles" validate constraint "articles_meta_image_id_media_id_fk";

alter table "public"."articles" add constraint "articles_published_by_id_users_id_fk" FOREIGN KEY (published_by_id) REFERENCES users(id) ON DELETE SET NULL not valid;

alter table "public"."articles" validate constraint "articles_published_by_id_users_id_fk";

alter table "public"."articles" add constraint "articles_sources_tab_source_feed_id_rss_feeds_id_fk" FOREIGN KEY (sources_tab_source_feed_id) REFERENCES rss_feeds(id) ON DELETE SET NULL not valid;

alter table "public"."articles" validate constraint "articles_sources_tab_source_feed_id_rss_feeds_id_fk";

alter table "public"."articles_english_tab_enhanced_key_insights" add constraint "articles_english_tab_enhanced_key_insights_parent_id_fk" FOREIGN KEY (_parent_id) REFERENCES articles(id) ON DELETE CASCADE not valid;

alter table "public"."articles_english_tab_enhanced_key_insights" validate constraint "articles_english_tab_enhanced_key_insights_parent_id_fk";

alter table "public"."articles_english_tab_keywords" add constraint "articles_english_tab_keywords_parent_id_fk" FOREIGN KEY (_parent_id) REFERENCES articles(id) ON DELETE CASCADE not valid;

alter table "public"."articles_english_tab_keywords" validate constraint "articles_english_tab_keywords_parent_id_fk";

alter table "public"."articles_german_tab_german_key_insights" add constraint "articles_german_tab_german_key_insights_parent_id_fk" FOREIGN KEY (_parent_id) REFERENCES articles(id) ON DELETE CASCADE not valid;

alter table "public"."articles_german_tab_german_key_insights" validate constraint "articles_german_tab_german_key_insights_parent_id_fk";

alter table "public"."articles_german_tab_german_keywords" add constraint "articles_german_tab_german_keywords_parent_id_fk" FOREIGN KEY (_parent_id) REFERENCES articles(id) ON DELETE CASCADE not valid;

alter table "public"."articles_german_tab_german_keywords" validate constraint "articles_german_tab_german_keywords_parent_id_fk";

alter table "public"."articles_related_companies" add constraint "articles_related_companies_parent_id_fk" FOREIGN KEY (_parent_id) REFERENCES articles(id) ON DELETE CASCADE not valid;

alter table "public"."articles_related_companies" validate constraint "articles_related_companies_parent_id_fk";

alter table "public"."articles_rels" add constraint "articles_rels_categories_fk" FOREIGN KEY (categories_id) REFERENCES categories(id) ON DELETE CASCADE not valid;

alter table "public"."articles_rels" validate constraint "articles_rels_categories_fk";

alter table "public"."articles_rels" add constraint "articles_rels_parent_fk" FOREIGN KEY (parent_id) REFERENCES articles(id) ON DELETE CASCADE not valid;

alter table "public"."articles_rels" validate constraint "articles_rels_parent_fk";

alter table "public"."footer" add constraint "footer_logo_image_id_media_id_fk" FOREIGN KEY (logo_image_id) REFERENCES media(id) ON DELETE SET NULL not valid;

alter table "public"."footer" validate constraint "footer_logo_image_id_media_id_fk";

alter table "public"."footer_legal_links" add constraint "footer_legal_links_parent_id_fk" FOREIGN KEY (_parent_id) REFERENCES footer(id) ON DELETE CASCADE not valid;

alter table "public"."footer_legal_links" validate constraint "footer_legal_links_parent_id_fk";

alter table "public"."footer_navigation_sections" add constraint "footer_navigation_sections_parent_id_fk" FOREIGN KEY (_parent_id) REFERENCES footer(id) ON DELETE CASCADE not valid;

alter table "public"."footer_navigation_sections" validate constraint "footer_navigation_sections_parent_id_fk";

alter table "public"."footer_navigation_sections_links" add constraint "footer_navigation_sections_links_parent_id_fk" FOREIGN KEY (_parent_id) REFERENCES footer_navigation_sections(id) ON DELETE CASCADE not valid;

alter table "public"."footer_navigation_sections_links" validate constraint "footer_navigation_sections_links_parent_id_fk";

alter table "public"."footer_rels" add constraint "footer_rels_articles_fk" FOREIGN KEY (articles_id) REFERENCES articles(id) ON DELETE CASCADE not valid;

alter table "public"."footer_rels" validate constraint "footer_rels_articles_fk";

alter table "public"."footer_rels" add constraint "footer_rels_categories_fk" FOREIGN KEY (categories_id) REFERENCES categories(id) ON DELETE CASCADE not valid;

alter table "public"."footer_rels" validate constraint "footer_rels_categories_fk";

alter table "public"."footer_rels" add constraint "footer_rels_pages_fk" FOREIGN KEY (pages_id) REFERENCES pages(id) ON DELETE CASCADE not valid;

alter table "public"."footer_rels" validate constraint "footer_rels_pages_fk";

alter table "public"."footer_rels" add constraint "footer_rels_parent_fk" FOREIGN KEY (parent_id) REFERENCES footer(id) ON DELETE CASCADE not valid;

alter table "public"."footer_rels" validate constraint "footer_rels_parent_fk";

alter table "public"."footer_social_links" add constraint "footer_social_links_parent_id_fk" FOREIGN KEY (_parent_id) REFERENCES footer(id) ON DELETE CASCADE not valid;

alter table "public"."footer_social_links" validate constraint "footer_social_links_parent_id_fk";

alter table "public"."header_nav_items" add constraint "header_nav_items_parent_id_fk" FOREIGN KEY (_parent_id) REFERENCES header(id) ON DELETE CASCADE not valid;

alter table "public"."header_nav_items" validate constraint "header_nav_items_parent_id_fk";

alter table "public"."header_rels" add constraint "header_rels_articles_fk" FOREIGN KEY (articles_id) REFERENCES articles(id) ON DELETE CASCADE not valid;

alter table "public"."header_rels" validate constraint "header_rels_articles_fk";

alter table "public"."header_rels" add constraint "header_rels_categories_fk" FOREIGN KEY (categories_id) REFERENCES categories(id) ON DELETE CASCADE not valid;

alter table "public"."header_rels" validate constraint "header_rels_categories_fk";

alter table "public"."header_rels" add constraint "header_rels_pages_fk" FOREIGN KEY (pages_id) REFERENCES pages(id) ON DELETE CASCADE not valid;

alter table "public"."header_rels" validate constraint "header_rels_pages_fk";

alter table "public"."header_rels" add constraint "header_rels_parent_fk" FOREIGN KEY (parent_id) REFERENCES header(id) ON DELETE CASCADE not valid;

alter table "public"."header_rels" validate constraint "header_rels_parent_fk";

alter table "public"."pages" add constraint "pages_featured_image_id_media_id_fk" FOREIGN KEY (featured_image_id) REFERENCES media(id) ON DELETE SET NULL not valid;

alter table "public"."pages" validate constraint "pages_featured_image_id_media_id_fk";

alter table "public"."pages" add constraint "pages_meta_image_id_media_id_fk" FOREIGN KEY (meta_image_id) REFERENCES media(id) ON DELETE SET NULL not valid;

alter table "public"."pages" validate constraint "pages_meta_image_id_media_id_fk";

alter table "public"."pages" add constraint "pages_parent_id_pages_id_fk" FOREIGN KEY (parent_id) REFERENCES pages(id) ON DELETE SET NULL not valid;

alter table "public"."pages" validate constraint "pages_parent_id_pages_id_fk";

alter table "public"."payload_locked_documents_rels" add constraint "payload_locked_documents_rels_articles_fk" FOREIGN KEY (articles_id) REFERENCES articles(id) ON DELETE CASCADE not valid;

alter table "public"."payload_locked_documents_rels" validate constraint "payload_locked_documents_rels_articles_fk";

alter table "public"."payload_locked_documents_rels" add constraint "payload_locked_documents_rels_categories_fk" FOREIGN KEY (categories_id) REFERENCES categories(id) ON DELETE CASCADE not valid;

alter table "public"."payload_locked_documents_rels" validate constraint "payload_locked_documents_rels_categories_fk";

alter table "public"."payload_locked_documents_rels" add constraint "payload_locked_documents_rels_keywords_fk" FOREIGN KEY (keywords_id) REFERENCES keywords(id) ON DELETE CASCADE not valid;

alter table "public"."payload_locked_documents_rels" validate constraint "payload_locked_documents_rels_keywords_fk";

alter table "public"."payload_locked_documents_rels" add constraint "payload_locked_documents_rels_media_fk" FOREIGN KEY (media_id) REFERENCES media(id) ON DELETE CASCADE not valid;

alter table "public"."payload_locked_documents_rels" validate constraint "payload_locked_documents_rels_media_fk";

alter table "public"."payload_locked_documents_rels" add constraint "payload_locked_documents_rels_pages_fk" FOREIGN KEY (pages_id) REFERENCES pages(id) ON DELETE CASCADE not valid;

alter table "public"."payload_locked_documents_rels" validate constraint "payload_locked_documents_rels_pages_fk";

alter table "public"."payload_locked_documents_rels" add constraint "payload_locked_documents_rels_parent_fk" FOREIGN KEY (parent_id) REFERENCES payload_locked_documents(id) ON DELETE CASCADE not valid;

alter table "public"."payload_locked_documents_rels" validate constraint "payload_locked_documents_rels_parent_fk";

alter table "public"."payload_locked_documents_rels" add constraint "payload_locked_documents_rels_processed_urls_fk" FOREIGN KEY (processed_urls_id) REFERENCES processed_urls(id) ON DELETE CASCADE not valid;

alter table "public"."payload_locked_documents_rels" validate constraint "payload_locked_documents_rels_processed_urls_fk";

alter table "public"."payload_locked_documents_rels" add constraint "payload_locked_documents_rels_rss_feeds_fk" FOREIGN KEY (rss_feeds_id) REFERENCES rss_feeds(id) ON DELETE CASCADE not valid;

alter table "public"."payload_locked_documents_rels" validate constraint "payload_locked_documents_rels_rss_feeds_fk";

alter table "public"."payload_locked_documents_rels" add constraint "payload_locked_documents_rels_users_fk" FOREIGN KEY (users_id) REFERENCES users(id) ON DELETE CASCADE not valid;

alter table "public"."payload_locked_documents_rels" validate constraint "payload_locked_documents_rels_users_fk";

alter table "public"."payload_preferences_rels" add constraint "payload_preferences_rels_parent_fk" FOREIGN KEY (parent_id) REFERENCES payload_preferences(id) ON DELETE CASCADE not valid;

alter table "public"."payload_preferences_rels" validate constraint "payload_preferences_rels_parent_fk";

alter table "public"."payload_preferences_rels" add constraint "payload_preferences_rels_users_fk" FOREIGN KEY (users_id) REFERENCES users(id) ON DELETE CASCADE not valid;

alter table "public"."payload_preferences_rels" validate constraint "payload_preferences_rels_users_fk";

alter table "public"."processed_urls" add constraint "processed_urls_article_id_id_articles_id_fk" FOREIGN KEY (article_id_id) REFERENCES articles(id) ON DELETE SET NULL not valid;

alter table "public"."processed_urls" validate constraint "processed_urls_article_id_id_articles_id_fk";

alter table "public"."processed_urls" add constraint "processed_urls_feed_id_id_rss_feeds_id_fk" FOREIGN KEY (feed_id_id) REFERENCES rss_feeds(id) ON DELETE SET NULL not valid;

alter table "public"."processed_urls" validate constraint "processed_urls_feed_id_id_rss_feeds_id_fk";

alter table "public"."rss_feeds_firecrawl_options_exclude_tags" add constraint "rss_feeds_firecrawl_options_exclude_tags_parent_id_fk" FOREIGN KEY (_parent_id) REFERENCES rss_feeds(id) ON DELETE CASCADE not valid;

alter table "public"."rss_feeds_firecrawl_options_exclude_tags" validate constraint "rss_feeds_firecrawl_options_exclude_tags_parent_id_fk";

alter table "public"."rss_feeds_firecrawl_options_include_tags" add constraint "rss_feeds_firecrawl_options_include_tags_parent_id_fk" FOREIGN KEY (_parent_id) REFERENCES rss_feeds(id) ON DELETE CASCADE not valid;

alter table "public"."rss_feeds_firecrawl_options_include_tags" validate constraint "rss_feeds_firecrawl_options_include_tags_parent_id_fk";

alter table "public"."rss_feeds_keyword_filtering_custom_keywords" add constraint "rss_feeds_keyword_filtering_custom_keywords_parent_id_fk" FOREIGN KEY (_parent_id) REFERENCES rss_feeds(id) ON DELETE CASCADE not valid;

alter table "public"."rss_feeds_keyword_filtering_custom_keywords" validate constraint "rss_feeds_keyword_filtering_custom_keywords_parent_id_fk";

grant delete on table "public"."_articles_v" to "anon";

grant insert on table "public"."_articles_v" to "anon";

grant references on table "public"."_articles_v" to "anon";

grant select on table "public"."_articles_v" to "anon";

grant trigger on table "public"."_articles_v" to "anon";

grant truncate on table "public"."_articles_v" to "anon";

grant update on table "public"."_articles_v" to "anon";

grant delete on table "public"."_articles_v" to "authenticated";

grant insert on table "public"."_articles_v" to "authenticated";

grant references on table "public"."_articles_v" to "authenticated";

grant select on table "public"."_articles_v" to "authenticated";

grant trigger on table "public"."_articles_v" to "authenticated";

grant truncate on table "public"."_articles_v" to "authenticated";

grant update on table "public"."_articles_v" to "authenticated";

grant delete on table "public"."_articles_v" to "service_role";

grant insert on table "public"."_articles_v" to "service_role";

grant references on table "public"."_articles_v" to "service_role";

grant select on table "public"."_articles_v" to "service_role";

grant trigger on table "public"."_articles_v" to "service_role";

grant truncate on table "public"."_articles_v" to "service_role";

grant update on table "public"."_articles_v" to "service_role";

grant delete on table "public"."_articles_v_rels" to "anon";

grant insert on table "public"."_articles_v_rels" to "anon";

grant references on table "public"."_articles_v_rels" to "anon";

grant select on table "public"."_articles_v_rels" to "anon";

grant trigger on table "public"."_articles_v_rels" to "anon";

grant truncate on table "public"."_articles_v_rels" to "anon";

grant update on table "public"."_articles_v_rels" to "anon";

grant delete on table "public"."_articles_v_rels" to "authenticated";

grant insert on table "public"."_articles_v_rels" to "authenticated";

grant references on table "public"."_articles_v_rels" to "authenticated";

grant select on table "public"."_articles_v_rels" to "authenticated";

grant trigger on table "public"."_articles_v_rels" to "authenticated";

grant truncate on table "public"."_articles_v_rels" to "authenticated";

grant update on table "public"."_articles_v_rels" to "authenticated";

grant delete on table "public"."_articles_v_rels" to "service_role";

grant insert on table "public"."_articles_v_rels" to "service_role";

grant references on table "public"."_articles_v_rels" to "service_role";

grant select on table "public"."_articles_v_rels" to "service_role";

grant trigger on table "public"."_articles_v_rels" to "service_role";

grant truncate on table "public"."_articles_v_rels" to "service_role";

grant update on table "public"."_articles_v_rels" to "service_role";

grant delete on table "public"."_articles_v_version_english_tab_enhanced_key_insights" to "anon";

grant insert on table "public"."_articles_v_version_english_tab_enhanced_key_insights" to "anon";

grant references on table "public"."_articles_v_version_english_tab_enhanced_key_insights" to "anon";

grant select on table "public"."_articles_v_version_english_tab_enhanced_key_insights" to "anon";

grant trigger on table "public"."_articles_v_version_english_tab_enhanced_key_insights" to "anon";

grant truncate on table "public"."_articles_v_version_english_tab_enhanced_key_insights" to "anon";

grant update on table "public"."_articles_v_version_english_tab_enhanced_key_insights" to "anon";

grant delete on table "public"."_articles_v_version_english_tab_enhanced_key_insights" to "authenticated";

grant insert on table "public"."_articles_v_version_english_tab_enhanced_key_insights" to "authenticated";

grant references on table "public"."_articles_v_version_english_tab_enhanced_key_insights" to "authenticated";

grant select on table "public"."_articles_v_version_english_tab_enhanced_key_insights" to "authenticated";

grant trigger on table "public"."_articles_v_version_english_tab_enhanced_key_insights" to "authenticated";

grant truncate on table "public"."_articles_v_version_english_tab_enhanced_key_insights" to "authenticated";

grant update on table "public"."_articles_v_version_english_tab_enhanced_key_insights" to "authenticated";

grant delete on table "public"."_articles_v_version_english_tab_enhanced_key_insights" to "service_role";

grant insert on table "public"."_articles_v_version_english_tab_enhanced_key_insights" to "service_role";

grant references on table "public"."_articles_v_version_english_tab_enhanced_key_insights" to "service_role";

grant select on table "public"."_articles_v_version_english_tab_enhanced_key_insights" to "service_role";

grant trigger on table "public"."_articles_v_version_english_tab_enhanced_key_insights" to "service_role";

grant truncate on table "public"."_articles_v_version_english_tab_enhanced_key_insights" to "service_role";

grant update on table "public"."_articles_v_version_english_tab_enhanced_key_insights" to "service_role";

grant delete on table "public"."_articles_v_version_english_tab_keywords" to "anon";

grant insert on table "public"."_articles_v_version_english_tab_keywords" to "anon";

grant references on table "public"."_articles_v_version_english_tab_keywords" to "anon";

grant select on table "public"."_articles_v_version_english_tab_keywords" to "anon";

grant trigger on table "public"."_articles_v_version_english_tab_keywords" to "anon";

grant truncate on table "public"."_articles_v_version_english_tab_keywords" to "anon";

grant update on table "public"."_articles_v_version_english_tab_keywords" to "anon";

grant delete on table "public"."_articles_v_version_english_tab_keywords" to "authenticated";

grant insert on table "public"."_articles_v_version_english_tab_keywords" to "authenticated";

grant references on table "public"."_articles_v_version_english_tab_keywords" to "authenticated";

grant select on table "public"."_articles_v_version_english_tab_keywords" to "authenticated";

grant trigger on table "public"."_articles_v_version_english_tab_keywords" to "authenticated";

grant truncate on table "public"."_articles_v_version_english_tab_keywords" to "authenticated";

grant update on table "public"."_articles_v_version_english_tab_keywords" to "authenticated";

grant delete on table "public"."_articles_v_version_english_tab_keywords" to "service_role";

grant insert on table "public"."_articles_v_version_english_tab_keywords" to "service_role";

grant references on table "public"."_articles_v_version_english_tab_keywords" to "service_role";

grant select on table "public"."_articles_v_version_english_tab_keywords" to "service_role";

grant trigger on table "public"."_articles_v_version_english_tab_keywords" to "service_role";

grant truncate on table "public"."_articles_v_version_english_tab_keywords" to "service_role";

grant update on table "public"."_articles_v_version_english_tab_keywords" to "service_role";

grant delete on table "public"."_articles_v_version_german_tab_german_key_insights" to "anon";

grant insert on table "public"."_articles_v_version_german_tab_german_key_insights" to "anon";

grant references on table "public"."_articles_v_version_german_tab_german_key_insights" to "anon";

grant select on table "public"."_articles_v_version_german_tab_german_key_insights" to "anon";

grant trigger on table "public"."_articles_v_version_german_tab_german_key_insights" to "anon";

grant truncate on table "public"."_articles_v_version_german_tab_german_key_insights" to "anon";

grant update on table "public"."_articles_v_version_german_tab_german_key_insights" to "anon";

grant delete on table "public"."_articles_v_version_german_tab_german_key_insights" to "authenticated";

grant insert on table "public"."_articles_v_version_german_tab_german_key_insights" to "authenticated";

grant references on table "public"."_articles_v_version_german_tab_german_key_insights" to "authenticated";

grant select on table "public"."_articles_v_version_german_tab_german_key_insights" to "authenticated";

grant trigger on table "public"."_articles_v_version_german_tab_german_key_insights" to "authenticated";

grant truncate on table "public"."_articles_v_version_german_tab_german_key_insights" to "authenticated";

grant update on table "public"."_articles_v_version_german_tab_german_key_insights" to "authenticated";

grant delete on table "public"."_articles_v_version_german_tab_german_key_insights" to "service_role";

grant insert on table "public"."_articles_v_version_german_tab_german_key_insights" to "service_role";

grant references on table "public"."_articles_v_version_german_tab_german_key_insights" to "service_role";

grant select on table "public"."_articles_v_version_german_tab_german_key_insights" to "service_role";

grant trigger on table "public"."_articles_v_version_german_tab_german_key_insights" to "service_role";

grant truncate on table "public"."_articles_v_version_german_tab_german_key_insights" to "service_role";

grant update on table "public"."_articles_v_version_german_tab_german_key_insights" to "service_role";

grant delete on table "public"."_articles_v_version_german_tab_german_keywords" to "anon";

grant insert on table "public"."_articles_v_version_german_tab_german_keywords" to "anon";

grant references on table "public"."_articles_v_version_german_tab_german_keywords" to "anon";

grant select on table "public"."_articles_v_version_german_tab_german_keywords" to "anon";

grant trigger on table "public"."_articles_v_version_german_tab_german_keywords" to "anon";

grant truncate on table "public"."_articles_v_version_german_tab_german_keywords" to "anon";

grant update on table "public"."_articles_v_version_german_tab_german_keywords" to "anon";

grant delete on table "public"."_articles_v_version_german_tab_german_keywords" to "authenticated";

grant insert on table "public"."_articles_v_version_german_tab_german_keywords" to "authenticated";

grant references on table "public"."_articles_v_version_german_tab_german_keywords" to "authenticated";

grant select on table "public"."_articles_v_version_german_tab_german_keywords" to "authenticated";

grant trigger on table "public"."_articles_v_version_german_tab_german_keywords" to "authenticated";

grant truncate on table "public"."_articles_v_version_german_tab_german_keywords" to "authenticated";

grant update on table "public"."_articles_v_version_german_tab_german_keywords" to "authenticated";

grant delete on table "public"."_articles_v_version_german_tab_german_keywords" to "service_role";

grant insert on table "public"."_articles_v_version_german_tab_german_keywords" to "service_role";

grant references on table "public"."_articles_v_version_german_tab_german_keywords" to "service_role";

grant select on table "public"."_articles_v_version_german_tab_german_keywords" to "service_role";

grant trigger on table "public"."_articles_v_version_german_tab_german_keywords" to "service_role";

grant truncate on table "public"."_articles_v_version_german_tab_german_keywords" to "service_role";

grant update on table "public"."_articles_v_version_german_tab_german_keywords" to "service_role";

grant delete on table "public"."_articles_v_version_related_companies" to "anon";

grant insert on table "public"."_articles_v_version_related_companies" to "anon";

grant references on table "public"."_articles_v_version_related_companies" to "anon";

grant select on table "public"."_articles_v_version_related_companies" to "anon";

grant trigger on table "public"."_articles_v_version_related_companies" to "anon";

grant truncate on table "public"."_articles_v_version_related_companies" to "anon";

grant update on table "public"."_articles_v_version_related_companies" to "anon";

grant delete on table "public"."_articles_v_version_related_companies" to "authenticated";

grant insert on table "public"."_articles_v_version_related_companies" to "authenticated";

grant references on table "public"."_articles_v_version_related_companies" to "authenticated";

grant select on table "public"."_articles_v_version_related_companies" to "authenticated";

grant trigger on table "public"."_articles_v_version_related_companies" to "authenticated";

grant truncate on table "public"."_articles_v_version_related_companies" to "authenticated";

grant update on table "public"."_articles_v_version_related_companies" to "authenticated";

grant delete on table "public"."_articles_v_version_related_companies" to "service_role";

grant insert on table "public"."_articles_v_version_related_companies" to "service_role";

grant references on table "public"."_articles_v_version_related_companies" to "service_role";

grant select on table "public"."_articles_v_version_related_companies" to "service_role";

grant trigger on table "public"."_articles_v_version_related_companies" to "service_role";

grant truncate on table "public"."_articles_v_version_related_companies" to "service_role";

grant update on table "public"."_articles_v_version_related_companies" to "service_role";

grant delete on table "public"."_pages_v" to "anon";

grant insert on table "public"."_pages_v" to "anon";

grant references on table "public"."_pages_v" to "anon";

grant select on table "public"."_pages_v" to "anon";

grant trigger on table "public"."_pages_v" to "anon";

grant truncate on table "public"."_pages_v" to "anon";

grant update on table "public"."_pages_v" to "anon";

grant delete on table "public"."_pages_v" to "authenticated";

grant insert on table "public"."_pages_v" to "authenticated";

grant references on table "public"."_pages_v" to "authenticated";

grant select on table "public"."_pages_v" to "authenticated";

grant trigger on table "public"."_pages_v" to "authenticated";

grant truncate on table "public"."_pages_v" to "authenticated";

grant update on table "public"."_pages_v" to "authenticated";

grant delete on table "public"."_pages_v" to "service_role";

grant insert on table "public"."_pages_v" to "service_role";

grant references on table "public"."_pages_v" to "service_role";

grant select on table "public"."_pages_v" to "service_role";

grant trigger on table "public"."_pages_v" to "service_role";

grant truncate on table "public"."_pages_v" to "service_role";

grant update on table "public"."_pages_v" to "service_role";

grant delete on table "public"."articles" to "anon";

grant insert on table "public"."articles" to "anon";

grant references on table "public"."articles" to "anon";

grant select on table "public"."articles" to "anon";

grant trigger on table "public"."articles" to "anon";

grant truncate on table "public"."articles" to "anon";

grant update on table "public"."articles" to "anon";

grant delete on table "public"."articles" to "authenticated";

grant insert on table "public"."articles" to "authenticated";

grant references on table "public"."articles" to "authenticated";

grant select on table "public"."articles" to "authenticated";

grant trigger on table "public"."articles" to "authenticated";

grant truncate on table "public"."articles" to "authenticated";

grant update on table "public"."articles" to "authenticated";

grant delete on table "public"."articles" to "service_role";

grant insert on table "public"."articles" to "service_role";

grant references on table "public"."articles" to "service_role";

grant select on table "public"."articles" to "service_role";

grant trigger on table "public"."articles" to "service_role";

grant truncate on table "public"."articles" to "service_role";

grant update on table "public"."articles" to "service_role";

grant delete on table "public"."articles_english_tab_enhanced_key_insights" to "anon";

grant insert on table "public"."articles_english_tab_enhanced_key_insights" to "anon";

grant references on table "public"."articles_english_tab_enhanced_key_insights" to "anon";

grant select on table "public"."articles_english_tab_enhanced_key_insights" to "anon";

grant trigger on table "public"."articles_english_tab_enhanced_key_insights" to "anon";

grant truncate on table "public"."articles_english_tab_enhanced_key_insights" to "anon";

grant update on table "public"."articles_english_tab_enhanced_key_insights" to "anon";

grant delete on table "public"."articles_english_tab_enhanced_key_insights" to "authenticated";

grant insert on table "public"."articles_english_tab_enhanced_key_insights" to "authenticated";

grant references on table "public"."articles_english_tab_enhanced_key_insights" to "authenticated";

grant select on table "public"."articles_english_tab_enhanced_key_insights" to "authenticated";

grant trigger on table "public"."articles_english_tab_enhanced_key_insights" to "authenticated";

grant truncate on table "public"."articles_english_tab_enhanced_key_insights" to "authenticated";

grant update on table "public"."articles_english_tab_enhanced_key_insights" to "authenticated";

grant delete on table "public"."articles_english_tab_enhanced_key_insights" to "service_role";

grant insert on table "public"."articles_english_tab_enhanced_key_insights" to "service_role";

grant references on table "public"."articles_english_tab_enhanced_key_insights" to "service_role";

grant select on table "public"."articles_english_tab_enhanced_key_insights" to "service_role";

grant trigger on table "public"."articles_english_tab_enhanced_key_insights" to "service_role";

grant truncate on table "public"."articles_english_tab_enhanced_key_insights" to "service_role";

grant update on table "public"."articles_english_tab_enhanced_key_insights" to "service_role";

grant delete on table "public"."articles_english_tab_keywords" to "anon";

grant insert on table "public"."articles_english_tab_keywords" to "anon";

grant references on table "public"."articles_english_tab_keywords" to "anon";

grant select on table "public"."articles_english_tab_keywords" to "anon";

grant trigger on table "public"."articles_english_tab_keywords" to "anon";

grant truncate on table "public"."articles_english_tab_keywords" to "anon";

grant update on table "public"."articles_english_tab_keywords" to "anon";

grant delete on table "public"."articles_english_tab_keywords" to "authenticated";

grant insert on table "public"."articles_english_tab_keywords" to "authenticated";

grant references on table "public"."articles_english_tab_keywords" to "authenticated";

grant select on table "public"."articles_english_tab_keywords" to "authenticated";

grant trigger on table "public"."articles_english_tab_keywords" to "authenticated";

grant truncate on table "public"."articles_english_tab_keywords" to "authenticated";

grant update on table "public"."articles_english_tab_keywords" to "authenticated";

grant delete on table "public"."articles_english_tab_keywords" to "service_role";

grant insert on table "public"."articles_english_tab_keywords" to "service_role";

grant references on table "public"."articles_english_tab_keywords" to "service_role";

grant select on table "public"."articles_english_tab_keywords" to "service_role";

grant trigger on table "public"."articles_english_tab_keywords" to "service_role";

grant truncate on table "public"."articles_english_tab_keywords" to "service_role";

grant update on table "public"."articles_english_tab_keywords" to "service_role";

grant delete on table "public"."articles_german_tab_german_key_insights" to "anon";

grant insert on table "public"."articles_german_tab_german_key_insights" to "anon";

grant references on table "public"."articles_german_tab_german_key_insights" to "anon";

grant select on table "public"."articles_german_tab_german_key_insights" to "anon";

grant trigger on table "public"."articles_german_tab_german_key_insights" to "anon";

grant truncate on table "public"."articles_german_tab_german_key_insights" to "anon";

grant update on table "public"."articles_german_tab_german_key_insights" to "anon";

grant delete on table "public"."articles_german_tab_german_key_insights" to "authenticated";

grant insert on table "public"."articles_german_tab_german_key_insights" to "authenticated";

grant references on table "public"."articles_german_tab_german_key_insights" to "authenticated";

grant select on table "public"."articles_german_tab_german_key_insights" to "authenticated";

grant trigger on table "public"."articles_german_tab_german_key_insights" to "authenticated";

grant truncate on table "public"."articles_german_tab_german_key_insights" to "authenticated";

grant update on table "public"."articles_german_tab_german_key_insights" to "authenticated";

grant delete on table "public"."articles_german_tab_german_key_insights" to "service_role";

grant insert on table "public"."articles_german_tab_german_key_insights" to "service_role";

grant references on table "public"."articles_german_tab_german_key_insights" to "service_role";

grant select on table "public"."articles_german_tab_german_key_insights" to "service_role";

grant trigger on table "public"."articles_german_tab_german_key_insights" to "service_role";

grant truncate on table "public"."articles_german_tab_german_key_insights" to "service_role";

grant update on table "public"."articles_german_tab_german_key_insights" to "service_role";

grant delete on table "public"."articles_german_tab_german_keywords" to "anon";

grant insert on table "public"."articles_german_tab_german_keywords" to "anon";

grant references on table "public"."articles_german_tab_german_keywords" to "anon";

grant select on table "public"."articles_german_tab_german_keywords" to "anon";

grant trigger on table "public"."articles_german_tab_german_keywords" to "anon";

grant truncate on table "public"."articles_german_tab_german_keywords" to "anon";

grant update on table "public"."articles_german_tab_german_keywords" to "anon";

grant delete on table "public"."articles_german_tab_german_keywords" to "authenticated";

grant insert on table "public"."articles_german_tab_german_keywords" to "authenticated";

grant references on table "public"."articles_german_tab_german_keywords" to "authenticated";

grant select on table "public"."articles_german_tab_german_keywords" to "authenticated";

grant trigger on table "public"."articles_german_tab_german_keywords" to "authenticated";

grant truncate on table "public"."articles_german_tab_german_keywords" to "authenticated";

grant update on table "public"."articles_german_tab_german_keywords" to "authenticated";

grant delete on table "public"."articles_german_tab_german_keywords" to "service_role";

grant insert on table "public"."articles_german_tab_german_keywords" to "service_role";

grant references on table "public"."articles_german_tab_german_keywords" to "service_role";

grant select on table "public"."articles_german_tab_german_keywords" to "service_role";

grant trigger on table "public"."articles_german_tab_german_keywords" to "service_role";

grant truncate on table "public"."articles_german_tab_german_keywords" to "service_role";

grant update on table "public"."articles_german_tab_german_keywords" to "service_role";

grant delete on table "public"."articles_related_companies" to "anon";

grant insert on table "public"."articles_related_companies" to "anon";

grant references on table "public"."articles_related_companies" to "anon";

grant select on table "public"."articles_related_companies" to "anon";

grant trigger on table "public"."articles_related_companies" to "anon";

grant truncate on table "public"."articles_related_companies" to "anon";

grant update on table "public"."articles_related_companies" to "anon";

grant delete on table "public"."articles_related_companies" to "authenticated";

grant insert on table "public"."articles_related_companies" to "authenticated";

grant references on table "public"."articles_related_companies" to "authenticated";

grant select on table "public"."articles_related_companies" to "authenticated";

grant trigger on table "public"."articles_related_companies" to "authenticated";

grant truncate on table "public"."articles_related_companies" to "authenticated";

grant update on table "public"."articles_related_companies" to "authenticated";

grant delete on table "public"."articles_related_companies" to "service_role";

grant insert on table "public"."articles_related_companies" to "service_role";

grant references on table "public"."articles_related_companies" to "service_role";

grant select on table "public"."articles_related_companies" to "service_role";

grant trigger on table "public"."articles_related_companies" to "service_role";

grant truncate on table "public"."articles_related_companies" to "service_role";

grant update on table "public"."articles_related_companies" to "service_role";

grant delete on table "public"."articles_rels" to "anon";

grant insert on table "public"."articles_rels" to "anon";

grant references on table "public"."articles_rels" to "anon";

grant select on table "public"."articles_rels" to "anon";

grant trigger on table "public"."articles_rels" to "anon";

grant truncate on table "public"."articles_rels" to "anon";

grant update on table "public"."articles_rels" to "anon";

grant delete on table "public"."articles_rels" to "authenticated";

grant insert on table "public"."articles_rels" to "authenticated";

grant references on table "public"."articles_rels" to "authenticated";

grant select on table "public"."articles_rels" to "authenticated";

grant trigger on table "public"."articles_rels" to "authenticated";

grant truncate on table "public"."articles_rels" to "authenticated";

grant update on table "public"."articles_rels" to "authenticated";

grant delete on table "public"."articles_rels" to "service_role";

grant insert on table "public"."articles_rels" to "service_role";

grant references on table "public"."articles_rels" to "service_role";

grant select on table "public"."articles_rels" to "service_role";

grant trigger on table "public"."articles_rels" to "service_role";

grant truncate on table "public"."articles_rels" to "service_role";

grant update on table "public"."articles_rels" to "service_role";

grant delete on table "public"."categories" to "anon";

grant insert on table "public"."categories" to "anon";

grant references on table "public"."categories" to "anon";

grant select on table "public"."categories" to "anon";

grant trigger on table "public"."categories" to "anon";

grant truncate on table "public"."categories" to "anon";

grant update on table "public"."categories" to "anon";

grant delete on table "public"."categories" to "authenticated";

grant insert on table "public"."categories" to "authenticated";

grant references on table "public"."categories" to "authenticated";

grant select on table "public"."categories" to "authenticated";

grant trigger on table "public"."categories" to "authenticated";

grant truncate on table "public"."categories" to "authenticated";

grant update on table "public"."categories" to "authenticated";

grant delete on table "public"."categories" to "service_role";

grant insert on table "public"."categories" to "service_role";

grant references on table "public"."categories" to "service_role";

grant select on table "public"."categories" to "service_role";

grant trigger on table "public"."categories" to "service_role";

grant truncate on table "public"."categories" to "service_role";

grant update on table "public"."categories" to "service_role";

grant delete on table "public"."footer" to "anon";

grant insert on table "public"."footer" to "anon";

grant references on table "public"."footer" to "anon";

grant select on table "public"."footer" to "anon";

grant trigger on table "public"."footer" to "anon";

grant truncate on table "public"."footer" to "anon";

grant update on table "public"."footer" to "anon";

grant delete on table "public"."footer" to "authenticated";

grant insert on table "public"."footer" to "authenticated";

grant references on table "public"."footer" to "authenticated";

grant select on table "public"."footer" to "authenticated";

grant trigger on table "public"."footer" to "authenticated";

grant truncate on table "public"."footer" to "authenticated";

grant update on table "public"."footer" to "authenticated";

grant delete on table "public"."footer" to "service_role";

grant insert on table "public"."footer" to "service_role";

grant references on table "public"."footer" to "service_role";

grant select on table "public"."footer" to "service_role";

grant trigger on table "public"."footer" to "service_role";

grant truncate on table "public"."footer" to "service_role";

grant update on table "public"."footer" to "service_role";

grant delete on table "public"."footer_legal_links" to "anon";

grant insert on table "public"."footer_legal_links" to "anon";

grant references on table "public"."footer_legal_links" to "anon";

grant select on table "public"."footer_legal_links" to "anon";

grant trigger on table "public"."footer_legal_links" to "anon";

grant truncate on table "public"."footer_legal_links" to "anon";

grant update on table "public"."footer_legal_links" to "anon";

grant delete on table "public"."footer_legal_links" to "authenticated";

grant insert on table "public"."footer_legal_links" to "authenticated";

grant references on table "public"."footer_legal_links" to "authenticated";

grant select on table "public"."footer_legal_links" to "authenticated";

grant trigger on table "public"."footer_legal_links" to "authenticated";

grant truncate on table "public"."footer_legal_links" to "authenticated";

grant update on table "public"."footer_legal_links" to "authenticated";

grant delete on table "public"."footer_legal_links" to "service_role";

grant insert on table "public"."footer_legal_links" to "service_role";

grant references on table "public"."footer_legal_links" to "service_role";

grant select on table "public"."footer_legal_links" to "service_role";

grant trigger on table "public"."footer_legal_links" to "service_role";

grant truncate on table "public"."footer_legal_links" to "service_role";

grant update on table "public"."footer_legal_links" to "service_role";

grant delete on table "public"."footer_navigation_sections" to "anon";

grant insert on table "public"."footer_navigation_sections" to "anon";

grant references on table "public"."footer_navigation_sections" to "anon";

grant select on table "public"."footer_navigation_sections" to "anon";

grant trigger on table "public"."footer_navigation_sections" to "anon";

grant truncate on table "public"."footer_navigation_sections" to "anon";

grant update on table "public"."footer_navigation_sections" to "anon";

grant delete on table "public"."footer_navigation_sections" to "authenticated";

grant insert on table "public"."footer_navigation_sections" to "authenticated";

grant references on table "public"."footer_navigation_sections" to "authenticated";

grant select on table "public"."footer_navigation_sections" to "authenticated";

grant trigger on table "public"."footer_navigation_sections" to "authenticated";

grant truncate on table "public"."footer_navigation_sections" to "authenticated";

grant update on table "public"."footer_navigation_sections" to "authenticated";

grant delete on table "public"."footer_navigation_sections" to "service_role";

grant insert on table "public"."footer_navigation_sections" to "service_role";

grant references on table "public"."footer_navigation_sections" to "service_role";

grant select on table "public"."footer_navigation_sections" to "service_role";

grant trigger on table "public"."footer_navigation_sections" to "service_role";

grant truncate on table "public"."footer_navigation_sections" to "service_role";

grant update on table "public"."footer_navigation_sections" to "service_role";

grant delete on table "public"."footer_navigation_sections_links" to "anon";

grant insert on table "public"."footer_navigation_sections_links" to "anon";

grant references on table "public"."footer_navigation_sections_links" to "anon";

grant select on table "public"."footer_navigation_sections_links" to "anon";

grant trigger on table "public"."footer_navigation_sections_links" to "anon";

grant truncate on table "public"."footer_navigation_sections_links" to "anon";

grant update on table "public"."footer_navigation_sections_links" to "anon";

grant delete on table "public"."footer_navigation_sections_links" to "authenticated";

grant insert on table "public"."footer_navigation_sections_links" to "authenticated";

grant references on table "public"."footer_navigation_sections_links" to "authenticated";

grant select on table "public"."footer_navigation_sections_links" to "authenticated";

grant trigger on table "public"."footer_navigation_sections_links" to "authenticated";

grant truncate on table "public"."footer_navigation_sections_links" to "authenticated";

grant update on table "public"."footer_navigation_sections_links" to "authenticated";

grant delete on table "public"."footer_navigation_sections_links" to "service_role";

grant insert on table "public"."footer_navigation_sections_links" to "service_role";

grant references on table "public"."footer_navigation_sections_links" to "service_role";

grant select on table "public"."footer_navigation_sections_links" to "service_role";

grant trigger on table "public"."footer_navigation_sections_links" to "service_role";

grant truncate on table "public"."footer_navigation_sections_links" to "service_role";

grant update on table "public"."footer_navigation_sections_links" to "service_role";

grant delete on table "public"."footer_rels" to "anon";

grant insert on table "public"."footer_rels" to "anon";

grant references on table "public"."footer_rels" to "anon";

grant select on table "public"."footer_rels" to "anon";

grant trigger on table "public"."footer_rels" to "anon";

grant truncate on table "public"."footer_rels" to "anon";

grant update on table "public"."footer_rels" to "anon";

grant delete on table "public"."footer_rels" to "authenticated";

grant insert on table "public"."footer_rels" to "authenticated";

grant references on table "public"."footer_rels" to "authenticated";

grant select on table "public"."footer_rels" to "authenticated";

grant trigger on table "public"."footer_rels" to "authenticated";

grant truncate on table "public"."footer_rels" to "authenticated";

grant update on table "public"."footer_rels" to "authenticated";

grant delete on table "public"."footer_rels" to "service_role";

grant insert on table "public"."footer_rels" to "service_role";

grant references on table "public"."footer_rels" to "service_role";

grant select on table "public"."footer_rels" to "service_role";

grant trigger on table "public"."footer_rels" to "service_role";

grant truncate on table "public"."footer_rels" to "service_role";

grant update on table "public"."footer_rels" to "service_role";

grant delete on table "public"."footer_social_links" to "anon";

grant insert on table "public"."footer_social_links" to "anon";

grant references on table "public"."footer_social_links" to "anon";

grant select on table "public"."footer_social_links" to "anon";

grant trigger on table "public"."footer_social_links" to "anon";

grant truncate on table "public"."footer_social_links" to "anon";

grant update on table "public"."footer_social_links" to "anon";

grant delete on table "public"."footer_social_links" to "authenticated";

grant insert on table "public"."footer_social_links" to "authenticated";

grant references on table "public"."footer_social_links" to "authenticated";

grant select on table "public"."footer_social_links" to "authenticated";

grant trigger on table "public"."footer_social_links" to "authenticated";

grant truncate on table "public"."footer_social_links" to "authenticated";

grant update on table "public"."footer_social_links" to "authenticated";

grant delete on table "public"."footer_social_links" to "service_role";

grant insert on table "public"."footer_social_links" to "service_role";

grant references on table "public"."footer_social_links" to "service_role";

grant select on table "public"."footer_social_links" to "service_role";

grant trigger on table "public"."footer_social_links" to "service_role";

grant truncate on table "public"."footer_social_links" to "service_role";

grant update on table "public"."footer_social_links" to "service_role";

grant delete on table "public"."header" to "anon";

grant insert on table "public"."header" to "anon";

grant references on table "public"."header" to "anon";

grant select on table "public"."header" to "anon";

grant trigger on table "public"."header" to "anon";

grant truncate on table "public"."header" to "anon";

grant update on table "public"."header" to "anon";

grant delete on table "public"."header" to "authenticated";

grant insert on table "public"."header" to "authenticated";

grant references on table "public"."header" to "authenticated";

grant select on table "public"."header" to "authenticated";

grant trigger on table "public"."header" to "authenticated";

grant truncate on table "public"."header" to "authenticated";

grant update on table "public"."header" to "authenticated";

grant delete on table "public"."header" to "service_role";

grant insert on table "public"."header" to "service_role";

grant references on table "public"."header" to "service_role";

grant select on table "public"."header" to "service_role";

grant trigger on table "public"."header" to "service_role";

grant truncate on table "public"."header" to "service_role";

grant update on table "public"."header" to "service_role";

grant delete on table "public"."header_nav_items" to "anon";

grant insert on table "public"."header_nav_items" to "anon";

grant references on table "public"."header_nav_items" to "anon";

grant select on table "public"."header_nav_items" to "anon";

grant trigger on table "public"."header_nav_items" to "anon";

grant truncate on table "public"."header_nav_items" to "anon";

grant update on table "public"."header_nav_items" to "anon";

grant delete on table "public"."header_nav_items" to "authenticated";

grant insert on table "public"."header_nav_items" to "authenticated";

grant references on table "public"."header_nav_items" to "authenticated";

grant select on table "public"."header_nav_items" to "authenticated";

grant trigger on table "public"."header_nav_items" to "authenticated";

grant truncate on table "public"."header_nav_items" to "authenticated";

grant update on table "public"."header_nav_items" to "authenticated";

grant delete on table "public"."header_nav_items" to "service_role";

grant insert on table "public"."header_nav_items" to "service_role";

grant references on table "public"."header_nav_items" to "service_role";

grant select on table "public"."header_nav_items" to "service_role";

grant trigger on table "public"."header_nav_items" to "service_role";

grant truncate on table "public"."header_nav_items" to "service_role";

grant update on table "public"."header_nav_items" to "service_role";

grant delete on table "public"."header_rels" to "anon";

grant insert on table "public"."header_rels" to "anon";

grant references on table "public"."header_rels" to "anon";

grant select on table "public"."header_rels" to "anon";

grant trigger on table "public"."header_rels" to "anon";

grant truncate on table "public"."header_rels" to "anon";

grant update on table "public"."header_rels" to "anon";

grant delete on table "public"."header_rels" to "authenticated";

grant insert on table "public"."header_rels" to "authenticated";

grant references on table "public"."header_rels" to "authenticated";

grant select on table "public"."header_rels" to "authenticated";

grant trigger on table "public"."header_rels" to "authenticated";

grant truncate on table "public"."header_rels" to "authenticated";

grant update on table "public"."header_rels" to "authenticated";

grant delete on table "public"."header_rels" to "service_role";

grant insert on table "public"."header_rels" to "service_role";

grant references on table "public"."header_rels" to "service_role";

grant select on table "public"."header_rels" to "service_role";

grant trigger on table "public"."header_rels" to "service_role";

grant truncate on table "public"."header_rels" to "service_role";

grant update on table "public"."header_rels" to "service_role";

grant delete on table "public"."keywords" to "anon";

grant insert on table "public"."keywords" to "anon";

grant references on table "public"."keywords" to "anon";

grant select on table "public"."keywords" to "anon";

grant trigger on table "public"."keywords" to "anon";

grant truncate on table "public"."keywords" to "anon";

grant update on table "public"."keywords" to "anon";

grant delete on table "public"."keywords" to "authenticated";

grant insert on table "public"."keywords" to "authenticated";

grant references on table "public"."keywords" to "authenticated";

grant select on table "public"."keywords" to "authenticated";

grant trigger on table "public"."keywords" to "authenticated";

grant truncate on table "public"."keywords" to "authenticated";

grant update on table "public"."keywords" to "authenticated";

grant delete on table "public"."keywords" to "service_role";

grant insert on table "public"."keywords" to "service_role";

grant references on table "public"."keywords" to "service_role";

grant select on table "public"."keywords" to "service_role";

grant trigger on table "public"."keywords" to "service_role";

grant truncate on table "public"."keywords" to "service_role";

grant update on table "public"."keywords" to "service_role";

grant delete on table "public"."media" to "anon";

grant insert on table "public"."media" to "anon";

grant references on table "public"."media" to "anon";

grant select on table "public"."media" to "anon";

grant trigger on table "public"."media" to "anon";

grant truncate on table "public"."media" to "anon";

grant update on table "public"."media" to "anon";

grant delete on table "public"."media" to "authenticated";

grant insert on table "public"."media" to "authenticated";

grant references on table "public"."media" to "authenticated";

grant select on table "public"."media" to "authenticated";

grant trigger on table "public"."media" to "authenticated";

grant truncate on table "public"."media" to "authenticated";

grant update on table "public"."media" to "authenticated";

grant delete on table "public"."media" to "service_role";

grant insert on table "public"."media" to "service_role";

grant references on table "public"."media" to "service_role";

grant select on table "public"."media" to "service_role";

grant trigger on table "public"."media" to "service_role";

grant truncate on table "public"."media" to "service_role";

grant update on table "public"."media" to "service_role";

grant delete on table "public"."pages" to "anon";

grant insert on table "public"."pages" to "anon";

grant references on table "public"."pages" to "anon";

grant select on table "public"."pages" to "anon";

grant trigger on table "public"."pages" to "anon";

grant truncate on table "public"."pages" to "anon";

grant update on table "public"."pages" to "anon";

grant delete on table "public"."pages" to "authenticated";

grant insert on table "public"."pages" to "authenticated";

grant references on table "public"."pages" to "authenticated";

grant select on table "public"."pages" to "authenticated";

grant trigger on table "public"."pages" to "authenticated";

grant truncate on table "public"."pages" to "authenticated";

grant update on table "public"."pages" to "authenticated";

grant delete on table "public"."pages" to "service_role";

grant insert on table "public"."pages" to "service_role";

grant references on table "public"."pages" to "service_role";

grant select on table "public"."pages" to "service_role";

grant trigger on table "public"."pages" to "service_role";

grant truncate on table "public"."pages" to "service_role";

grant update on table "public"."pages" to "service_role";

grant delete on table "public"."payload_locked_documents" to "anon";

grant insert on table "public"."payload_locked_documents" to "anon";

grant references on table "public"."payload_locked_documents" to "anon";

grant select on table "public"."payload_locked_documents" to "anon";

grant trigger on table "public"."payload_locked_documents" to "anon";

grant truncate on table "public"."payload_locked_documents" to "anon";

grant update on table "public"."payload_locked_documents" to "anon";

grant delete on table "public"."payload_locked_documents" to "authenticated";

grant insert on table "public"."payload_locked_documents" to "authenticated";

grant references on table "public"."payload_locked_documents" to "authenticated";

grant select on table "public"."payload_locked_documents" to "authenticated";

grant trigger on table "public"."payload_locked_documents" to "authenticated";

grant truncate on table "public"."payload_locked_documents" to "authenticated";

grant update on table "public"."payload_locked_documents" to "authenticated";

grant delete on table "public"."payload_locked_documents" to "service_role";

grant insert on table "public"."payload_locked_documents" to "service_role";

grant references on table "public"."payload_locked_documents" to "service_role";

grant select on table "public"."payload_locked_documents" to "service_role";

grant trigger on table "public"."payload_locked_documents" to "service_role";

grant truncate on table "public"."payload_locked_documents" to "service_role";

grant update on table "public"."payload_locked_documents" to "service_role";

grant delete on table "public"."payload_locked_documents_rels" to "anon";

grant insert on table "public"."payload_locked_documents_rels" to "anon";

grant references on table "public"."payload_locked_documents_rels" to "anon";

grant select on table "public"."payload_locked_documents_rels" to "anon";

grant trigger on table "public"."payload_locked_documents_rels" to "anon";

grant truncate on table "public"."payload_locked_documents_rels" to "anon";

grant update on table "public"."payload_locked_documents_rels" to "anon";

grant delete on table "public"."payload_locked_documents_rels" to "authenticated";

grant insert on table "public"."payload_locked_documents_rels" to "authenticated";

grant references on table "public"."payload_locked_documents_rels" to "authenticated";

grant select on table "public"."payload_locked_documents_rels" to "authenticated";

grant trigger on table "public"."payload_locked_documents_rels" to "authenticated";

grant truncate on table "public"."payload_locked_documents_rels" to "authenticated";

grant update on table "public"."payload_locked_documents_rels" to "authenticated";

grant delete on table "public"."payload_locked_documents_rels" to "service_role";

grant insert on table "public"."payload_locked_documents_rels" to "service_role";

grant references on table "public"."payload_locked_documents_rels" to "service_role";

grant select on table "public"."payload_locked_documents_rels" to "service_role";

grant trigger on table "public"."payload_locked_documents_rels" to "service_role";

grant truncate on table "public"."payload_locked_documents_rels" to "service_role";

grant update on table "public"."payload_locked_documents_rels" to "service_role";

grant delete on table "public"."payload_migrations" to "anon";

grant insert on table "public"."payload_migrations" to "anon";

grant references on table "public"."payload_migrations" to "anon";

grant select on table "public"."payload_migrations" to "anon";

grant trigger on table "public"."payload_migrations" to "anon";

grant truncate on table "public"."payload_migrations" to "anon";

grant update on table "public"."payload_migrations" to "anon";

grant delete on table "public"."payload_migrations" to "authenticated";

grant insert on table "public"."payload_migrations" to "authenticated";

grant references on table "public"."payload_migrations" to "authenticated";

grant select on table "public"."payload_migrations" to "authenticated";

grant trigger on table "public"."payload_migrations" to "authenticated";

grant truncate on table "public"."payload_migrations" to "authenticated";

grant update on table "public"."payload_migrations" to "authenticated";

grant delete on table "public"."payload_migrations" to "service_role";

grant insert on table "public"."payload_migrations" to "service_role";

grant references on table "public"."payload_migrations" to "service_role";

grant select on table "public"."payload_migrations" to "service_role";

grant trigger on table "public"."payload_migrations" to "service_role";

grant truncate on table "public"."payload_migrations" to "service_role";

grant update on table "public"."payload_migrations" to "service_role";

grant delete on table "public"."payload_preferences" to "anon";

grant insert on table "public"."payload_preferences" to "anon";

grant references on table "public"."payload_preferences" to "anon";

grant select on table "public"."payload_preferences" to "anon";

grant trigger on table "public"."payload_preferences" to "anon";

grant truncate on table "public"."payload_preferences" to "anon";

grant update on table "public"."payload_preferences" to "anon";

grant delete on table "public"."payload_preferences" to "authenticated";

grant insert on table "public"."payload_preferences" to "authenticated";

grant references on table "public"."payload_preferences" to "authenticated";

grant select on table "public"."payload_preferences" to "authenticated";

grant trigger on table "public"."payload_preferences" to "authenticated";

grant truncate on table "public"."payload_preferences" to "authenticated";

grant update on table "public"."payload_preferences" to "authenticated";

grant delete on table "public"."payload_preferences" to "service_role";

grant insert on table "public"."payload_preferences" to "service_role";

grant references on table "public"."payload_preferences" to "service_role";

grant select on table "public"."payload_preferences" to "service_role";

grant trigger on table "public"."payload_preferences" to "service_role";

grant truncate on table "public"."payload_preferences" to "service_role";

grant update on table "public"."payload_preferences" to "service_role";

grant delete on table "public"."payload_preferences_rels" to "anon";

grant insert on table "public"."payload_preferences_rels" to "anon";

grant references on table "public"."payload_preferences_rels" to "anon";

grant select on table "public"."payload_preferences_rels" to "anon";

grant trigger on table "public"."payload_preferences_rels" to "anon";

grant truncate on table "public"."payload_preferences_rels" to "anon";

grant update on table "public"."payload_preferences_rels" to "anon";

grant delete on table "public"."payload_preferences_rels" to "authenticated";

grant insert on table "public"."payload_preferences_rels" to "authenticated";

grant references on table "public"."payload_preferences_rels" to "authenticated";

grant select on table "public"."payload_preferences_rels" to "authenticated";

grant trigger on table "public"."payload_preferences_rels" to "authenticated";

grant truncate on table "public"."payload_preferences_rels" to "authenticated";

grant update on table "public"."payload_preferences_rels" to "authenticated";

grant delete on table "public"."payload_preferences_rels" to "service_role";

grant insert on table "public"."payload_preferences_rels" to "service_role";

grant references on table "public"."payload_preferences_rels" to "service_role";

grant select on table "public"."payload_preferences_rels" to "service_role";

grant trigger on table "public"."payload_preferences_rels" to "service_role";

grant truncate on table "public"."payload_preferences_rels" to "service_role";

grant update on table "public"."payload_preferences_rels" to "service_role";

grant delete on table "public"."processed_urls" to "anon";

grant insert on table "public"."processed_urls" to "anon";

grant references on table "public"."processed_urls" to "anon";

grant select on table "public"."processed_urls" to "anon";

grant trigger on table "public"."processed_urls" to "anon";

grant truncate on table "public"."processed_urls" to "anon";

grant update on table "public"."processed_urls" to "anon";

grant delete on table "public"."processed_urls" to "authenticated";

grant insert on table "public"."processed_urls" to "authenticated";

grant references on table "public"."processed_urls" to "authenticated";

grant select on table "public"."processed_urls" to "authenticated";

grant trigger on table "public"."processed_urls" to "authenticated";

grant truncate on table "public"."processed_urls" to "authenticated";

grant update on table "public"."processed_urls" to "authenticated";

grant delete on table "public"."processed_urls" to "service_role";

grant insert on table "public"."processed_urls" to "service_role";

grant references on table "public"."processed_urls" to "service_role";

grant select on table "public"."processed_urls" to "service_role";

grant trigger on table "public"."processed_urls" to "service_role";

grant truncate on table "public"."processed_urls" to "service_role";

grant update on table "public"."processed_urls" to "service_role";

grant delete on table "public"."rss_feeds" to "anon";

grant insert on table "public"."rss_feeds" to "anon";

grant references on table "public"."rss_feeds" to "anon";

grant select on table "public"."rss_feeds" to "anon";

grant trigger on table "public"."rss_feeds" to "anon";

grant truncate on table "public"."rss_feeds" to "anon";

grant update on table "public"."rss_feeds" to "anon";

grant delete on table "public"."rss_feeds" to "authenticated";

grant insert on table "public"."rss_feeds" to "authenticated";

grant references on table "public"."rss_feeds" to "authenticated";

grant select on table "public"."rss_feeds" to "authenticated";

grant trigger on table "public"."rss_feeds" to "authenticated";

grant truncate on table "public"."rss_feeds" to "authenticated";

grant update on table "public"."rss_feeds" to "authenticated";

grant delete on table "public"."rss_feeds" to "service_role";

grant insert on table "public"."rss_feeds" to "service_role";

grant references on table "public"."rss_feeds" to "service_role";

grant select on table "public"."rss_feeds" to "service_role";

grant trigger on table "public"."rss_feeds" to "service_role";

grant truncate on table "public"."rss_feeds" to "service_role";

grant update on table "public"."rss_feeds" to "service_role";

grant delete on table "public"."rss_feeds_firecrawl_options_exclude_tags" to "anon";

grant insert on table "public"."rss_feeds_firecrawl_options_exclude_tags" to "anon";

grant references on table "public"."rss_feeds_firecrawl_options_exclude_tags" to "anon";

grant select on table "public"."rss_feeds_firecrawl_options_exclude_tags" to "anon";

grant trigger on table "public"."rss_feeds_firecrawl_options_exclude_tags" to "anon";

grant truncate on table "public"."rss_feeds_firecrawl_options_exclude_tags" to "anon";

grant update on table "public"."rss_feeds_firecrawl_options_exclude_tags" to "anon";

grant delete on table "public"."rss_feeds_firecrawl_options_exclude_tags" to "authenticated";

grant insert on table "public"."rss_feeds_firecrawl_options_exclude_tags" to "authenticated";

grant references on table "public"."rss_feeds_firecrawl_options_exclude_tags" to "authenticated";

grant select on table "public"."rss_feeds_firecrawl_options_exclude_tags" to "authenticated";

grant trigger on table "public"."rss_feeds_firecrawl_options_exclude_tags" to "authenticated";

grant truncate on table "public"."rss_feeds_firecrawl_options_exclude_tags" to "authenticated";

grant update on table "public"."rss_feeds_firecrawl_options_exclude_tags" to "authenticated";

grant delete on table "public"."rss_feeds_firecrawl_options_exclude_tags" to "service_role";

grant insert on table "public"."rss_feeds_firecrawl_options_exclude_tags" to "service_role";

grant references on table "public"."rss_feeds_firecrawl_options_exclude_tags" to "service_role";

grant select on table "public"."rss_feeds_firecrawl_options_exclude_tags" to "service_role";

grant trigger on table "public"."rss_feeds_firecrawl_options_exclude_tags" to "service_role";

grant truncate on table "public"."rss_feeds_firecrawl_options_exclude_tags" to "service_role";

grant update on table "public"."rss_feeds_firecrawl_options_exclude_tags" to "service_role";

grant delete on table "public"."rss_feeds_firecrawl_options_include_tags" to "anon";

grant insert on table "public"."rss_feeds_firecrawl_options_include_tags" to "anon";

grant references on table "public"."rss_feeds_firecrawl_options_include_tags" to "anon";

grant select on table "public"."rss_feeds_firecrawl_options_include_tags" to "anon";

grant trigger on table "public"."rss_feeds_firecrawl_options_include_tags" to "anon";

grant truncate on table "public"."rss_feeds_firecrawl_options_include_tags" to "anon";

grant update on table "public"."rss_feeds_firecrawl_options_include_tags" to "anon";

grant delete on table "public"."rss_feeds_firecrawl_options_include_tags" to "authenticated";

grant insert on table "public"."rss_feeds_firecrawl_options_include_tags" to "authenticated";

grant references on table "public"."rss_feeds_firecrawl_options_include_tags" to "authenticated";

grant select on table "public"."rss_feeds_firecrawl_options_include_tags" to "authenticated";

grant trigger on table "public"."rss_feeds_firecrawl_options_include_tags" to "authenticated";

grant truncate on table "public"."rss_feeds_firecrawl_options_include_tags" to "authenticated";

grant update on table "public"."rss_feeds_firecrawl_options_include_tags" to "authenticated";

grant delete on table "public"."rss_feeds_firecrawl_options_include_tags" to "service_role";

grant insert on table "public"."rss_feeds_firecrawl_options_include_tags" to "service_role";

grant references on table "public"."rss_feeds_firecrawl_options_include_tags" to "service_role";

grant select on table "public"."rss_feeds_firecrawl_options_include_tags" to "service_role";

grant trigger on table "public"."rss_feeds_firecrawl_options_include_tags" to "service_role";

grant truncate on table "public"."rss_feeds_firecrawl_options_include_tags" to "service_role";

grant update on table "public"."rss_feeds_firecrawl_options_include_tags" to "service_role";

grant delete on table "public"."rss_feeds_keyword_filtering_custom_keywords" to "anon";

grant insert on table "public"."rss_feeds_keyword_filtering_custom_keywords" to "anon";

grant references on table "public"."rss_feeds_keyword_filtering_custom_keywords" to "anon";

grant select on table "public"."rss_feeds_keyword_filtering_custom_keywords" to "anon";

grant trigger on table "public"."rss_feeds_keyword_filtering_custom_keywords" to "anon";

grant truncate on table "public"."rss_feeds_keyword_filtering_custom_keywords" to "anon";

grant update on table "public"."rss_feeds_keyword_filtering_custom_keywords" to "anon";

grant delete on table "public"."rss_feeds_keyword_filtering_custom_keywords" to "authenticated";

grant insert on table "public"."rss_feeds_keyword_filtering_custom_keywords" to "authenticated";

grant references on table "public"."rss_feeds_keyword_filtering_custom_keywords" to "authenticated";

grant select on table "public"."rss_feeds_keyword_filtering_custom_keywords" to "authenticated";

grant trigger on table "public"."rss_feeds_keyword_filtering_custom_keywords" to "authenticated";

grant truncate on table "public"."rss_feeds_keyword_filtering_custom_keywords" to "authenticated";

grant update on table "public"."rss_feeds_keyword_filtering_custom_keywords" to "authenticated";

grant delete on table "public"."rss_feeds_keyword_filtering_custom_keywords" to "service_role";

grant insert on table "public"."rss_feeds_keyword_filtering_custom_keywords" to "service_role";

grant references on table "public"."rss_feeds_keyword_filtering_custom_keywords" to "service_role";

grant select on table "public"."rss_feeds_keyword_filtering_custom_keywords" to "service_role";

grant trigger on table "public"."rss_feeds_keyword_filtering_custom_keywords" to "service_role";

grant truncate on table "public"."rss_feeds_keyword_filtering_custom_keywords" to "service_role";

grant update on table "public"."rss_feeds_keyword_filtering_custom_keywords" to "service_role";

grant delete on table "public"."users" to "anon";

grant insert on table "public"."users" to "anon";

grant references on table "public"."users" to "anon";

grant select on table "public"."users" to "anon";

grant trigger on table "public"."users" to "anon";

grant truncate on table "public"."users" to "anon";

grant update on table "public"."users" to "anon";

grant delete on table "public"."users" to "authenticated";

grant insert on table "public"."users" to "authenticated";

grant references on table "public"."users" to "authenticated";

grant select on table "public"."users" to "authenticated";

grant trigger on table "public"."users" to "authenticated";

grant truncate on table "public"."users" to "authenticated";

grant update on table "public"."users" to "authenticated";

grant delete on table "public"."users" to "service_role";

grant insert on table "public"."users" to "service_role";

grant references on table "public"."users" to "service_role";

grant select on table "public"."users" to "service_role";

grant trigger on table "public"."users" to "service_role";

grant truncate on table "public"."users" to "service_role";

grant update on table "public"."users" to "service_role";


