/**
 * Articles Collection Real Integration Test
 *
 * This test uses the ACTUAL Articles collection hook logic
 * to verify that the implementation works correctly.
 *
 * This ensures that the tests accurately reflect reality
 * and that we didn't just tweak tests to pass.
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { Articles } from '../Articles';

// Extract the actual hook from the Articles collection
const actualSlugHook = Articles.fields.find(field => field.name === 'slug')
  ?.hooks?.beforeValidate?.[0];

// Mock PayloadCMS for testing
const createMockPayload = (existingArticles: any[] = []) => ({
  find: vi.fn().mockResolvedValue({
    docs: existingArticles,
  }),
});

describe('Articles Collection Real Integration', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should use the actual Articles collection hook logic', async () => {
    if (!actualSlugHook) {
      throw new Error('Could not find slug hook in Articles collection');
    }

    // Test with the real hook
    const data = {
      englishTab: {
        enhancedTitle: 'Real Hook Test Article',
      },
    };

    const context = {
      value: undefined,
      data,
      operation: 'create',
      req: { payload: createMockPayload() },
    };

    const result = await actualSlugHook(context);

    expect(result).toBe('real-hook-test-article');
    expect(context.req.payload.find).toHaveBeenCalledWith({
      collection: 'articles',
      where: {
        slug: {
          equals: 'real-hook-test-article',
        },
      },
      limit: 1,
    });
  });

  it('should handle duplicates with actual hook logic', async () => {
    if (!actualSlugHook) {
      throw new Error('Could not find slug hook in Articles collection');
    }

    // Mock existing article
    const existingArticles = [
      { id: 'existing-123', slug: 'duplicate-test-article' },
    ];

    const data = {
      englishTab: {
        enhancedTitle: 'Duplicate Test Article',
      },
    };

    const context = {
      value: undefined,
      data,
      operation: 'create',
      req: { payload: createMockPayload(existingArticles) },
    };

    const result = await actualSlugHook(context);

    // Should get timestamp suffix due to duplicate
    expect(result).toMatch(/^duplicate-test-article-\d{14}$/);
    expect(context.req.payload.find).toHaveBeenCalledWith({
      collection: 'articles',
      where: {
        slug: {
          equals: 'duplicate-test-article',
        },
      },
      limit: 1,
    });
  });

  it('should handle German translation updates with actual hook', async () => {
    if (!actualSlugHook) {
      throw new Error('Could not find slug hook in Articles collection');
    }

    const data = {
      id: 'article-789',
      germanTab: {
        germanTitle: 'Echter Haken Test Artikel',
      },
      hasGermanTranslation: true,
    };

    const context = {
      value: 'existing-english-slug',
      data,
      operation: 'update',
      req: { payload: createMockPayload() },
    };

    const result = await actualSlugHook(context);

    expect(result).toBe('echter-haken-test-artikel');
    expect(context.req.payload.find).toHaveBeenCalledWith({
      collection: 'articles',
      where: {
        and: [
          {
            slug: {
              equals: 'echter-haken-test-artikel',
            },
          },
          {
            id: {
              not_equals: 'article-789',
            },
          },
        ],
      },
      limit: 1,
    });
  });

  it('should handle errors gracefully with actual hook', async () => {
    if (!actualSlugHook) {
      throw new Error('Could not find slug hook in Articles collection');
    }

    // Mock database error
    const mockPayload = {
      find: vi.fn().mockRejectedValue(new Error('Database error')),
    };

    const data = {
      englishTab: {
        enhancedTitle: 'Error Test Article',
      },
    };

    const context = {
      value: 'fallback-slug',
      data,
      operation: 'create',
      req: { payload: mockPayload },
    };

    const result = await actualSlugHook(context);

    // Should return fallback value on error
    expect(result).toBe('fallback-slug');
  });
});
