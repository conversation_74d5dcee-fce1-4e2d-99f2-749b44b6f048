/**
 * Articles Collection Implementation Comparison Test
 *
 * This test directly compares the test implementation logic
 * with the actual Articles collection hook to prove they
 * produce identical results.
 *
 * This ensures test integrity and prevents false positives.
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { Articles } from '../Articles';
import {
  formatSlug,
  formatSlugWithTimestamp,
} from '../../fields/slug/formatSlug';

// Extract the actual hook from the Articles collection
const actualSlugHook = Articles.fields.find(field => field.name === 'slug')
  ?.hooks?.beforeValidate?.[0];

// Test implementation from our tests (copy for comparison)
const testSlugHook = async (context: {
  value: string | undefined;
  data: any;
  operation: 'create' | 'update';
  req: { payload: any };
}) => {
  const { value, data, operation, req } = context;

  try {
    // On create: Generate slug from enhanced English title or main title
    if (!value && operation === 'create') {
      const titleSource = data?.englishTab?.enhancedTitle || data?.title;

      if (titleSource) {
        const baseSlug = formatSlug(titleSource);

        // Check for existing slug in database
        const payload = req.payload;
        const existingArticles = await payload.find({
          collection: 'articles',
          where: {
            slug: {
              equals: baseSlug,
            },
          },
          limit: 1,
        });

        // If duplicate exists, add timestamp suffix
        if (existingArticles.docs.length > 0) {
          return formatSlugWithTimestamp(titleSource, true);
        }

        return baseSlug;
      }
    }

    // On update: Switch to German slug when translation is added
    if (
      operation === 'update' &&
      data?.germanTab?.germanTitle &&
      data?.hasGermanTranslation
    ) {
      const baseSlug = formatSlug(data.germanTab.germanTitle);

      // Check for existing slug in database (excluding current article)
      const payload = req.payload;
      const existingArticles = await payload.find({
        collection: 'articles',
        where: {
          and: [
            {
              slug: {
                equals: baseSlug,
              },
            },
            {
              id: {
                not_equals: data.id,
              },
            },
          ],
        },
        limit: 1,
      });

      // If duplicate exists, add timestamp suffix
      if (existingArticles.docs.length > 0) {
        return formatSlugWithTimestamp(data.germanTab.germanTitle, true);
      }

      return baseSlug;
    }

    // On update: Update to English enhanced title if it becomes available and no German exists
    if (
      operation === 'update' &&
      data?.englishTab?.enhancedTitle &&
      !data?.hasGermanTranslation &&
      !value
    ) {
      return formatSlug(data.englishTab.enhancedTitle);
    }

    return value;
  } catch (error) {
    console.error('Error in slug generation:', error);
    return value; // Fallback to existing value
  }
};

// Mock PayloadCMS for testing
const createMockPayload = (existingArticles: any[] = []) => ({
  find: vi.fn().mockResolvedValue({
    docs: existingArticles,
  }),
});

describe('Articles Collection Implementation Comparison', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should produce identical results for basic slug generation', async () => {
    if (!actualSlugHook) {
      throw new Error('Could not find slug hook in Articles collection');
    }

    const data = {
      englishTab: {
        enhancedTitle: 'Comparison Test Article Title',
      },
    };

    const context = {
      value: undefined,
      data,
      operation: 'create' as const,
      req: { payload: createMockPayload() },
    };

    // Test both implementations with identical input
    const actualResult = await actualSlugHook(context);

    // Reset mocks for fair comparison
    vi.clearAllMocks();
    const testContext = {
      ...context,
      req: { payload: createMockPayload() },
    };
    const testResult = await testSlugHook(testContext);

    expect(actualResult).toBe(testResult);
    expect(actualResult).toBe('comparison-test-article-title');
  });

  it('should produce identical results for duplicate handling', async () => {
    if (!actualSlugHook) {
      throw new Error('Could not find slug hook in Articles collection');
    }

    const existingArticles = [
      { id: 'existing-123', slug: 'duplicate-handling-test' },
    ];

    const data = {
      englishTab: {
        enhancedTitle: 'Duplicate Handling Test',
      },
    };

    // Mock fixed timestamp for consistent comparison
    vi.spyOn(Date.prototype, 'getUTCFullYear').mockReturnValue(2025);
    vi.spyOn(Date.prototype, 'getUTCMonth').mockReturnValue(0);
    vi.spyOn(Date.prototype, 'getUTCDate').mockReturnValue(28);
    vi.spyOn(Date.prototype, 'getUTCHours').mockReturnValue(15);
    vi.spyOn(Date.prototype, 'getUTCMinutes').mockReturnValue(30);
    vi.spyOn(Date.prototype, 'getUTCSeconds').mockReturnValue(45);

    const context1 = {
      value: undefined,
      data,
      operation: 'create' as const,
      req: { payload: createMockPayload(existingArticles) },
    };

    const context2 = {
      value: undefined,
      data,
      operation: 'create' as const,
      req: { payload: createMockPayload(existingArticles) },
    };

    const actualResult = await actualSlugHook(context1);
    const testResult = await testSlugHook(context2);

    expect(actualResult).toBe(testResult);
    expect(actualResult).toBe('duplicate-handling-test-20250128153045');
  });

  it('should produce identical results for German translation updates', async () => {
    if (!actualSlugHook) {
      throw new Error('Could not find slug hook in Articles collection');
    }

    const data = {
      id: 'article-comparison-123',
      germanTab: {
        germanTitle: 'Deutsche Überschrift Vergleichstest',
      },
      hasGermanTranslation: true,
    };

    const context1 = {
      value: 'existing-english-slug',
      data,
      operation: 'update' as const,
      req: { payload: createMockPayload() },
    };

    const context2 = {
      value: 'existing-english-slug',
      data,
      operation: 'update' as const,
      req: { payload: createMockPayload() },
    };

    const actualResult = await actualSlugHook(context1);
    const testResult = await testSlugHook(context2);

    expect(actualResult).toBe(testResult);
    expect(actualResult).toBe('deutsche-berschrift-vergleichstest');
  });

  it('should produce identical results for error handling', async () => {
    if (!actualSlugHook) {
      throw new Error('Could not find slug hook in Articles collection');
    }

    // Mock database error
    const mockPayload1 = {
      find: vi.fn().mockRejectedValue(new Error('Database error')),
    };
    const mockPayload2 = {
      find: vi.fn().mockRejectedValue(new Error('Database error')),
    };

    const data = {
      englishTab: {
        enhancedTitle: 'Error Handling Test',
      },
    };

    const context1 = {
      value: 'error-fallback-slug',
      data,
      operation: 'create' as const,
      req: { payload: mockPayload1 },
    };

    const context2 = {
      value: 'error-fallback-slug',
      data,
      operation: 'create' as const,
      req: { payload: mockPayload2 },
    };

    const actualResult = await actualSlugHook(context1);
    const testResult = await testSlugHook(context2);

    expect(actualResult).toBe(testResult);
    expect(actualResult).toBe('error-fallback-slug');
  });

  it('should call payload.find with identical parameters', async () => {
    if (!actualSlugHook) {
      throw new Error('Could not find slug hook in Articles collection');
    }

    const data = {
      englishTab: {
        enhancedTitle: 'Parameter Comparison Test',
      },
    };

    const mockPayload1 = createMockPayload();
    const mockPayload2 = createMockPayload();

    const context1 = {
      value: undefined,
      data,
      operation: 'create' as const,
      req: { payload: mockPayload1 },
    };

    const context2 = {
      value: undefined,
      data,
      operation: 'create' as const,
      req: { payload: mockPayload2 },
    };

    await actualSlugHook(context1);
    await testSlugHook(context2);

    // Both should make identical database calls
    expect(mockPayload1.find).toHaveBeenCalledWith({
      collection: 'articles',
      where: {
        slug: {
          equals: 'parameter-comparison-test',
        },
      },
      limit: 1,
    });

    expect(mockPayload2.find).toHaveBeenCalledWith({
      collection: 'articles',
      where: {
        slug: {
          equals: 'parameter-comparison-test',
        },
      },
      limit: 1,
    });

    // Verify the calls are identical
    expect(mockPayload1.find.mock.calls[0]).toEqual(
      mockPayload2.find.mock.calls[0]
    );
  });
});
