/**
 * Firecrawl Error Analysis Test
 * Demonstrates the improved error handling and user-friendly messaging
 */

import { analyzeFirecrawlError } from '../enhanced-client';

describe('Firecrawl Error Analysis', () => {
  const testUrl = 'https://example.com/test-article';

  test('should detect 502 Bad Gateway errors', () => {
    const result = analyzeFirecrawlError(
      'Unexpected error occurred while trying to scrape URL. Status code: 502',
      testUrl
    );

    expect(result.userMessage).toBe(
      'Website temporarily unavailable (502 Bad Gateway)'
    );
    expect(result.isTemporary).toBe(true);
    expect(result.errorType).toBe('http_502');
    expect(result.suggestion).toContain('gateway is having issues');
  });

  test('should detect 503 Service Unavailable errors', () => {
    const result = analyzeFirecrawlError(
      'Service temporarily unavailable. Status code: 503',
      testUrl
    );

    expect(result.userMessage).toBe('Website service unavailable (503)');
    expect(result.isTemporary).toBe(true);
    expect(result.errorType).toBe('http_503');
    expect(result.suggestion).toContain('maintenance or overloaded');
  });

  test('should detect 404 Not Found errors', () => {
    const result = analyzeFirecrawlError(
      'Page not found. Status code: 404',
      testUrl
    );

    expect(result.userMessage).toBe('Article not found (404)');
    expect(result.isTemporary).toBe(false);
    expect(result.errorType).toBe('http_404');
    expect(result.suggestion).toContain('moved or deleted');
  });

  test('should detect rate limiting errors', () => {
    const result = analyzeFirecrawlError(
      'Too many requests. Status code: 429',
      testUrl
    );

    expect(result.userMessage).toBe('Rate limited by website');
    expect(result.isTemporary).toBe(true);
    expect(result.errorType).toBe('rate_limited');
    expect(result.suggestion).toContain('retry later');
  });

  test('should detect timeout errors', () => {
    const result = analyzeFirecrawlError(
      'Request timed out after 30 seconds',
      testUrl
    );

    expect(result.userMessage).toBe('Request timeout while scraping website');
    expect(result.isTemporary).toBe(true);
    expect(result.errorType).toBe('timeout');
    expect(result.suggestion).toContain('often temporary');
  });

  test('should detect API authentication errors', () => {
    const result = analyzeFirecrawlError('Invalid API key provided', testUrl);

    expect(result.userMessage).toBe('Firecrawl API authentication error');
    expect(result.isTemporary).toBe(false);
    expect(result.errorType).toBe('auth_error');
    expect(result.suggestion).toContain('API key configuration');
  });

  test('should handle unknown errors gracefully', () => {
    const result = analyzeFirecrawlError(
      'Some unknown error occurred',
      testUrl
    );

    expect(result.userMessage).toBe(
      'Extraction error: Some unknown error occurred'
    );
    expect(result.isTemporary).toBe(false);
    expect(result.errorType).toBe('unknown');
    expect(result.suggestion).toContain('manual investigation');
  });

  test('should differentiate temporary vs permanent errors', () => {
    const temporaryError = analyzeFirecrawlError('Status code: 502', testUrl);
    const permanentError = analyzeFirecrawlError('Status code: 404', testUrl);

    expect(temporaryError.isTemporary).toBe(true);
    expect(permanentError.isTemporary).toBe(false);
  });
});
