/**
 * Unified Content Enhancement API
 * Single API call that consolidates all 7 original prompt functions
 * Provides comprehensive German/English enhancement with full analytics
 *
 * <AUTHOR> Blick Development Team
 * @created 2025-07-07
 * @sprint Prompt Refactoring Project
 */

import OpenAI from 'openai';
import { zodTextFormat } from 'openai/helpers/zod';
import {
  cleanContent,
  cleanTitle,
  getCharacterReport,
} from '@/lib/utils/character-cleaning';
import {
  SIMPLIFIED_CONTENT_ENHANCEMENT_PROMPT,
  formatUnifiedPrompt,
  formatRetryPrompt,
  QUALITY_THRESHOLDS,
} from './prompts-unified';
import { withRateLimit } from './rate-limiter';

import {
  type DualLanguageEnhancement,
  type EnglishOnlyEnhancement,
  EnglishOnlyEnhancementSchema,
  transformToLegacyFormat,
  type UnifiedContentEnhancement,
  UnifiedContentEnhancementSchema,
  SimplifiedEnglishOnlySchema,
} from './schemas';

/**
 * Clean content and fix HTML tag imbalances
 */
function cleanAndFixHtml(content: string): string {
  // First apply standard content cleaning
  let cleaned = cleanContent(content);

  // Simple HTML tag balance fix - ensure all <p> tags are properly closed
  // Count open and close p tags
  const openPTags = (cleaned.match(/<p[^>]*>/g) || []).length;
  const closePTags = (cleaned.match(/<\/p>/g) || []).length;

  // If we have more open tags than close tags, add missing close tags
  if (openPTags > closePTags) {
    const missingCloseTags = openPTags - closePTags;
    for (let i = 0; i < missingCloseTags; i++) {
      cleaned += '</p>';
    }
  }

  // If we have more close tags than open tags, remove extra close tags
  if (closePTags > openPTags) {
    const extraCloseTags = closePTags - openPTags;
    for (let i = 0; i < extraCloseTags; i++) {
      const lastCloseTag = cleaned.lastIndexOf('</p>');
      if (lastCloseTag !== -1) {
        cleaned =
          cleaned.substring(0, lastCloseTag) +
          cleaned.substring(lastCloseTag + 4);
      }
    }
  }

  return cleaned;
}

/**
 * Convert plain text content to HTML with natural paragraph grouping
 * Groups related content into flowing paragraphs for better readability
 */
function convertPlainTextToHtml(plainText: string): string {
  if (!plainText || typeof plainText !== 'string') {
    return '';
  }

  const lines = plainText
    .split('\n')
    .map(line => line.trim())
    .filter(line => line.length > 0);
  const result: string[] = [];
  let currentParagraph: string[] = [];

  for (const line of lines) {
    // Check if line is a heading
    if (line.startsWith('### ')) {
      // Finish current paragraph if exists
      if (currentParagraph.length > 0) {
        result.push(`<p>${currentParagraph.join(' ')}</p>`);
        currentParagraph = [];
      }
      // Add heading
      result.push(`<h3>${line.replace('### ', '')}</h3>`);
    } else if (line.startsWith('## ')) {
      // Finish current paragraph if exists
      if (currentParagraph.length > 0) {
        result.push(`<p>${currentParagraph.join(' ')}</p>`);
        currentParagraph = [];
      }
      // Add heading
      result.push(`<h2>${line.replace('## ', '')}</h2>`);
    } else if (line.startsWith('# ')) {
      // Finish current paragraph if exists
      if (currentParagraph.length > 0) {
        result.push(`<p>${currentParagraph.join(' ')}</p>`);
        currentParagraph = [];
      }
      // Add heading
      result.push(`<h1>${line.replace('# ', '')}</h1>`);
    } else if (line.startsWith('#### ')) {
      // Finish current paragraph if exists
      if (currentParagraph.length > 0) {
        result.push(`<p>${currentParagraph.join(' ')}</p>`);
        currentParagraph = [];
      }
      // Add heading
      result.push(`<h4>${line.replace('#### ', '')}</h4>`);
    } else if (line.startsWith('##### ')) {
      // Finish current paragraph if exists
      if (currentParagraph.length > 0) {
        result.push(`<p>${currentParagraph.join(' ')}</p>`);
        currentParagraph = [];
      }
      // Add heading
      result.push(`<h5>${line.replace('##### ', '')}</h5>`);
    } else if (line.startsWith('###### ')) {
      // Finish current paragraph if exists
      if (currentParagraph.length > 0) {
        result.push(`<p>${currentParagraph.join(' ')}</p>`);
        currentParagraph = [];
      }
      // Add heading
      result.push(`<h6>${line.replace('###### ', '')}</h6>`);
    } else {
      // Regular content - add to current paragraph
      currentParagraph.push(line);
    }
  }

  // Don't forget the last paragraph
  if (currentParagraph.length > 0) {
    result.push(`<p>${currentParagraph.join(' ')}</p>`);
  }

  return result.join('\n');
}

/**
 * Enhanced validation for plain text content
 */
function validatePlainTextCompletion(content: string): {
  isComplete: boolean;
  issues: string[];
  wordCount: number;
} {
  const issues: string[] = [];
  const wordCount = content.trim().split(/\s+/).length;

  // Check for proper sentence endings
  const endsWithPunctuation = /[.!?]$/.test(content.trim());
  if (!endsWithPunctuation) {
    issues.push('🚨 CRITICAL: Content does not end with proper punctuation');
  }

  // Check for incomplete phrases (common truncation patterns)
  const commonIncompleteEndings = [
    /\b(a|an|the|to|of|in|on|at|for|with|by)$/i,
    /\b(and|or|but|so|as|if|when|while|that|which)$/i,
    /\b(will|would|could|should|might|may|can)$/i,
    // 🔧 NEW: Incomplete company/proper noun patterns
    /\b[A-Z][a-z]*,?\s*$/i, // Single capitalized word at end (like "Ko")
    /\b[A-Z][a-z]*\s+[A-Z][a-z]*,?\s*$/i, // Two capitalized words (like "Daimler Truck, Ko")
    // 🔧 NEW: Common financial/business truncation patterns
    /\b(Company|Corporation|Corp|Inc|Ltd|AG|GmbH),?\s*$/i,
    /\b(like|such|including|among|between),?\s*$/i,
    /\b(market|stock|share|price|value|growth),?\s*$/i,
    // 🔧 NEW: Sentence structure patterns that indicate incompletion
    /\b(companies?|sectors?|industries?)\s+[a-z]+,?\s*$/i,
    /\b(performance|trajectory|outlook)\s+(of|for|and),?\s*$/i,
  ];

  const hasIncompleteEnding = commonIncompleteEndings.some(pattern =>
    pattern.test(content.trim())
  );

  if (hasIncompleteEnding) {
    issues.push(
      '🚨 CRITICAL: Content appears to end mid-sentence with incomplete phrase'
    );
  }

  // Check word count
  if (wordCount < 200) {
    issues.push(
      `⚠️ Content below minimum length (${wordCount} words, expected 600-750)`
    );
  }

  return {
    isComplete: issues.length === 0,
    issues,
    wordCount,
  };
}

/**
 * Check if we're in build time (should not initialize clients)
 */
function isBuildTime(): boolean {
  return (
    (process.env.NODE_ENV === 'production' &&
      process.env.NEXT_PHASE === 'phase-production-build') ||
    process.argv.includes('build') ||
    process.env.BUILDING === 'true'
  );
}

// Initialize OpenAI client with error handling (only during runtime)
let openai: OpenAI | null = null;

if (!isBuildTime()) {
  try {
    if (process.env.OPENAI_API_KEY) {
      openai = new OpenAI({
        apiKey: process.env.OPENAI_API_KEY,
      });
      console.log('✅ OpenAI Unified Enhancement client initialized');
    } else {
      console.warn(
        '⚠️ OPENAI_API_KEY not found - Unified Enhancement will not function'
      );
    }
  } catch (error) {
    console.error(
      '❌ Failed to initialize OpenAI client for unified enhancement:',
      error
    );
  }
}

/**
 * Performance monitoring for unified enhancement
 */
interface UnifiedMetrics {
  startTime: number;
  endTime?: number;
  processingTime?: number;
  tokensUsed?: number;
  cost?: number;
  success: boolean;
  functionsCovered: string[];
  apiCallsReduced: number;
  originalSystemEquivalent: number;
}

const unifiedMetrics: UnifiedMetrics[] = [];

/**
 * Track unified enhancement performance
 */
function trackUnifiedMetrics(
  _operation: string,
  startTime: number,
  success: boolean,
  tokensUsed?: number,
  functionsCovered: string[] = [],
  apiCallsReduced: number = 4
): void {
  const endTime = Date.now();
  const processingTime = endTime - startTime;
  const cost = tokensUsed ? tokensUsed * 0.00002 : undefined; // GPT-4o pricing

  unifiedMetrics.push({
    startTime,
    endTime,
    processingTime,
    tokensUsed,
    cost,
    success,
    functionsCovered,
    apiCallsReduced,
    originalSystemEquivalent: apiCallsReduced + 1, // +1 for the unified call
  });

  // Keep only last 100 metrics
  if (unifiedMetrics.length > 100) {
    unifiedMetrics.shift();
  }

  console.log(`📊 Unified Enhancement Metrics:
    - Processing Time: ${processingTime}ms
    - Functions Covered: ${functionsCovered.join(', ')}
    - API Calls Reduced: ${apiCallsReduced}
    - Tokens Used: ${tokensUsed || 'Unknown'}
    - Cost: $${cost?.toFixed(4) || 'Unknown'}
    - Success: ${success ? '✅' : '❌'}`);
}

/**
 * Get performance statistics for unified enhancement
 */
export function getUnifiedPerformanceStats() {
  const recent = unifiedMetrics.slice(-20); // Last 20 calls
  const successful = recent.filter(m => m.success);

  if (successful.length === 0) {
    return {
      totalCalls: recent.length,
      successRate: 0,
      averageProcessingTime: 0,
      totalTokens: 0,
      estimatedCost: 0,
      averageApiCallsReduced: 0,
      totalFunctionsCovered: [],
    };
  }

  const allFunctions = successful.flatMap(m => m.functionsCovered);
  const uniqueFunctions = [...new Set(allFunctions)];

  return {
    totalCalls: recent.length,
    successRate: (successful.length / recent.length) * 100,
    averageProcessingTime:
      successful.reduce((acc, m) => acc + (m.processingTime || 0), 0) /
      successful.length,
    totalTokens: successful.reduce((acc, m) => acc + (m.tokensUsed || 0), 0),
    estimatedCost: successful.reduce((acc, m) => acc + (m.cost || 0), 0),
    averageApiCallsReduced:
      successful.reduce((acc, m) => acc + m.apiCallsReduced, 0) /
      successful.length,
    totalFunctionsCovered: uniqueFunctions,
    functionsPerCall: allFunctions.length / successful.length,
  };
}

/**
 * Validate unified enhancement output (streamlined version)
 */
function validateUnifiedOutput(result: UnifiedContentEnhancement): {
  isValid: boolean;
  issues: string[];
  qualityScore: number;
} {
  const issues: string[] = [];
  let qualityScore = 100;

  // Content length validation
  if (result.enhancedGerman.content.length < 100) {
    issues.push('German content too short');
    qualityScore -= 20;
  }
  if (result.enhancedEnglish.content.length < 100) {
    issues.push('English content too short');
    qualityScore -= 20;
  }

  // Title length validation
  if (
    result.enhancedGerman.title.length <
      QUALITY_THRESHOLDS.minimumTitleLength ||
    result.enhancedGerman.title.length > QUALITY_THRESHOLDS.maximumTitleLength
  ) {
    issues.push('German title length out of range (50-60 chars for AI output)');
    qualityScore -= 10;
  }

  if (
    result.enhancedEnglish.title.length <
      QUALITY_THRESHOLDS.minimumTitleLength ||
    result.enhancedEnglish.title.length > QUALITY_THRESHOLDS.maximumTitleLength
  ) {
    issues.push(
      'English title length out of range (50-60 chars for AI output)'
    );
    qualityScore -= 10;
  }

  // Quality thresholds
  if (result.quality.contentScore < QUALITY_THRESHOLDS.minimumContentScore) {
    issues.push(
      `Content quality score below threshold (${result.quality.contentScore} < ${QUALITY_THRESHOLDS.minimumContentScore})`
    );
    qualityScore -= 15;
  }

  if (
    result.quality.relevanceScore < QUALITY_THRESHOLDS.minimumRelevanceScore
  ) {
    issues.push(
      `Market relevance score below threshold (${result.quality.relevanceScore} < ${QUALITY_THRESHOLDS.minimumRelevanceScore})`
    );
    qualityScore -= 10;
  }

  // Keywords validation
  const germanKeywords = result.enhancedGerman.keywords;
  const englishKeywords = result.enhancedEnglish.keywords;

  if (
    germanKeywords.length < QUALITY_THRESHOLDS.minimumKeywords ||
    germanKeywords.length > QUALITY_THRESHOLDS.maximumKeywords
  ) {
    issues.push(
      `German keywords count out of range (${germanKeywords.length}, expected 5-10)`
    );
    qualityScore -= 5;
  }

  if (
    englishKeywords.length < QUALITY_THRESHOLDS.minimumKeywords ||
    englishKeywords.length > QUALITY_THRESHOLDS.maximumKeywords
  ) {
    issues.push(
      `English keywords count out of range (${englishKeywords.length}, expected 5-10)`
    );
    qualityScore -= 5;
  }

  return {
    isValid: issues.length === 0,
    issues,
    qualityScore: Math.max(0, qualityScore),
  };
}

/**
 * Apply character cleaning to unified output (streamlined version)
 */
function cleanUnifiedOutput(
  result: UnifiedContentEnhancement
): UnifiedContentEnhancement {
  return {
    ...result,
    enhancedGerman: {
      ...result.enhancedGerman,
      title: cleanTitle(result.enhancedGerman.title),
      content: cleanContent(result.enhancedGerman.content),
      summary: cleanContent(result.enhancedGerman.summary),
      keywords: result.enhancedGerman.keywords.map(keyword =>
        cleanContent(keyword)
      ),
    },
    enhancedEnglish: {
      ...result.enhancedEnglish,
      title: cleanTitle(result.enhancedEnglish.title),
      content: cleanContent(result.enhancedEnglish.content),
      summary: cleanContent(result.enhancedEnglish.summary),
      keywords: result.enhancedEnglish.keywords.map(keyword =>
        cleanContent(keyword)
      ),
    },
    quality: {
      ...result.quality,
    },
  };
}

/**
 * English-Only Content Enhancement - Streamlined Function
 * Direct German→English enhanced content transformation
 * Replaces dual-language system with focused English-only output
 *
 * @param title Original German title
 * @param content Original German content
 * @param keyPoints Optional key points to emphasize
 * @param options Configuration options
 * @returns Enhanced English content result with metrics
 */
export async function englishOnlyContentEnhancement(
  title: string,
  content: string,
  keyPoints: string[] = [],
  options: {
    temperature?: number;
    timeout?: number;
    includeProcessingMetadata?: boolean;
    _retryCount?: number; // Internal retry counter
  } = {}
): Promise<{
  success: boolean;
  data?: EnglishOnlyEnhancement;
  error?: string;
  validation: {
    isValid: boolean;
    issues: string[];
    qualityScore: number;
  };
  metrics: {
    processingTime: number;
    tokenUsage: number;
    apiCalls: number;
    functionsConsolidated: string[];
    costReduction: number;
  };
  characterCleaning?: {
    hasIssues: boolean;
    issues: string[];
    cleanedLength: number;
    originalLength: number;
  };
}> {
  const startTime = Date.now();
  const retryCount = options._retryCount || 0;

  if (!openai) {
    return {
      success: false,
      error: 'OpenAI client not initialized - check OPENAI_API_KEY',
      validation: {
        isValid: false,
        issues: ['OpenAI client not available'],
        qualityScore: 0,
      },
      metrics: {
        processingTime: Date.now() - startTime,
        tokenUsage: 0,
        apiCalls: 0,
        functionsConsolidated: [],
        costReduction: 0,
      },
    };
  }

  try {
    // Validate input size limits
    const maxContentSize = 10 * 1024 * 1024; // 10MB limit (OpenAI API limit)

    if (!content || content.length < 20) {
      throw new Error(
        'Content too short for enhancement (minimum 20 characters)'
      );
    }

    if (content.length > maxContentSize) {
      throw new Error(
        `Content too large for enhancement: ${(content.length / (1024 * 1024)).toFixed(2)}MB exceeds ${(maxContentSize / (1024 * 1024)).toFixed(0)}MB limit`
      );
    }

    // Title validation removed - no minimum character requirement

    console.log('🚀 Starting English-only content enhancement...');
    console.log(
      `📄 Processing: "${title}" (${content.length} chars, ${(content.length / (1024 * 1024)).toFixed(2)}MB)`
    );

    // Prepare unified prompt (use retry prompt if this is a retry)
    const isRetry = retryCount > 0;
    const { systemPrompt, userPrompt } = isRetry
      ? formatRetryPrompt(title, content, keyPoints)
      : formatUnifiedPrompt(title, content, keyPoints);

    if (isRetry) {
      console.log('🔄 Using enhanced retry prompt for better completion');
    }

    // 🔧 ENHANCED: Dynamic token limit based on content size to prevent truncation
    const estimatedPromptTokens = Math.ceil(
      (systemPrompt.length + userPrompt.length) / 4
    );
    const safetyBuffer = 1000; // Reserve tokens for JSON structure overhead
    const availableTokens = 128000 - estimatedPromptTokens - safetyBuffer; // GPT-4o actual context limit
    const dynamicTokenLimit = Math.min(availableTokens, 16000); // Cap at 16000 for quality output

    console.log(
      `📊 Token allocation: Prompt ~${estimatedPromptTokens}, Output limit: ${dynamicTokenLimit}, Available: ${availableTokens}`
    );

    if (dynamicTokenLimit < 8000) {
      console.warn(
        `⚠️ Low token budget (${dynamicTokenLimit}) - consider reducing input content size`
      );
    }

    // Make single structured API call using simplified schema with rate limiting
    const requestId = `english-enhancement-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`;

    // 🔧 SIMPLIFIED APPROACH: Focus on content completion first
    console.log(
      '📝 Using simplified approach: Plain text generation with completion focus'
    );

    const response = await withRateLimit(requestId, async () => {
      return await openai!.responses.parse({
        model: 'gpt-4o-2024-08-06',
        input: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt },
        ],
        text: {
          format: zodTextFormat(
            SimplifiedEnglishOnlySchema, // 🔧 USING SIMPLIFIED SCHEMA
            'simplified_english_enhancement'
          ),
        },
        max_output_tokens: Math.min(dynamicTokenLimit, 8000), // 🔧 REDUCED: Focus on completion over length
        temperature: options.temperature || 0.7, // 🔧 INCREASED: 0.4 → 0.7 for better completion
      });
    });

    if (!response.output_parsed) {
      throw new Error('Failed to parse English-only enhancement response');
    }

    // 🔧 ENHANCED: Monitor token usage to detect truncation issues
    const tokensUsed = response.usage?.output_tokens || 0;
    const tokenUtilization = (tokensUsed / dynamicTokenLimit) * 100;

    console.log(
      `📊 Token usage: ${tokensUsed}/${dynamicTokenLimit} (${tokenUtilization.toFixed(1)}%)`
    );

    // Warning when approaching token limits (could cause truncation)
    if (tokenUtilization > 85) {
      console.warn(
        `⚠️ High token usage (${tokenUtilization.toFixed(1)}%) - potential truncation risk`
      );
    }

    if (tokenUtilization > 95) {
      console.error(
        `🚨 Critical token usage (${tokenUtilization.toFixed(1)}%) - likely truncation occurred`
      );
    }

    const result = response.output_parsed;

    // 🔧 PHASE 1: Validate plain text completion first
    const plainTextValidation = validatePlainTextCompletion(
      result.enhancedContent.content
    );
    console.log(
      `📊 Plain text validation: ${plainTextValidation.isComplete ? '✅ Complete' : '❌ Issues detected'}`
    );
    console.log(`📊 Word count: ${plainTextValidation.wordCount} words`);

    if (plainTextValidation.issues.length > 0) {
      console.warn('⚠️ Content validation issues detected:');
      plainTextValidation.issues.forEach(issue => console.warn(`   ${issue}`));
    }

    // 🔧 RETRY MECHANISM: If truncation detected, retry with enhanced settings
    if (!plainTextValidation.isComplete) {
      const hasCriticalTruncation = plainTextValidation.issues.some(issue =>
        issue.includes('🚨 CRITICAL')
      );

      if (hasCriticalTruncation && retryCount === 0) {
        console.warn(
          '🔄 RETRY: Truncation detected, attempting retry with enhanced completion settings...'
        );
        console.warn(
          `   Original ending: "${result.enhancedContent.content.trim().slice(-50)}"`
        );

        // Retry with enhanced completion settings
        const retryResult = await englishOnlyContentEnhancement(
          title,
          content,
          keyPoints,
          {
            ...options,
            temperature: 0.5, // Lower temperature for better completion
            timeout: (options.timeout || 30000) + 15000, // Extra 15s timeout
            _retryCount: 1, // Internal retry counter
          }
        );

        if (retryResult.success) {
          console.log('✅ RETRY SUCCESS: Truncation issue resolved');
        } else {
          console.error(
            '❌ RETRY FAILED: Still experiencing truncation issues'
          );
        }

        return retryResult;
      } else if (hasCriticalTruncation && retryCount > 0) {
        console.error(
          '❌ RETRY LIMIT REACHED: Still truncated after retry - accepting partial result'
        );
        // Continue with truncated result rather than infinite retry
      }
    }

    // 🔧 PHASE 2: Convert plain text to HTML
    const htmlContent = convertPlainTextToHtml(result.enhancedContent.content);
    console.log(
      `📝 Converted plain text to HTML (${htmlContent.length} chars)`
    );

    // Apply character cleaning to English content (simplified)
    const cleanedResult: EnglishOnlyEnhancement = {
      enhancedContent: {
        title: cleanTitle(result.enhancedContent.title),
        content: cleanContent(htmlContent), // 🔧 TWO-PHASE: Use converted HTML content
        summary: cleanContent(result.enhancedContent.summary),
        keyInsights: result.enhancedContent.keyInsights.map((insight: string) =>
          cleanContent(insight)
        ),
        keywords: result.enhancedContent.keywords.map((keyword: string) =>
          cleanContent(keyword)
        ),
      },
      quality: {
        contentScore: result.quality.contentScore,
        wordCount: plainTextValidation.wordCount, // 🔧 USE ACTUAL: Use measured word count
        completionStatus: plainTextValidation.isComplete
          ? 'complete'
          : 'truncated', // 🔧 DYNAMIC: Based on validation
      },
    };

    // Generate character cleaning report
    const originalText = JSON.stringify(result);
    const characterReport = getCharacterReport(originalText);

    // Validate English-only output
    const validation = validateEnglishOnlyOutput(cleanedResult);

    // 🔧 ENHANCED: Validation with reasonable thresholds
    if (!validation.isValid) {
      const hasCriticalIssues = validation.issues.some(issue =>
        issue.includes('🚨 CRITICAL')
      );

      // Log all issues but only block on truly critical ones
      console.warn('⚠️ Content validation issues detected:');
      validation.issues.forEach(issue => {
        if (issue.includes('🚨')) {
          console.error(`   ${issue}`);
        } else {
          console.warn(`   ${issue}`);
        }
      });

      // Only throw error for severe truncation (very short content)
      if (hasCriticalIssues && validation.qualityScore < 50) {
        // If token utilization is high, it's likely a token limit issue
        if (tokenUtilization > 90) {
          console.error(
            `   📊 High token usage (${tokenUtilization.toFixed(1)}%) likely caused truncation`
          );
          console.error(`   💡 Recommendations:`);
          console.error(`      - Reduce input content size by 20-30%`);
          console.error(`      - Use shorter, more focused prompts`);
          console.error(
            `      - Consider processing content in smaller chunks`
          );

          throw new Error(
            `Content truncation detected due to token limits. Token usage: ${tokenUtilization.toFixed(1)}%. ` +
              `Content ends with incomplete sentences. Reduce input size or use shorter prompts.`
          );
        } else {
          console.error(
            `   📊 Token usage appears normal (${tokenUtilization.toFixed(1)}%)`
          );
          console.error(
            `   💡 This may be a model response quality issue or prompt interpretation problem`
          );

          // 🔧 TEMPORARY: Only throw for very severe cases to allow testing
          if (validation.qualityScore < 30) {
            throw new Error(
              `Content severely incomplete (quality score: ${validation.qualityScore}). ` +
                `Token usage: ${tokenUtilization.toFixed(1)}%. Issues: ${validation.issues.filter(i => i.includes('🚨')).join(', ')}`
            );
          } else {
            console.warn(
              `   ⚠️ Proceeding with content despite quality issues (score: ${validation.qualityScore})`
            );
          }
        }
      } else {
        console.warn(
          `   ⚠️ Minor validation issues detected but proceeding (quality score: ${validation.qualityScore})`
        );
      }
    }

    // Extract processing metadata
    const processingMetadata = {
      modelUsed: 'gpt-4o-2024-08-06',
      processingTime: Date.now() - startTime,
      tokenUsage: {
        prompt: response.usage?.input_tokens || 0,
        completion: response.usage?.output_tokens || 0,
        total: response.usage?.total_tokens || 0,
      },
    };

    // Track performance metrics for English-only approach
    const functionsConsolidated = [
      'Direct English Enhancement',
      'SEO Optimization',
      'International Context Addition',
      'Quality Assessment',
      'Readability Optimization',
    ];

    const processingTime = Date.now() - startTime;
    const tokenUsage = processingMetadata.tokenUsage.total || 0;
    const estimatedOldCost = 0.12; // Estimated cost of old dual-language system
    const newCost = tokenUsage * 0.00002 || 0.03; // Current cost (even lower than dual-language)
    const costReduction =
      ((estimatedOldCost - newCost) / estimatedOldCost) * 100;

    trackUnifiedMetrics(
      'englishOnlyContentEnhancement',
      startTime,
      true,
      tokenUsage,
      functionsConsolidated,
      7 // Even more API calls reduced (no German enhancement step)
    );

    console.log('✅ English-only enhancement completed successfully');
    console.log(`📊 Performance: ${processingTime}ms, ${tokenUsage} tokens`);
    console.log(`💰 Cost reduction: ${costReduction.toFixed(1)}%`);
    console.log(`🎯 Functions consolidated: ${functionsConsolidated.length}`);
    console.log(`⚡ Direct transformation: German → Enhanced English`);

    return {
      success: true,
      data: cleanedResult,
      validation,
      metrics: {
        processingTime,
        tokenUsage,
        apiCalls: 1,
        functionsConsolidated,
        costReduction,
      },
      characterCleaning: characterReport.hasIssues
        ? characterReport
        : undefined,
    };
  } catch (error) {
    const processingTime = Date.now() - startTime;
    console.error('❌ English-only enhancement failed:', error);

    trackUnifiedMetrics(
      'englishOnlyContentEnhancement',
      startTime,
      false,
      0,
      [],
      0
    );

    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : 'Unknown error in English-only enhancement',
      validation: {
        isValid: false,
        issues: [error instanceof Error ? error.message : 'Unknown error'],
        qualityScore: 0,
      },
      metrics: {
        processingTime,
        tokenUsage: 0,
        apiCalls: 0,
        functionsConsolidated: [],
        costReduction: 0,
      },
    };
  }
}

/**
 * Validate English-only enhancement output
 */
function validateEnglishOnlyOutput(result: EnglishOnlyEnhancement): {
  isValid: boolean;
  issues: string[];
  qualityScore: number;
} {
  const issues: string[] = [];
  let qualityScore = 100;

  // 🔧 CRITICAL NEW: Sentence completion validation to detect truncation
  const content = result.enhancedContent.content;
  const trimmedContent = content.trim();
  const endsWithSentencePunctuation = /[.!?]$/.test(trimmedContent);

  if (!endsWithSentencePunctuation) {
    const lastWords = trimmedContent.split(/\s+/).slice(-5).join(' ');
    issues.push(
      `⚠️ WARNING: Content appears truncated - ends with "${lastWords}" instead of proper punctuation`
    );
    qualityScore -= 15; // 🔧 TEMPORARY: Reduced penalty to allow testing
  }

  // 🔧 NEW: Check for common truncation patterns (incomplete phrases)
  const truncationPatterns = [
    /\b(Although|However|Therefore|While|Because|Since|When|If|The|A|An)\s*$/i,
    /\b(and|or|but|so|yet|for|nor)\s*$/i,
    /\b(is|was|are|were|will|would|could|should|might|may)\s*$/i,
    /\b(has|have|had|can|must|shall|do|does|did)\s*$/i,
    /\b(this|that|these|those|such|more|most|some|many)\s*$/i,
  ];

  const foundTruncationPattern = truncationPatterns.some(pattern =>
    pattern.test(trimmedContent)
  );

  if (foundTruncationPattern) {
    const lastWords = trimmedContent.split(/\s+/).slice(-3).join(' ');
    issues.push(
      `🚨 CRITICAL: Content ends with incomplete phrase "${lastWords}" - likely truncated mid-sentence`
    );
    qualityScore -= 25;
  }

  // 🔧 NEW: Check for HTML tag truncation (unclosed tags)
  const openTags = (content.match(/<[^/][^>]*>/g) || []).length;
  const closeTags = (content.match(/<\/[^>]*>/g) || []).length;
  if (openTags !== closeTags) {
    issues.push(
      `⚠️ WARNING: HTML structure incomplete - ${openTags} open tags vs ${closeTags} close tags (will auto-fix)`
    );
    qualityScore -= 5; // 🔧 TEMPORARY: Reduced penalty, we can fix HTML issues automatically
  }

  // 🔧 NEW: Check for JSON/structured data at the end (indicates truncation)
  if (
    trimmedContent.includes('{') ||
    trimmedContent.includes('[') ||
    trimmedContent.endsWith(',')
  ) {
    issues.push(
      `🚨 CRITICAL: Content contains unprocessed JSON/structured data - likely parsing truncation`
    );
    qualityScore -= 25;
  }

  // Content length validation (600-750 words) - Enhanced for truncation detection
  const contentWordCount = result.enhancedContent.content.split(/\s+/).length;
  if (contentWordCount < 300) {
    // 🔧 TEMPORARY: Further relaxed to 300 words to test completion fixes
    if (contentWordCount < 150) {
      // 🔧 TEMPORARY: Relaxed from 200 to 150 words
      issues.push(
        `🚨 CRITICAL: Content severely truncated (${contentWordCount} words, expected 600-750) - likely token limit issue`
      );
      qualityScore -= 25; // Higher penalty for severe truncation
    } else {
      issues.push(
        `⚠️ Content below target length (${contentWordCount} words, expected 600-750) - possible truncation`
      );
      qualityScore -= 15;
    }
  } else if (contentWordCount > 750) {
    issues.push(
      `Content exceeds target length (${contentWordCount} words, expected 600-750)`
    );
    qualityScore -= 10; // Lower penalty for exceeding length
  }

  // Title length validation (50-60 characters)
  const titleLength = result.enhancedContent.title.length;
  if (titleLength < 50 || titleLength > 60) {
    issues.push(
      `Title length out of range (${titleLength}, expected 50-60 chars)`
    );
    qualityScore -= 10;
  }

  // Summary length validation (100-150 characters)
  const summaryLength = result.enhancedContent.summary.length;
  if (summaryLength < 100 || summaryLength > 150) {
    issues.push(
      `Summary length out of range (${summaryLength}, expected 100-150 chars)`
    );
    qualityScore -= 10;
  }

  // Quality thresholds
  if (result.quality.contentScore < QUALITY_THRESHOLDS.minimumContentScore) {
    issues.push(
      `Content quality score below threshold (${result.quality.contentScore} < ${QUALITY_THRESHOLDS.minimumContentScore})`
    );
    qualityScore -= 15;
  }

  // 🔧 SIMPLIFIED: Check completion status instead of relevance score
  if (result.quality.completionStatus === 'truncated') {
    issues.push('🚨 CRITICAL: Content appears truncated');
    qualityScore -= 30; // Heavy penalty for truncation
  }

  // Keywords validation (3-12 keywords for simplified schema)
  const keywordCount = result.enhancedContent.keywords.length;
  if (keywordCount < 3 || keywordCount > 12) {
    issues.push(`Keywords count out of range (${keywordCount}, expected 3-12)`);
    qualityScore -= 5;
  }

  // Key insights validation (2-5 insights for simplified schema)
  const insightsCount = result.enhancedContent.keyInsights.length;
  if (insightsCount < 2 || insightsCount > 5) {
    issues.push(
      `Key insights count out of range (${insightsCount}, expected 2-5)`
    );
    qualityScore -= 5;
  }

  // 🔧 SIMPLIFIED: Removed company validation as not in simplified schema

  return {
    isValid: issues.length === 0,
    issues,
    qualityScore: Math.max(0, qualityScore), // Ensure score doesn't go negative
  };
}

/**
 * Unified Content Enhancement - Master Function
 * Consolidates all 7 original prompt functions into a single API call
 *
 * @param title Original German title
 * @param content Original German content
 * @param keyPoints Optional key points to emphasize
 * @param options Configuration options
 * @returns Comprehensive enhancement result with all metrics
 */
export async function unifiedContentEnhancement(
  title: string,
  content: string,
  keyPoints: string[] = [],
  options: {
    temperature?: number;
    enableFallback?: boolean;
    timeout?: number;
    includeProcessingMetadata?: boolean;
  } = {}
): Promise<{
  success: boolean;
  data?: UnifiedContentEnhancement;
  legacyFormat?: DualLanguageEnhancement;
  error?: string;
  fallbackUsed?: boolean;
  validation: {
    isValid: boolean;
    issues: string[];
    qualityScore: number;
  };
  metrics: {
    processingTime: number;
    tokenUsage: number;
    apiCalls: number;
    functionsConsolidated: string[];
    apiCallsReduced: number;
    costReduction: number;
  };
  characterCleaning?: {
    hasIssues: boolean;
    issues: string[];
    cleanedLength: number;
    originalLength: number;
  };
}> {
  const startTime = Date.now();

  if (!openai) {
    return {
      success: false,
      error: 'OpenAI client not initialized - check OPENAI_API_KEY',
      validation: {
        isValid: false,
        issues: ['OpenAI client not available'],
        qualityScore: 0,
      },
      metrics: {
        processingTime: Date.now() - startTime,
        tokenUsage: 0,
        apiCalls: 0,
        functionsConsolidated: [],
        apiCallsReduced: 0,
        costReduction: 0,
      },
    };
  }

  try {
    // Validate input
    if (!content || content.length < 20) {
      throw new Error(
        'Content too short for enhancement (minimum 20 characters)'
      );
    }

    // Title validation removed - no minimum character requirement

    console.log('🚀 Starting unified content enhancement...');
    console.log(`📄 Processing: "${title}" (${content.length} chars)`);

    // Prepare unified prompt
    const { systemPrompt, userPrompt } = formatUnifiedPrompt(
      title,
      content,
      keyPoints
    );

    // Make single structured API call using Responses API with rate limiting
    const requestId = `unified-enhancement-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`;
    const response = await withRateLimit(requestId, async () => {
      return await openai!.responses.parse({
        model: 'gpt-4o-2024-08-06',
        input: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt },
        ],
        text: {
          format: zodTextFormat(
            UnifiedContentEnhancementSchema,
            'unified_content_enhancement'
          ),
        },
        max_output_tokens: 16000, // 🔧 CRITICAL FIX: Increased to match single enhancement limits
        temperature: options.temperature || 0.7,
      });
    });

    if (!response.output_parsed) {
      throw new Error('Failed to parse unified enhancement response');
    }

    // 🔧 CRITICAL FIX: Monitor token usage for unified enhancement too
    const unifiedTokensUsed = response.usage?.output_tokens || 0;
    const unifiedTokenLimit = 5000;
    const unifiedTokenUtilization =
      (unifiedTokensUsed / unifiedTokenLimit) * 100;

    console.log(
      `📊 Unified token usage: ${unifiedTokensUsed}/${unifiedTokenLimit} (${unifiedTokenUtilization.toFixed(1)}%)`
    );

    if (unifiedTokenUtilization > 85) {
      console.warn(
        `⚠️ Unified high token usage (${unifiedTokenUtilization.toFixed(1)}%) - potential truncation risk`
      );
    }

    if (unifiedTokenUtilization > 95) {
      console.error(
        `🚨 Unified critical token usage (${unifiedTokenUtilization.toFixed(1)}%) - likely truncation occurred`
      );
    }

    const result = response.output_parsed;

    // 🔧 PHASE 1: Validate plain text completion first
    const plainTextValidation = validatePlainTextCompletion(
      result.enhancedEnglish.content
    );
    console.log(
      `📊 Plain text validation: ${plainTextValidation.isComplete ? '✅ Complete' : '❌ Issues detected'}`
    );
    console.log(`📊 Word count: ${plainTextValidation.wordCount} words`);

    if (plainTextValidation.issues.length > 0) {
      console.warn('⚠️ Content validation issues detected:');
      plainTextValidation.issues.forEach(issue => console.warn(`   ${issue}`));
    }

    // 🔧 PHASE 2: Convert plain text to HTML
    const htmlContent = convertPlainTextToHtml(result.enhancedEnglish.content);
    console.log(
      `📝 Converted plain text to HTML (${htmlContent.length} chars)`
    );

    // Apply character cleaning
    const cleanedResult = cleanUnifiedOutput(result);

    // Generate character cleaning report
    const originalText = JSON.stringify(result);
    const characterReport = getCharacterReport(originalText);

    // Validate output quality
    const validation = validateUnifiedOutput(cleanedResult);

    // Track performance metrics
    const functionsConsolidated = [
      'German Enhancement',
      'English Translation',
      'SEO Metadata Generation',
      'Market Relevance Analysis',
      'Content Quality Assessment',
      'Structure Preservation',
      'Comprehensive Analytics',
    ];

    trackUnifiedMetrics(
      'unifiedContentEnhancement',
      startTime,
      true,
      response.usage?.total_tokens || 0,
      functionsConsolidated,
      6 // API calls reduced
    );

    // Generate legacy format for backward compatibility
    const legacyFormat = transformToLegacyFormat(cleanedResult);

    const processingTime = Date.now() - startTime;
    const tokenUsage = response.usage?.total_tokens || 0;
    const estimatedOldCost = 0.12; // Estimated cost of old 7-prompt system
    const newCost = tokenUsage * 0.00002 || 0.04; // Current cost
    const costReduction =
      ((estimatedOldCost - newCost) / estimatedOldCost) * 100;

    console.log('✅ Unified enhancement completed successfully');
    console.log(`📊 Performance: ${processingTime}ms, ${tokenUsage} tokens`);
    console.log(`💰 Cost reduction: ${costReduction.toFixed(1)}%`);
    console.log(`🎯 Functions consolidated: ${functionsConsolidated.length}`);
    console.log(`⚡ API calls reduced: 6 (from 7 to 1)`);

    return {
      success: true,
      data: cleanedResult,
      legacyFormat,
      validation,
      metrics: {
        processingTime,
        tokenUsage,
        apiCalls: 1,
        functionsConsolidated,
        apiCallsReduced: 6,
        costReduction,
      },
      characterCleaning: characterReport.hasIssues
        ? characterReport
        : undefined,
    };
  } catch (error) {
    const processingTime = Date.now() - startTime;
    console.error('❌ Unified enhancement failed:', error);

    trackUnifiedMetrics(
      'unifiedContentEnhancement',
      startTime,
      false,
      0,
      [],
      0
    );

    // No fallback to legacy system - unified enhancement should work or fail completely
    console.log('❌ Unified enhancement failed - no fallback enabled');
    console.log('💡 This ensures we only use the unified prompt system');

    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : 'Unknown error in unified enhancement',
      validation: {
        isValid: false,
        issues: [error instanceof Error ? error.message : 'Unknown error'],
        qualityScore: 0,
      },
      metrics: {
        processingTime,
        tokenUsage: 0,
        apiCalls: 0,
        functionsConsolidated: [],
        apiCallsReduced: 0,
        costReduction: 0,
      },
    };
  }
}

/**
 * Health check for unified enhancement system
 */
export async function unifiedHealthCheck(): Promise<{
  status: 'healthy' | 'degraded' | 'unhealthy';
  details: Record<string, unknown>;
}> {
  try {
    const testResult = await unifiedContentEnhancement(
      'Test Titel für Health Check',
      'Das ist ein Test-Artikel für das Health Check System. Der Artikel enthält genügend Inhalt, um die Mindestanforderungen zu erfüllen und das unified enhancement System zu testen. Es werden verschiedene Funktionen getestet, einschließlich der deutschen Verbesserung, englischen Übersetzung, SEO-Metadaten-Generierung und Marktrelevanz-Analyse.',
      [],
      { temperature: 0.1, timeout: 30000 }
    );

    const stats = getUnifiedPerformanceStats();

    return {
      status:
        stats.successRate > 90
          ? 'healthy'
          : stats.successRate > 70
            ? 'degraded'
            : 'unhealthy',
      details: {
        ...stats,
        lastTestSuccess: testResult.success,
        lastTestValidation: testResult.validation,
        lastTestMetrics: testResult.metrics,
        timestamp: new Date().toISOString(),
      },
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      details: {
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      },
    };
  }
}

/**
 * Performance monitoring functions are exported above
 */

/**
 * Export for backward compatibility with existing system
 */
export async function enhanceAndTranslateDualLanguageUnified(
  title: string,
  content: string,
  keyPoints: string[] = [],
  options: { temperature?: number } = {}
): Promise<DualLanguageEnhancement> {
  const result = await unifiedContentEnhancement(title, content, keyPoints, {
    temperature: options.temperature,
    enableFallback: true,
  });

  if (!result.success) {
    throw new Error(result.error || 'Unified enhancement failed');
  }

  if (result.legacyFormat || result.fallbackUsed) {
    if (!result.legacyFormat) {
      throw new Error('Legacy format missing from fallback result');
    }
    return result.legacyFormat;
  }

  if (!result.data) {
    throw new Error('Unified enhancement data missing');
  }

  return transformToLegacyFormat(result.data);
}

/**
 * DIAGNOSTIC: Simple non-structured content generation test
 * Uses standard Chat Completions API to isolate schema issues
 */
export async function testNonStructuredCompletion(
  title: string,
  content: string
): Promise<{
  success: boolean;
  rawContent?: string;
  wordCount?: number;
  isComplete?: boolean;
  tokenUsage?: number;
  processingTime?: number;
  error?: string;
}> {
  const startTime = Date.now();

  if (!openai) {
    return {
      success: false,
      error: 'OpenAI client not available',
    };
  }

  try {
    const simplePrompt = `Transform this German financial content into a complete English article of 600-750 words:\n\nTitle: ${title}\n\nContent: ${content}\n\nIMPORTANT: Write a COMPLETE article that ends with proper punctuation. NEVER stop mid-sentence.`;

    console.log('🧪 DIAGNOSTIC: Testing non-structured completion...');

    const response = await openai.chat.completions.create({
      model: 'gpt-4o-2024-08-06',
      messages: [
        {
          role: 'system',
          content:
            'You are a financial content editor. Always write complete articles that end with proper punctuation.',
        },
        {
          role: 'user',
          content: simplePrompt,
        },
      ],
      max_tokens: 16000,
      temperature: 0.7,
    });

    const rawContent = response.choices[0]?.message?.content || '';
    const wordCount = rawContent.trim().split(/\s+/).length;
    const tokenUsage = response.usage?.total_tokens || 0;
    const processingTime = Date.now() - startTime;

    // Simple completion check
    const endsWithPunctuation = /[.!?]$/.test(rawContent.trim());
    const isComplete = endsWithPunctuation && wordCount > 300;

    console.log(`🧪 DIAGNOSTIC RESULTS:`);
    console.log(`   📝 Word count: ${wordCount}`);
    console.log(`   📊 Token usage: ${tokenUsage}`);
    console.log(`   ⏱️ Processing time: ${processingTime}ms`);
    console.log(`   ✅ Ends with punctuation: ${endsWithPunctuation}`);
    console.log(`   🎯 Is complete: ${isComplete}`);
    console.log(`   📄 Last 50 chars: "${rawContent.trim().slice(-50)}"`);

    return {
      success: true,
      rawContent,
      wordCount,
      isComplete,
      tokenUsage,
      processingTime,
    };
  } catch (error) {
    console.error('🧪 DIAGNOSTIC ERROR:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error),
      processingTime: Date.now() - startTime,
    };
  }
}
