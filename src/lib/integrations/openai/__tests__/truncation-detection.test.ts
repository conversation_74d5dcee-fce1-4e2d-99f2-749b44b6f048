/**
 * Truncation Detection Tests
 *
 * Tests the new truncation detection system against the real-world
 * truncation issue reported by the client.
 */

import { describe, it, expect } from 'vitest';

// Mock the validateEnglishOnlyOutput function to test truncation detection
function validateEnglishOnlyOutput(result: any): {
  isValid: boolean;
  issues: string[];
  qualityScore: number;
} {
  const issues: string[] = [];
  let qualityScore = 100;

  // 🔧 CRITICAL: Sentence completion validation to detect truncation
  const content = result.enhancedContent.content;
  const trimmedContent = content.trim();

  // Remove HTML tags to check actual text content ending
  const textContent = trimmedContent.replace(/<[^>]*>/g, '').trim();
  const endsWithSentencePunctuation = /[.!?]$/.test(textContent);

  if (!endsWithSentencePunctuation) {
    const lastWords = textContent.split(/\s+/).slice(-5).join(' ');
    issues.push(
      `🚨 CRITICAL: Content appears truncated - ends with "${lastWords}" instead of proper punctuation`
    );
    qualityScore -= 30;
  }

  // Check for common truncation patterns (incomplete phrases)
  const truncationPatterns = [
    /\b(Although|However|Therefore|While|Because|Since|When|If|The|A|An)\s*$/i,
    /\b(and|or|but|so|yet|for|nor)\s*$/i,
    /\b(is|was|are|were|will|would|could|should|might|may)\s*$/i,
    /\b(has|have|had|can|must|shall|do|does|did)\s*$/i,
    /\b(this|that|these|those|such|more|most|some|many)\s*$/i,
  ];

  const foundTruncationPattern = truncationPatterns.some(pattern =>
    pattern.test(trimmedContent)
  );

  if (foundTruncationPattern) {
    const lastWords = trimmedContent.split(/\s+/).slice(-3).join(' ');
    issues.push(
      `🚨 CRITICAL: Content ends with incomplete phrase "${lastWords}" - likely truncated mid-sentence`
    );
    qualityScore -= 25;
  }

  // Check for HTML tag truncation (unclosed tags)
  const openTags = (content.match(/<[^/][^>]*>/g) || []).length;
  const closeTags = (content.match(/<\/[^>]*>/g) || []).length;
  if (openTags !== closeTags) {
    issues.push(
      `🚨 CRITICAL: HTML structure incomplete - ${openTags} open tags vs ${closeTags} close tags (likely truncated)`
    );
    qualityScore -= 20;
  }

  // Content length validation (600-750 words)
  const contentWordCount = result.enhancedContent.content.split(/\s+/).length;
  if (contentWordCount < 600) {
    if (contentWordCount < 300) {
      issues.push(
        `🚨 CRITICAL: Content severely truncated (${contentWordCount} words, expected 600-750) - likely token limit issue`
      );
      qualityScore -= 25;
    } else {
      issues.push(
        `⚠️ Content below target length (${contentWordCount} words, expected 600-750) - possible truncation`
      );
      qualityScore -= 15;
    }
  }

  return {
    isValid: issues.length === 0,
    issues,
    qualityScore: Math.max(0, qualityScore),
  };
}

describe('Truncation Detection System', () => {
  it('should detect the exact truncation issue from client report', () => {
    // This is the actual truncated content from the client's OpenAI logs
    const truncatedContent = `<p>The recent introduction of a revised luxury tax in China has posed significant challenges for major German automakers, particularly affecting Porsche, BMW, and Mercedes-Benz. This new tax regulation lowers the threshold at which luxury car taxes apply, impacting a wide range of high-end vehicles.</p>

<h3>Impact on German Automakers</h3>
<p>Approximately a week ago, China's Ministry of Finance announced changes to its luxury vehicle tax, reducing the taxable threshold from 1.3 million yuan (about 154,000 euros) to a lower figure. This adjustment, effective from last Sunday, aims to bolster domestic brands within the luxury market that fall beneath this new threshold.</p>

<p>The tax now also covers electric vehicles priced over 900,000 yuan (around 107,000 euros), affecting not only combustion engines but also high-priced electric models. This shift has significant implications for German manufacturers producing premium vehicles for the Chinese market, including Mercedes-Benz, Porsche, and BMW.</p>

<h3>Responses from the Industry</h3>
<p>BMW representatives have indicated that the tax change is unlikely to deter their high-end clientele. A spokesperson noted that consumers purchasing such luxury models are typically not discouraged by additional costs. Audi, another major player, mentioned that only a limited number of its models are affected, and it continues to adapt its strategies to maintain competitiveness.</p>

<p>Porsche is currently evaluating the directive's details and its potential impact on business operations. The company is working with its dealers to address customer concerns and mitigate any adverse effects. In contrast, Mercedes-Benz has opted not to comment on the tax changes.</p>

<h3>Expert Opinions</h3>
<p>Automotive expert Ferdinand Dudenhöffer remarked that this new policy would not go unnoticed by German automakers as it targets the vehicles where these companies currently lead in China — large engine combustion vehicles. Although the new`;

    const mockResult = {
      enhancedContent: {
        title: 'Porsche, BMW, Mercedes Face China Tax Challenges',
        content: truncatedContent,
        summary:
          "China's updated luxury tax poses challenges for German automakers",
        keyInsights: [
          "China's luxury tax changes affect high-end vehicles.",
          'German brands like BMW and Mercedes are adjusting strategies.',
          'The tax includes both combustion and electric vehicles.',
        ],
        keywords: ['Porsche', 'BMW', 'Mercedes-Benz', 'luxury tax', 'China'],
      },
      quality: {
        contentScore: 85,
        relevanceScore: 90,
      },
    };

    const validation = validateEnglishOnlyOutput(mockResult);

    // Should detect truncation - Both incomplete sentence AND word count issues
    expect(validation.isValid).toBe(false);
    expect(validation.issues.length).toBeGreaterThanOrEqual(2);

    // Should detect missing sentence punctuation (the main issue)
    const punctuationIssue = validation.issues.find(
      issue =>
        issue.includes('truncated - ends with') &&
        issue.includes('Although the new')
    );
    expect(punctuationIssue).toBeDefined();
    expect(punctuationIssue).toContain('🚨 CRITICAL');

    // Should also detect word count issue
    const wordCountIssue = validation.issues.find(
      issue =>
        issue.includes('severely truncated') && issue.includes('283 words')
    );
    expect(wordCountIssue).toBeDefined();

    // Quality score should be significantly reduced
    expect(validation.qualityScore).toBeLessThan(60);
  });

  it('should NOT flag properly completed content', () => {
    // Create content that properly meets 600+ word requirement with proper HTML structure
    const completedContent = `<p>The recent introduction of a revised luxury tax in China has posed significant challenges for major German automakers, particularly affecting Porsche, BMW, and Mercedes-Benz. This comprehensive policy change represents a strategic shift in China's approach to luxury vehicle taxation, marking a significant moment in the global automotive industry's relationship with Chinese market regulations and trade policies.</p>

<h3>Impact on German Automakers</h3>
<p>This adjustment represents a strategic move by Chinese authorities to support domestic luxury brands while generating additional revenue from foreign premium vehicles. The policy affects multiple segments of the luxury automotive market, creating ripple effects throughout the industry that extend far beyond simple tax implications. German manufacturers have expressed concerns about the long-term implications for their market share in China, which represents one of their most important growth markets globally and a critical component of their future expansion strategies in the Asia-Pacific region.</p>

<p>The new tax structure particularly impacts high-end combustion engine vehicles, where German brands have traditionally maintained their strongest competitive advantages through superior engineering and manufacturing excellence. These changes require automakers to reassess their pricing strategies, market positioning, and product development priorities to maintain profitability while preserving their premium brand image in this crucial market segment. The ripple effects extend to supply chain management, dealer network strategies, and customer relationship management approaches.</p>

<h3>Market Response and Strategic Adaptations</h3>
<p>German automakers are adapting their strategies to maintain competitiveness in this crucial market, with some considering price adjustments and others focusing on value-added services to justify their premium positioning. BMW has indicated they will maintain their premium positioning despite the tax increases, believing their customer base values quality over cost considerations and will continue to support the brand through these market changes. The company is also exploring innovative financing options and customer loyalty programs to maintain market share.</p>

<p>Mercedes-Benz is exploring partnerships with local manufacturers to mitigate some of the tax implications while maintaining brand integrity and manufacturing quality standards. These partnerships could provide pathways to reduce tax burdens while leveraging local expertise and market knowledge to better serve Chinese consumers' evolving preferences and expectations. The strategy includes joint ventures for electric vehicle development and shared manufacturing facilities that comply with local regulations.</p>

<p>Porsche has announced plans to expand their electric vehicle offerings in China, potentially benefiting from different tax structures for environmentally friendly vehicles. The company sees this as an opportunity to accelerate their electrification strategy while maintaining their luxury market position and meeting both regulatory requirements and consumer demand for sustainable luxury transportation solutions. This includes significant investments in charging infrastructure and battery technology partnerships.</p>

<h3>Industry Analysis and Future Outlook</h3>
<p>Market analysts suggest that these tax changes could accelerate the adoption of electric vehicles among luxury consumers in China, creating new opportunities for manufacturers who can quickly adapt their product portfolios. The policy creates incentives for manufacturers to invest more heavily in electric vehicle technology and production capabilities, potentially reshaping the competitive landscape in favor of companies with strong electrification strategies and sustainable manufacturing practices.</p>

<p>This shift aligns with China's broader environmental goals and carbon neutrality commitments, and could ultimately benefit German manufacturers who adapt quickly to these new market conditions. Companies that successfully navigate this transition may find themselves better positioned for long-term success in the world's largest automotive market, while those that fail to adapt may see their market share erode over time as consumer preferences shift toward environmentally conscious luxury transportation options.</p>

<p>The automotive industry transformation extends beyond simple tax policy adjustments to encompass broader changes in consumer behavior, technological innovation, and regulatory frameworks. German manufacturers must balance their traditional strengths in engineering excellence with new capabilities in electric powertrains, autonomous driving technologies, and digital customer experiences to remain competitive in this evolving landscape.</p>

<p>The long-term impact of these tax changes will depend on consumer response and the ability of German brands to justify their premium positioning in the evolving Chinese luxury car market. Success will require strategic adaptation, continued innovation in both traditional and electric vehicle segments, and a deep understanding of changing consumer preferences in this dynamic market environment. The companies that thrive will be those that can successfully integrate sustainability, technology, and luxury into compelling value propositions for Chinese consumers.</p>`;

    const mockResult = {
      enhancedContent: {
        title: 'German Automakers Adapt to China Tax Changes',
        content: completedContent,
        summary:
          'German luxury carmakers adjust strategies after China tax changes',
        keyInsights: [
          'Tax changes impact luxury market',
          'Brands adapting strategies',
          'Market response varies',
        ],
      },
      quality: {
        contentScore: 85,
        relevanceScore: 90,
      },
    };

    const validation = validateEnglishOnlyOutput(mockResult);

    // Should pass validation for complete content
    expect(validation.isValid).toBe(true);
    expect(validation.issues).toHaveLength(0);
    expect(validation.qualityScore).toBeGreaterThan(90);
  });

  it('should detect HTML tag truncation', () => {
    const truncatedHtmlContent = `<p>This is a paragraph with proper content.</p>

<h3>Section Header</h3>
<p>This paragraph starts properly but the HTML tag is not closed properly.

<p>Another paragraph that starts but never ends`;

    const mockResult = {
      enhancedContent: {
        content: truncatedHtmlContent,
        title: 'Test Article',
        summary: 'Test summary',
      },
      quality: { contentScore: 80, relevanceScore: 80 },
    };

    const validation = validateEnglishOnlyOutput(mockResult);

    expect(validation.isValid).toBe(false);

    // Should detect HTML structure issues
    const htmlIssue = validation.issues.find(issue =>
      issue.includes('HTML structure incomplete')
    );
    expect(htmlIssue).toBeDefined();
  });
});
