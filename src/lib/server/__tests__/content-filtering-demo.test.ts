/**
 * Content Filtering Demo Test
 * Demonstrates the improved error handling and logging for non-financial content
 */

import { detectNonFinancialContent } from '../create-candidate';

describe('Content Filtering Improvements', () => {
  test('should detect sports content', () => {
    const result = detectNonFinancialContent(
      'Nach Tour-Erfolg: Ra<PERSON><PERSON><PERSON>: Lange Party-Nacht und ein Paris-Bummel',
      'https://www.stern.de/sport/nach-tour-erfolg--rad-held-lipowitz--lange-party-nacht-und-ein-paris-bummel-35928318.html'
    );

    expect(result.isLikelyNonFinancial).toBe(true);
    expect(result.contentType).toBe('sports');
    expect(result.reason).toContain('sports/athletics');
  });

  test('should detect financial content', () => {
    const result = detectNonFinancialContent(
      'DAX steigt auf neues Allzeithoch - Aktien von BMW und Mercedes im Fokus',
      'https://www.finanzen.net/nachricht/aktien/dax-steigt-bmw-mercedes'
    );

    expect(result.isLikelyNonFinancial).toBe(false);
    expect(result.contentType).toBe('unknown');
  });

  test('should detect politics content', () => {
    const result = detectNonFinancialContent(
      'Bundestag stimmt über neues Gesetz ab - Minister unter Druck',
      'https://example.com/politik/bundestag-abstimmung'
    );

    expect(result.isLikelyNonFinancial).toBe(true);
    expect(result.contentType).toBe('politics');
  });

  test('should detect entertainment content', () => {
    const result = detectNonFinancialContent(
      'Netflix Serie bricht alle Rekorde - Schauspieler gewinnt Oscar',
      'https://example.com/entertainment/netflix-oscar'
    );

    expect(result.isLikelyNonFinancial).toBe(true);
    expect(result.contentType).toBe('entertainment');
  });
});
