/**
 * Enhanced Pipeline Logger
 *
 * Creates unique log files for each pipeline run and ensures complete parity
 * between console output and file logging. All terminal output is captured
 * and written to timestamped log files.
 *
 * Features:
 * - Unique log file per pipeline run with timestamp
 * - Complete console output capture (log, warn, error, info)
 * - Structured logging with context
 * - Pipeline-specific configurations
 * - Real-time file writing with proper formatting
 *
 * <AUTHOR> Blick Development Team
 * @created 2025-07-28
 */

import { promises as fs } from 'fs';
import path from 'path';

export interface PipelineLoggerConfig {
  pipelineName: string;
  enableConsole: boolean;
  enableFile: boolean;
  runId?: string;
}

export interface LogEntry {
  timestamp: string;
  level: 'INFO' | 'WARN' | 'ERROR' | 'DEBUG';
  message: string;
  context?: any;
}

/**
 * Enhanced Pipeline Logger with unique file generation
 */
export class PipelineLogger {
  private config: PipelineLoggerConfig;
  private logFilePath: string;
  private runStartTime: Date;
  private originalConsole: {
    log: typeof console.log;
    warn: typeof console.warn;
    error: typeof console.error;
    info: typeof console.info;
  };

  constructor(config: PipelineLoggerConfig) {
    this.config = config;
    this.runStartTime = new Date();

    // Generate unique run ID if not provided
    if (!config.runId) {
      this.config.runId = this.generateRunId();
    }

    // Create unique log file path
    this.logFilePath = this.createLogFilePath();

    // Store original console methods
    this.originalConsole = {
      log: console.log,
      warn: console.warn,
      error: console.error,
      info: console.info,
    };

    // Initialize logging
    this.initializeLogging();
  }

  /**
   * Generate unique run ID with timestamp
   */
  private generateRunId(): string {
    const now = new Date();
    const timestamp = now.toISOString().replace(/[:.]/g, '-').slice(0, -5); // Remove milliseconds
    return `${timestamp}`;
  }

  /**
   * Create unique log file path for this run
   */
  private createLogFilePath(): string {
    const logsDir = path.join(process.cwd(), 'logs');
    const filename = `${this.config.pipelineName}-${this.config.runId}.log`;
    return path.join(logsDir, filename);
  }

  /**
   * Initialize logging system with console capture
   */
  private async initializeLogging(): Promise<void> {
    if (this.config.enableFile) {
      await this.ensureLogDirectory();
      await this.writeLogHeader();
      this.interceptConsoleOutput();
    }
  }

  /**
   * Ensure logs directory exists
   */
  private async ensureLogDirectory(): Promise<void> {
    const logsDir = path.dirname(this.logFilePath);
    await fs.mkdir(logsDir, { recursive: true });
  }

  /**
   * Write log file header with run information
   */
  private async writeLogHeader(): Promise<void> {
    const header = [
      `=== PIPELINE RUN: ${this.config.pipelineName.toUpperCase()} ===`,
      `Run ID: ${this.config.runId}`,
      `Start Time: ${this.runStartTime.toISOString()}`,
      `Log File: ${path.basename(this.logFilePath)}`,
      `Console Output: ${this.config.enableConsole ? 'Enabled' : 'Disabled'}`,
      `===================================================\n`,
    ].join('\n');

    await this.writeToFile('INFO', header);
  }

  /**
   * Intercept console output and duplicate to file
   */
  private interceptConsoleOutput(): void {
    // Override console.log
    console.log = (...args: any[]) => {
      const message = args
        .map(arg =>
          typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
        )
        .join(' ');

      if (this.config.enableConsole) {
        this.originalConsole.log(...args);
      }
      this.writeToFile('INFO', message);
    };

    // Override console.warn
    console.warn = (...args: any[]) => {
      const message = args
        .map(arg =>
          typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
        )
        .join(' ');

      if (this.config.enableConsole) {
        this.originalConsole.warn(...args);
      }
      this.writeToFile('WARN', message);
    };

    // Override console.error
    console.error = (...args: any[]) => {
      const message = args
        .map(arg =>
          typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
        )
        .join(' ');

      if (this.config.enableConsole) {
        this.originalConsole.error(...args);
      }
      this.writeToFile('ERROR', message);
    };

    // Override console.info
    console.info = (...args: any[]) => {
      const message = args
        .map(arg =>
          typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
        )
        .join(' ');

      if (this.config.enableConsole) {
        this.originalConsole.info(...args);
      }
      this.writeToFile('INFO', message);
    };
  }

  /**
   * Write entry to log file
   */
  private async writeToFile(
    level: LogEntry['level'],
    message: string,
    context?: any
  ): Promise<void> {
    if (!this.config.enableFile) return;

    try {
      const timestamp = new Date().toISOString();
      const logEntry: LogEntry = {
        timestamp,
        level,
        message,
        context,
      };

      // Format for readability in log files
      const logLine = `[${timestamp}] ${level.padEnd(5)} ${message}${context ? '\n' + JSON.stringify(context, null, 2) : ''}\n`;

      await fs.appendFile(this.logFilePath, logLine);
    } catch (error) {
      // Fallback to original console if file writing fails
      this.originalConsole.error('Failed to write to log file:', error);
    }
  }

  /**
   * Manual structured logging with context
   */
  public async log(
    level: LogEntry['level'],
    message: string,
    context?: any
  ): Promise<void> {
    const fullMessage = context
      ? `${message} ${JSON.stringify(context)}`
      : message;

    if (this.config.enableConsole) {
      switch (level) {
        case 'ERROR':
          this.originalConsole.error(fullMessage);
          break;
        case 'WARN':
          this.originalConsole.warn(fullMessage);
          break;
        case 'INFO':
        case 'DEBUG':
        default:
          this.originalConsole.log(fullMessage);
          break;
      }
    }

    if (this.config.enableFile) {
      await this.writeToFile(level, message, context);
    }
  }

  /**
   * Write pipeline completion summary
   */
  public async finalize(summary?: any): Promise<void> {
    const endTime = new Date();
    const duration = endTime.getTime() - this.runStartTime.getTime();

    const footer = [
      `\n=== PIPELINE RUN COMPLETED ===`,
      `End Time: ${endTime.toISOString()}`,
      `Duration: ${Math.round(duration / 1000)}s (${duration}ms)`,
      `Run ID: ${this.config.runId}`,
    ];

    if (summary) {
      footer.push(`Summary: ${JSON.stringify(summary, null, 2)}`);
    }

    footer.push(`================================\n`);

    await this.writeToFile('INFO', footer.join('\n'));

    // Log completion to console as well
    if (this.config.enableConsole) {
      console.log(`\n📝 Log file saved: ${this.logFilePath}`);
      console.log(`⏱️  Run duration: ${Math.round(duration / 1000)}s`);
    }
  }

  /**
   * Restore original console methods
   */
  public restore(): void {
    console.log = this.originalConsole.log;
    console.warn = this.originalConsole.warn;
    console.error = this.originalConsole.error;
    console.info = this.originalConsole.info;
  }

  /**
   * Get log file path for this run
   */
  public getLogFilePath(): string {
    return this.logFilePath;
  }

  /**
   * Get run ID
   */
  public getRunId(): string {
    return this.config.runId!;
  }
}

/**
 * Factory function to create pipeline logger
 */
export function createPipelineLogger(
  pipelineName: string,
  options: Partial<PipelineLoggerConfig> = {}
): PipelineLogger {
  return new PipelineLogger({
    pipelineName,
    enableConsole: true,
    enableFile: true,
    ...options,
  });
}

/**
 * Convenience function for API routes
 */
export async function withPipelineLogging<T>(
  pipelineName: string,
  operation: (logger: PipelineLogger) => Promise<T>,
  options: Partial<PipelineLoggerConfig> = {}
): Promise<T> {
  const logger = createPipelineLogger(pipelineName, options);

  try {
    const result = await operation(logger);
    await logger.finalize(typeof result === 'object' ? result : undefined);
    return result;
  } catch (error) {
    await logger.log('ERROR', 'Pipeline operation failed', {
      error: error instanceof Error ? error.message : String(error),
    });
    await logger.finalize({
      error: error instanceof Error ? error.message : String(error),
    });
    throw error;
  } finally {
    logger.restore();
  }
}
