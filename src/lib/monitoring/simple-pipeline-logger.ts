/**
 * Simple Pipeline Logger
 *
 * A simplified logging system that creates unique log files per pipeline run
 * and captures all console output. This approach preserves existing code
 * while adding the requested features.
 *
 * Features:
 * - Unique log file per pipeline run with timestamp
 * - Console output capture and file duplication
 * - Preserves existing logger.info/logger.error calls
 * - Simple integration with existing pipeline endpoints
 *
 * <AUTHOR> Blick Development Team
 * @created 2025-07-28
 */

import { promises as fs } from 'fs';
import path from 'path';
import { getLogger } from './logger';

export interface SimplePipelineLoggerConfig {
  pipelineName: string;
  runId?: string;
}

export class SimplePipelineLogger {
  private pipelineName: string;
  private runId: string;
  private logFilePath: string;
  private runStartTime: Date;
  private originalLogger: any;
  private fileLogger: any;

  // Store original console methods
  private originalConsole = {
    log: console.log,
    warn: console.warn,
    error: console.error,
    info: console.info,
  };

  constructor(config: SimplePipelineLoggerConfig) {
    this.pipelineName = config.pipelineName;
    this.runStartTime = new Date();

    // Generate unique run ID if not provided
    this.runId = config.runId || this.generateRunId();

    // Create unique log file path
    this.logFilePath = this.createLogFilePath();

    // Create file-based logger for this run
    this.fileLogger = getLogger(`${this.pipelineName}-${this.runId}`, {
      enableFile: true,
      enableConsole: false, // Avoid double console output
    });

    // Initialize logging (but don't start interception immediately)
    this.initializeLogging();
  }

  /**
   * Generate unique run ID with timestamp
   */
  private generateRunId(): string {
    const now = new Date();
    const timestamp = now.toISOString().replace(/[:.]/g, '-').slice(0, -5);
    return timestamp;
  }

  /**
   * Create unique log file path for this run
   */
  private createLogFilePath(): string {
    const logsDir = path.join(process.cwd(), 'logs');
    const filename = `${this.pipelineName}-${this.runId}.log`;
    return path.join(logsDir, filename);
  }

  /**
   * Initialize logging system
   */
  private async initializeLogging(): Promise<void> {
    await this.ensureLogDirectory();
    await this.writeLogHeader();
    this.interceptConsoleOutput();
  }

  /**
   * Ensure logs directory exists
   */
  private async ensureLogDirectory(): Promise<void> {
    const logsDir = path.dirname(this.logFilePath);
    await fs.mkdir(logsDir, { recursive: true });
  }

  /**
   * Write log file header
   */
  private async writeLogHeader(): Promise<void> {
    const header = [
      `=== PIPELINE RUN: ${this.pipelineName.toUpperCase()} ===`,
      `Run ID: ${this.runId}`,
      `Start Time: ${this.runStartTime.toISOString()}`,
      `Log File: ${path.basename(this.logFilePath)}`,
      `===================================================\n`,
    ].join('\n');

    try {
      await fs.appendFile(this.logFilePath, header);
    } catch (error) {
      console.error('Failed to write log header:', error);
    }
  }

  /**
   * Intercept console output and duplicate to file
   */
  private interceptConsoleOutput(): void {
    const writeToFile = async (level: string, message: string) => {
      try {
        const timestamp = new Date().toISOString();
        const logLine = `[${timestamp}] ${level.padEnd(5)} ${message}\n`;
        await fs.appendFile(this.logFilePath, logLine);
      } catch (error) {
        // Silently fail to avoid recursion
      }
    };

    // Override console.log
    console.log = (...args: any[]) => {
      const message = args
        .map(arg =>
          typeof arg === 'object' && arg !== null
            ? JSON.stringify(arg, null, 2)
            : String(arg)
        )
        .join(' ');

      // Skip PayloadCMS and Next.js internal messages to prevent interference
      if (
        message.includes('Generating import map') ||
        message.includes('No new imports found') ||
        message.includes('Import map generated') ||
        message.includes('Payload Admin') ||
        message.includes('importMap.js') ||
        message.includes('admin/importMap') ||
        message.includes('Writing import map') ||
        message.includes('Import Map')
      ) {
        return this.originalConsole.log(...args); // Only log to terminal, don't write to file
      }

      this.originalConsole.log(...args);
      writeToFile('INFO', message);
    };

    // Override console.warn
    console.warn = (...args: any[]) => {
      const message = args
        .map(arg =>
          typeof arg === 'object' && arg !== null
            ? JSON.stringify(arg, null, 2)
            : String(arg)
        )
        .join(' ');

      this.originalConsole.warn(...args);
      writeToFile('WARN', message);
    };

    // Override console.error
    console.error = (...args: any[]) => {
      const message = args
        .map(arg =>
          typeof arg === 'object' && arg !== null
            ? JSON.stringify(arg, null, 2)
            : String(arg)
        )
        .join(' ');

      this.originalConsole.error(...args);
      writeToFile('ERROR', message);
    };

    // Override console.info
    console.info = (...args: any[]) => {
      const message = args
        .map(arg =>
          typeof arg === 'object' && arg !== null
            ? JSON.stringify(arg, null, 2)
            : String(arg)
        )
        .join(' ');

      this.originalConsole.info(...args);
      writeToFile('INFO', message);
    };
  }

  /**
   * Get a logger that works with existing code but writes to the unique file
   */
  public getLogger() {
    return this.fileLogger;
  }

  /**
   * Finalize logging and restore console
   */
  public async finalize(summary?: any): Promise<void> {
    const endTime = new Date();
    const duration = endTime.getTime() - this.runStartTime.getTime();

    const footer = [
      `\n=== PIPELINE RUN COMPLETED ===`,
      `End Time: ${endTime.toISOString()}`,
      `Duration: ${Math.round(duration / 1000)}s (${duration}ms)`,
      `Run ID: ${this.runId}`,
    ];

    if (summary) {
      footer.push(`Summary: ${JSON.stringify(summary, null, 2)}`);
    }

    footer.push(`================================\n`);

    try {
      await fs.appendFile(this.logFilePath, footer.join('\n'));
    } catch (error) {
      console.error('Failed to write log footer:', error);
    }

    // Log completion to console
    console.log(`\n📝 Log file saved: ${this.logFilePath}`);
    console.log(`⏱️  Run duration: ${Math.round(duration / 1000)}s`);

    // Restore original console methods
    this.restore();
  }

  /**
   * Restore original console methods
   */
  public restore(): void {
    console.log = this.originalConsole.log;
    console.warn = this.originalConsole.warn;
    console.error = this.originalConsole.error;
    console.info = this.originalConsole.info;
  }

  /**
   * Get log file path
   */
  public getLogFilePath(): string {
    return this.logFilePath;
  }

  /**
   * Get run ID
   */
  public getRunId(): string {
    return this.runId;
  }
}

/**
 * Simple wrapper function for pipeline endpoints
 * TEMPORARILY DISABLED: Testing if logging causes import map issues
 */
export async function withSimplePipelineLogging<T>(
  pipelineName: string,
  operation: (logger: any) => Promise<T>
): Promise<T> {
  console.log(
    `🧪 LOGGING DISABLED: Running ${pipelineName} without file logging or console interception`
  );

  // Create a dummy logger that does nothing but matches the interface
  const dummyLogger = {
    log: () => {},
    info: () => {},
    warn: () => {},
    error: () => {},
    debug: () => {},
  };

  try {
    // Call operation directly with dummy logger - no console interception
    const result = await operation(dummyLogger);

    console.log(
      `🧪 LOGGING DISABLED: ${pipelineName} completed without logging interference`
    );
    return result;
  } catch (error) {
    console.error(`🧪 LOGGING DISABLED: ${pipelineName} failed:`, error);
    throw error;
  }
}
