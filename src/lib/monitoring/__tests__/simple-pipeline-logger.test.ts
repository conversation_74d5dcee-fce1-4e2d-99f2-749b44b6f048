/**
 * Simple Pipeline Logger Test
 * Demonstrates the new unique log file generation and console capture functionality
 */

import {
  SimplePipelineLogger,
  withSimplePipelineLogging,
} from '../simple-pipeline-logger';
import { promises as fs } from 'fs';
import path from 'path';

describe('Simple Pipeline Logger', () => {
  const testLogsDir = path.join(process.cwd(), 'logs');

  test('should create unique log file with timestamp', async () => {
    const logger = new SimplePipelineLogger({ pipelineName: 'test-pipeline' });

    // Get the log file path
    const logFilePath = logger.getLogFilePath();
    const runId = logger.getRunId();

    // Verify file path format
    expect(logFilePath).toContain('test-pipeline-');
    expect(logFilePath).toMatch(
      /test-pipeline-\d{4}-\d{2}-\d{2}T\d{2}-\d{2}-\d{2}\.log$/
    );
    expect(runId).toMatch(/^\d{4}-\d{2}-\d{2}T\d{2}-\d{2}-\d{2}$/);

    // Clean up
    logger.restore();
  });

  test('should capture console output to file', async () => {
    const logger = new SimplePipelineLogger({
      pipelineName: 'console-test',
      runId: 'test-run-123',
    });

    const logFilePath = logger.getLogFilePath();

    // Wait a bit for initialization
    await new Promise(resolve => setTimeout(resolve, 100));

    // Test console output
    console.log('Test log message');
    console.warn('Test warning message');
    console.error('Test error message');

    // Wait for file writes
    await new Promise(resolve => setTimeout(resolve, 100));

    // Finalize and check file contents
    await logger.finalize({ testResult: 'success' });

    // Read log file
    const logContents = await fs.readFile(logFilePath, 'utf-8');

    expect(logContents).toContain('=== PIPELINE RUN: CONSOLE-TEST ===');
    expect(logContents).toContain('Run ID: test-run-123');
    expect(logContents).toContain('Test log message');
    expect(logContents).toContain('Test warning message');
    expect(logContents).toContain('Test error message');
    expect(logContents).toContain('=== PIPELINE RUN COMPLETED ===');
    expect(logContents).toContain('testResult');

    // Clean up test file
    try {
      await fs.unlink(logFilePath);
    } catch (error) {
      // Ignore cleanup errors
    }
  });

  test('should work with existing pipeline code pattern', async () => {
    const testResult = await withSimplePipelineLogging(
      'integration-test',
      async (logger: any) => {
        console.log('Starting integration test');

        // Simulate existing logger usage
        logger.info('Test info message', { step: 'validation' });
        logger.error('Test error', new Error('Test error'), {
          context: 'testing',
        });

        console.log('Integration test completed');

        return { success: true, processed: 5, errors: 0 };
      }
    );

    expect(testResult).toEqual({ success: true, processed: 5, errors: 0 });
  });
});

// Mock console to avoid test output interference
const originalConsole = { ...console };
beforeEach(() => {
  console.log = jest.fn();
  console.warn = jest.fn();
  console.error = jest.fn();
  console.info = jest.fn();
});

afterEach(() => {
  Object.assign(console, originalConsole);
});
