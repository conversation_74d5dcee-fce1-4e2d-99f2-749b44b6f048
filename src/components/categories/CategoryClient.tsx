'use client';

import { usePageTicker } from '@/components/ticker-tape/TickerTapeContext';
import { getSymbolSetForCategory } from '@/components/ticker-tape/TickerTape.config';

interface CategoryClientProps {
  categorySlug: string;
}

/**
 * Client component that sets up category-specific ticker via context
 * This allows the ticker to be rendered above the navigation in the layout
 */
export function CategoryClient({ categorySlug }: CategoryClientProps) {
  // Get the appropriate symbol set for this category
  const symbols = getSymbolSetForCategory(categorySlug);

  // Set the ticker symbols for this category page
  usePageTicker(symbols);

  // This component doesn't render anything - it just sets up the ticker
  return null;
}
