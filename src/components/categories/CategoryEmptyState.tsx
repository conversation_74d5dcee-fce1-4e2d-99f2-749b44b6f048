import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { FileText, Home, RefreshCw } from 'lucide-react';
import Link from 'next/link';
import { getCachedAllCategories } from '@/lib/cache/categories';
import type { Category } from '@/payload-types';
import { ReloadButton } from './ReloadButton';

interface CategoryEmptyStateProps {
  category: Category;
}

export default async function CategoryEmptyState({
  category,
}: CategoryEmptyStateProps) {
  // Fetch available categories on the server
  const allCategories = await getCachedAllCategories();
  // Filter out the current category and limit to 4 suggestions
  const availableCategories = allCategories
    .filter(cat => cat.id !== category.id)
    .slice(0, 4);

  return (
    <div className="max-w-[1440px] mx-auto px-4 py-12 lg:py-16">
      <div className="flex justify-center">
        <Card className="max-w-md w-full">
          <CardHeader className="text-center">
            <div className="flex justify-center mb-4">
              <FileText className="h-12 w-12 text-muted-foreground" />
            </div>
            <CardTitle className="text-xl font-serif font-normal text-foreground">
              Keine {category.title}-Artikel verfügbar
            </CardTitle>
            <CardDescription className="text-muted-foreground">
              In dieser Kategorie sind derzeit keine Artikel vorhanden.
            </CardDescription>
          </CardHeader>

          <CardContent className="flex flex-col gap-4">
            <div className="text-center text-sm text-muted-foreground">
              <p>Mögliche Gründe:</p>
              <ul className="mt-2 text-xs space-y-1">
                <li>• Neue Kategorie ohne Inhalte</li>
                <li>• Artikel werden noch verarbeitet</li>
                <li>• Temporäre Inhaltspause</li>
              </ul>
            </div>

            {/* Action buttons */}
            <div className="flex flex-col sm:flex-row gap-2">
              <Button
                asChild
                className="flex-1 flex items-center gap-2"
                variant="default"
              >
                <Link href="/">
                  <Home className="h-4 w-4" />
                  Zur Startseite
                </Link>
              </Button>

              <ReloadButton />
            </div>

            {/* Dynamic category suggestions */}
            {availableCategories.length > 0 && (
              <div className="text-center text-sm text-muted-foreground border-t pt-4">
                <p className="mb-2">Andere Kategorien entdecken:</p>
                <div className="flex flex-wrap gap-2 justify-center">
                  {availableCategories.map(cat => (
                    <Link
                      key={cat.id}
                      href={`/kategorien/${cat.slug}`}
                      className="text-xs px-2 py-1 bg-gray-100 dark:bg-gray-800 rounded hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
                    >
                      {cat.title}
                    </Link>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
