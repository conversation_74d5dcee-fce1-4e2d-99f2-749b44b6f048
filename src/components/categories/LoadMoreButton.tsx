'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Loader2, Plus } from 'lucide-react';

interface LoadMoreButtonProps {
  tier: 'tier-1' | 'tier-2' | 'tier-3';
  categorySlug: string;
  currentPage: number;
  hasNextPage: boolean;
  totalArticles: number;
  loadedArticles: number;
  onLoadMore: (articles: any[], newPage: number) => void;
  className?: string;
}

export default function LoadMoreButton({
  tier,
  categorySlug,
  currentPage,
  hasNextPage,
  totalArticles,
  loadedArticles,
  onLoadMore,
  className = '',
}: LoadMoreButtonProps) {
  const [isLoading, setIsLoading] = useState(false);

  const tierLabels = {
    'tier-1': 'Tier 1',
    'tier-2': 'Tier 2',
    'tier-3': 'Tier 3',
  };

  const tierColors = {
    'tier-1': 'border-blue-500 text-blue-600 hover:bg-blue-50',
    'tier-2': 'border-amber-500 text-amber-600 hover:bg-amber-50',
    'tier-3': 'border-rose-500 text-rose-600 hover:bg-rose-50',
  };

  const handleLoadMore = async () => {
    if (isLoading || !hasNextPage) return;

    setIsLoading(true);
    try {
      const nextPage = currentPage + 1;
      const response = await fetch(
        `/api/kategorien/${categorySlug}/articles?page=${nextPage}&tier=${tier}&loadMore=true&limit=6`
      );

      if (!response.ok) {
        throw new Error('Failed to load more articles');
      }

      const data = await response.json();
      onLoadMore(data.articles, nextPage);
    } catch (error) {
      console.error('Error loading more articles:', error);
    } finally {
      setIsLoading(false);
    }
  };

  if (!hasNextPage) {
    return (
      <div className={`text-center py-4 ${className}`}>
        <p className="text-sm text-muted-foreground">
          Alle {tierLabels[tier]}-Artikel angezeigt ({loadedArticles} von{' '}
          {totalArticles})
        </p>
      </div>
    );
  }

  return (
    <div className={`text-center py-4 ${className}`}>
      <Button
        variant="outline"
        onClick={handleLoadMore}
        disabled={isLoading}
        className={`${tierColors[tier]} transition-colors duration-200`}
      >
        {isLoading ? (
          <>
            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
            Lade weitere Artikel...
          </>
        ) : (
          <>
            <Plus className="w-4 h-4 mr-2" />
            Mehr {tierLabels[tier]}-Artikel laden
          </>
        )}
      </Button>
      <p className="text-xs text-muted-foreground mt-2">
        {loadedArticles} von {totalArticles} Artikeln angezeigt
      </p>
    </div>
  );
}
