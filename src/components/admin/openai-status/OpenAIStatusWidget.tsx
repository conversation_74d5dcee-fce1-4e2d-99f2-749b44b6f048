'use client';

import type React from 'react';
import { useState, useEffect } from 'react';

interface OpenAIStatus {
  hasRecentQuotaErrors: boolean;
  recentQuotaErrorCount: number;
  lastError: any;
  totalErrorsLogged: number;
  checkTime: string;
  recommendations: string[];
}

interface StatusResponse {
  success: boolean;
  status: OpenAIStatus;
  timestamp: string;
}

export const OpenAIStatusWidget: React.FC = () => {
  const [status, setStatus] = useState<OpenAIStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchStatus = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/openai-status');
      const data: StatusResponse = await response.json();

      if (data.success) {
        setStatus(data.status);
        setError(null);
      } else {
        setError('Failed to fetch status');
      }
    } catch (err) {
      setError('Network error');
      console.error('Failed to fetch OpenAI status:', err);
    } finally {
      setLoading(false);
    }
  };

  const clearErrorLog = async () => {
    try {
      const response = await fetch('/api/openai-status', { method: 'DELETE' });
      const data = await response.json();

      if (data.success) {
        // Refresh status after clearing
        await fetchStatus();
      }
    } catch (err) {
      console.error('Failed to clear error log:', err);
    }
  };

  useEffect(() => {
    fetchStatus();

    // Auto-refresh disabled to prevent excessive polling
    // Users can manually refresh using the Refresh button
    // const interval = setInterval(fetchStatus, 5 * 60 * 1000);
    // return () => clearInterval(interval);
  }, []);

  if (loading) {
    return (
      <div className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          OpenAI API Status
        </h3>
        <div className="text-gray-500">Loading...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white border border-red-200 rounded-lg p-4 shadow-sm">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          OpenAI API Status
        </h3>
        <div className="text-red-600">Error: {error}</div>
        <button
          onClick={fetchStatus}
          className="mt-2 px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-600"
        >
          Retry
        </button>
      </div>
    );
  }

  if (!status) return null;

  const statusColor = status.hasRecentQuotaErrors ? 'red' : 'green';
  const statusIcon = status.hasRecentQuotaErrors ? '🚨' : '✅';
  const statusText = status.hasRecentQuotaErrors
    ? 'Quota Issues Detected'
    : 'API Functioning Normally';

  return (
    <div
      className={`bg-white border rounded-lg p-4 shadow-sm ${
        status.hasRecentQuotaErrors ? 'border-red-200' : 'border-green-200'
      }`}
    >
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-lg font-semibold text-gray-900">
          OpenAI API Status
        </h3>
        <button
          onClick={fetchStatus}
          className="px-2 py-1 bg-gray-100 text-gray-600 rounded text-xs hover:bg-gray-200"
        >
          Refresh
        </button>
      </div>

      <div className={`flex items-center mb-3 text-${statusColor}-600`}>
        <span className="text-lg mr-2">{statusIcon}</span>
        <span className="font-medium">{statusText}</span>
      </div>

      <div className="grid grid-cols-2 gap-4 text-sm mb-3">
        <div>
          <span className="text-gray-500">Total Errors:</span>
          <span className="ml-2 font-medium">{status.totalErrorsLogged}</span>
        </div>
        <div>
          <span className="text-gray-500">Recent (1h):</span>
          <span
            className={`ml-2 font-medium ${
              status.recentQuotaErrorCount > 0
                ? 'text-red-600'
                : 'text-green-600'
            }`}
          >
            {status.recentQuotaErrorCount}
          </span>
        </div>
      </div>

      {status.lastError && (
        <div className="bg-gray-50 rounded p-3 mb-3">
          <div className="text-sm font-medium text-gray-700 mb-1">
            Last Error:
          </div>
          <div className="text-xs text-gray-600">
            <div>
              Time: {new Date(status.lastError.timestamp).toLocaleString()}
            </div>
            <div>Function: {status.lastError.function}</div>
            <div>Status: {status.lastError.error.status}</div>
            <div>Type: {status.lastError.error.type}</div>
          </div>
        </div>
      )}

      <div className="text-xs text-gray-500 mb-3">
        Last checked: {new Date(status.checkTime).toLocaleString()}
      </div>

      {status.hasRecentQuotaErrors && (
        <div className="bg-red-50 border border-red-200 rounded p-3 mb-3">
          <div className="text-sm font-medium text-red-800 mb-2">
            Action Required:
          </div>
          <ul className="text-xs text-red-700 space-y-1">
            <li>
              • Check billing at{' '}
              <a
                href="https://platform.openai.com/usage"
                target="_blank"
                className="underline"
                rel="noopener"
              >
                OpenAI Dashboard
              </a>
            </li>
            <li>• Consider upgrading your plan or adding credits</li>
            <li>• RSS processing may create articles without translations</li>
          </ul>
        </div>
      )}

      {status.totalErrorsLogged > 0 && (
        <button
          onClick={clearErrorLog}
          className="w-full px-3 py-2 bg-gray-100 text-gray-700 rounded text-sm hover:bg-gray-200"
        >
          Clear Error Log
        </button>
      )}
    </div>
  );
};
