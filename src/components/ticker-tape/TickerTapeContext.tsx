'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';
import type { TickerSymbol } from './TickerTape.types';

interface TickerTapeContextType {
  symbols: TickerSymbol[] | null;
  setSymbols: (symbols: TickerSymbol[] | null) => void;
  isVisible: boolean;
  setIsVisible: (visible: boolean) => void;
}

const TickerTapeContext = createContext<TickerTapeContextType | undefined>(
  undefined
);

export function TickerTapeProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const [symbols, setSymbols] = useState<TickerSymbol[] | null>(null);
  const [isVisible, setIsVisible] = useState(false);

  return (
    <TickerTapeContext.Provider
      value={{ symbols, setSymbols, isVisible, setIsVisible }}
    >
      {children}
    </TickerTapeContext.Provider>
  );
}

export function useTickerTapeContext() {
  const context = useContext(TickerTapeContext);
  if (context === undefined) {
    throw new Error(
      'useTickerTapeContext must be used within a TickerTapeProvider'
    );
  }
  return context;
}

/**
 * Hook for pages to set their ticker symbols
 * This will automatically show the ticker above the navigation
 */
export function usePageTicker(symbols: TickerSymbol[]) {
  const { setSymbols, setIsVisible } = useTickerTapeContext();

  useEffect(() => {
    setSymbols(symbols);
    setIsVisible(true);

    return () => {
      setSymbols(null);
      setIsVisible(false);
    };
  }, [symbols, setSymbols, setIsVisible]);
}
