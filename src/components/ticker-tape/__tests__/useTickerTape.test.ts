import { renderHook } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { useTheme } from 'next-themes';
import { useTickerTapeTheme } from '../useTickerTape';

// Mock next-themes
vi.mock('next-themes', () => ({
  useTheme: vi.fn(),
}));

const mockUseTheme = vi.mocked(useTheme);

describe('useTickerTapeTheme', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should return light theme when theme is light', () => {
    mockUseTheme.mockReturnValue({
      theme: 'light',
      systemTheme: 'light',
      setTheme: vi.fn(),
      resolvedTheme: 'light',
      themes: ['light', 'dark', 'system'],
    });

    const { result } = renderHook(() => useTickerTapeTheme());
    expect(result.current).toBe('light');
  });

  it('should return dark theme when theme is dark', () => {
    mockUseTheme.mockReturnValue({
      theme: 'dark',
      systemTheme: 'dark',
      setTheme: vi.fn(),
      resolvedTheme: 'dark',
      themes: ['light', 'dark', 'system'],
    });

    const { result } = renderHook(() => useTickerTapeTheme());
    expect(result.current).toBe('dark');
  });

  it('should use systemTheme when theme is system', () => {
    mockUseTheme.mockReturnValue({
      theme: 'system',
      systemTheme: 'dark',
      setTheme: vi.fn(),
      resolvedTheme: 'dark',
      themes: ['light', 'dark', 'system'],
    });

    const { result } = renderHook(() => useTickerTapeTheme());
    expect(result.current).toBe('dark');
  });

  it('should use systemTheme light when theme is system and systemTheme is light', () => {
    mockUseTheme.mockReturnValue({
      theme: 'system',
      systemTheme: 'light',
      setTheme: vi.fn(),
      resolvedTheme: 'light',
      themes: ['light', 'dark', 'system'],
    });

    const { result } = renderHook(() => useTickerTapeTheme());
    expect(result.current).toBe('light');
  });

  it('should default to light for unknown themes', () => {
    mockUseTheme.mockReturnValue({
      theme: 'unknown' as any,
      systemTheme: 'unknown' as any,
      setTheme: vi.fn(),
      resolvedTheme: 'unknown' as any,
      themes: ['light', 'dark', 'system'],
    });

    const { result } = renderHook(() => useTickerTapeTheme());
    expect(result.current).toBe('light');
  });

  it('should memoize the result', () => {
    mockUseTheme.mockReturnValue({
      theme: 'light',
      systemTheme: 'light',
      setTheme: vi.fn(),
      resolvedTheme: 'light',
      themes: ['light', 'dark', 'system'],
    });

    const { result, rerender } = renderHook(() => useTickerTapeTheme());
    const firstResult = result.current;

    rerender();
    const secondResult = result.current;

    expect(firstResult).toBe(secondResult);
  });
});
