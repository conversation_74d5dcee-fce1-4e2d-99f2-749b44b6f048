import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import { TickerTape } from '../TickerTape';
import { SYMBOL_SETS } from '../TickerTape.config';

// Mock next-themes
vi.mock('next-themes', () => ({
  useTheme: () => ({
    theme: 'light',
    systemTheme: 'light',
  }),
}));

describe('TickerTape Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders without crashing with valid symbols', () => {
    render(<TickerTape symbols={SYMBOL_SETS.investment.symbols} />);

    // Check for the main container
    const container = screen.getByRole('region', {
      name: /stock ticker tape/i,
    });
    expect(container).toBeInTheDocument();
  });

  it('renders error message for empty symbols array', () => {
    render(<TickerTape symbols={[]} />);

    expect(
      screen.getByText(/error: invalid ticker symbols provided/i)
    ).toBeInTheDocument();
    expect(
      screen.getByText(
        /please check that all symbols are in the correct format/i
      )
    ).toBeInTheDocument();
  });

  it('renders error message for invalid symbols', () => {
    const invalidSymbols = [
      { proName: '', title: '' }, // Empty proName
      { proName: 'INVALID', title: '' }, // No colon separator
    ];

    render(<TickerTape symbols={invalidSymbols} />);

    expect(
      screen.getByText(/error: invalid ticker symbols provided/i)
    ).toBeInTheDocument();
  });

  it('applies custom className', () => {
    const customClass = 'my-custom-ticker';
    render(
      <TickerTape
        symbols={SYMBOL_SETS.homepage.symbols}
        className={customClass}
      />
    );

    const wrapper = screen.getByRole('region').parentElement;
    expect(wrapper).toHaveClass(customClass);
  });

  it('renders with homepage symbol set', () => {
    render(<TickerTape symbols={SYMBOL_SETS.homepage.symbols} />);

    const container = screen.getByRole('region', {
      name: /stock ticker tape/i,
    });
    expect(container).toBeInTheDocument();
    expect(container).toHaveClass('tradingview-widget-container');
  });

  it('renders with investment symbol set', () => {
    render(<TickerTape symbols={SYMBOL_SETS.investment.symbols} />);

    const container = screen.getByRole('region', {
      name: /stock ticker tape/i,
    });
    expect(container).toBeInTheDocument();
  });

  it('renders with technology symbol set', () => {
    render(<TickerTape symbols={SYMBOL_SETS.technology.symbols} />);

    const container = screen.getByRole('region', {
      name: /stock ticker tape/i,
    });
    expect(container).toBeInTheDocument();
  });

  it('has proper accessibility attributes', () => {
    render(<TickerTape symbols={SYMBOL_SETS.homepage.symbols} />);

    const container = screen.getByRole('region', {
      name: /stock ticker tape/i,
    });

    expect(container).toHaveAttribute('aria-label', 'Stock ticker tape');
    expect(container).toHaveAttribute('aria-live', 'polite');
  });

  it('accepts all supported props', () => {
    render(
      <TickerTape
        symbols={SYMBOL_SETS.homepage.symbols}
        displayMode="compact"
        showSymbolLogo={false}
        isTransparent={true}
        largeChartUrl="https://example.com"
        locale="de"
        className="test-class"
      />
    );

    const container = screen.getByRole('region', {
      name: /stock ticker tape/i,
    });
    expect(container).toBeInTheDocument();
  });
});
