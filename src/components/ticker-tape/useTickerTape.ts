'use client';

import { useEffect, useRef, useState, useMemo } from 'react';
import { useTheme } from 'next-themes';
import type { TickerTapeConfig, UseTickerTapeReturn } from './TickerTape.types';

/**
 * Hook for theme-aware ticker tape configuration
 * Returns null until mounted to prevent hydration mismatches
 */
export function useTickerTapeTheme(): 'light' | 'dark' | null {
  const { theme, systemTheme, resolvedTheme } = useTheme();
  const [mounted, setMounted] = useState(false);

  // Handle hydration
  useEffect(() => {
    setMounted(true);
  }, []);

  return useMemo(() => {
    // Return null until mounted to prevent loading with wrong theme
    if (!mounted) {
      return null;
    }

    // Use resolvedTheme first as it's the most reliable
    if (resolvedTheme) {
      return resolvedTheme === 'dark' ? 'dark' : 'light';
    }

    // Fallback to manual resolution
    const effectiveTheme = theme === 'system' ? systemTheme : theme;
    return effectiveTheme === 'dark' ? 'dark' : 'light';
  }, [mounted, theme, systemTheme, resolvedTheme]);
}

/**
 * Hook that uses the exact pattern TradingView expects for embedding
 */
export function useTickerTape(config: TickerTapeConfig): UseTickerTapeReturn {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!containerRef.current) return;

    // Don't load widget until we have a proper theme
    if (!config.colorTheme) {
      console.log('Waiting for theme to be resolved...');
      return;
    }

    const container = containerRef.current;
    let timeoutId: NodeJS.Timeout | null = null;

    const loadWidget = () => {
      try {
        setIsLoading(true);
        setError(null);

        // Small delay for dark theme to ensure TradingView processes it correctly
        const delay = config.colorTheme === 'dark' ? 100 : 0;

        setTimeout(() => {
          loadWidgetInternal();
        }, delay);
      } catch (err) {
        console.error('Widget initialization error:', err);
        setError(err instanceof Error ? err.message : 'Unknown error');
        setIsLoading(false);
      }
    };

    const loadWidgetInternal = () => {
      try {
        // Complete DOM cleanup: TradingView widgets inject their own DOM and styles
        // We need to fully reset the container to prevent theme artifacts
        const widgetElement = container.querySelector(
          '.tradingview-widget-container__widget'
        );

        // Clear any TradingView injected content
        if (widgetElement) {
          widgetElement.innerHTML = '';
        }

        // Remove any existing scripts
        const existingScripts = container.querySelectorAll('script');
        existingScripts.forEach(script => script.remove());

        // Force cleanup of any TradingView global objects that might cache themes
        if (typeof window !== 'undefined') {
          // Clear any TradingView cached widgets
          const widgets = container.querySelectorAll('[id^="tradingview_"]');
          widgets.forEach(widget => widget.remove());

          // Remove any TradingView injected styles that might affect theming
          const tvStyles = document.querySelectorAll('style[data-tradingview]');
          tvStyles.forEach(style => {
            if (style.textContent?.includes('tradingview-widget-container')) {
              style.remove();
            }
          });
        }

        // Create and configure the script element with cache-busting
        const scriptElement = document.createElement('script');
        scriptElement.type = 'text/javascript';
        const cacheBuster = `${Date.now()}-${config.colorTheme}`;
        scriptElement.src = `https://s3.tradingview.com/external-embedding/embed-widget-ticker-tape.js?v=${cacheBuster}`;
        scriptElement.async = true;

        // Set the configuration as the script's content
        const configString = JSON.stringify({
          symbols: config.symbols,
          colorTheme: config.colorTheme,
          locale: config.locale,
          largeChartUrl: config.largeChartUrl,
          isTransparent: config.isTransparent,
          showSymbolLogo: config.showSymbolLogo,
          displayMode: config.displayMode,
        });

        // Log ticker loading for debugging (can be removed in production)
        console.log('TradingView ticker loading:', {
          theme: config.colorTheme,
          transparent: config.isTransparent,
          symbols: config.symbols.length,
        });

        scriptElement.innerHTML = configString;

        // Set timeout
        timeoutId = setTimeout(() => {
          setError('Widget failed to load');
          setIsLoading(false);
        }, 15000);

        // Handle load events
        scriptElement.onload = () => {
          console.log('TradingView script loaded successfully');
          if (timeoutId) {
            clearTimeout(timeoutId);
            timeoutId = null;
          }

          // Give the widget time to render
          setTimeout(() => {
            container.classList.add('loaded');

            // Force transparency via CSS override for both light and dark modes
            // TradingView widget has transparency issues in both themes
            if (config.isTransparent) {
              // Apply transparency to all possible TradingView elements
              const selectors = [
                '[class*="tradingview-widget"]',
                '.tradingview-widget-container',
                '.tradingview-widget-container__widget',
                '[id*="tradingview"]',
                'div[style*="background"]',
                'iframe',
                'div',
                'span',
                'table',
                'tr',
                'td',
              ];

              selectors.forEach(selector => {
                const elements = container.querySelectorAll(selector);
                elements.forEach(element => {
                  const htmlElement = element as HTMLElement;
                  htmlElement.style.setProperty(
                    'background-color',
                    'transparent',
                    'important'
                  );
                  htmlElement.style.setProperty(
                    'background',
                    'transparent',
                    'important'
                  );
                  htmlElement.style.setProperty(
                    'background-image',
                    'none',
                    'important'
                  );
                });
              });

              // Inject ultra-aggressive CSS to force transparency globally
              const styleId = `ticker-transparency-ultra`;
              let existingStyle = document.getElementById(styleId);
              if (!existingStyle) {
                const style = document.createElement('style');
                style.id = styleId;
                style.textContent = `
                  /* Ultra-aggressive transparency override for TradingView */
                  .tradingview-widget-container,
                  .tradingview-widget-container *,
                  .tradingview-widget-container__widget,
                  .tradingview-widget-container__widget *,
                  [class*="tradingview-widget"],
                  [class*="tradingview-widget"] *,
                  [id*="tradingview"],
                  [id*="tradingview"] *,
                  iframe[src*="tradingview"],
                  div[style*="background-color"],
                  div[style*="background"] {
                    background: transparent !important;
                    background-color: transparent !important;
                    background-image: none !important;
                  }

                  /* Specific dark mode overrides */
                  .dark .tradingview-widget-container,
                  .dark .tradingview-widget-container *,
                  .dark [class*="tradingview-widget"],
                  .dark [class*="tradingview-widget"] *,
                  html.dark .tradingview-widget-container,
                  html.dark .tradingview-widget-container *,
                  html.dark [class*="tradingview-widget"],
                  html.dark [class*="tradingview-widget"] * {
                    background: transparent !important;
                    background-color: transparent !important;
                    background-image: none !important;
                  }
                `;
                document.head.appendChild(style);
              }

              // Also try to override any iframe content if accessible
              const iframes = container.querySelectorAll('iframe');
              iframes.forEach(iframe => {
                try {
                  if (iframe.contentDocument) {
                    const body = iframe.contentDocument.body;
                    if (body) {
                      body.style.setProperty(
                        'background-color',
                        'transparent',
                        'important'
                      );
                      body.style.setProperty(
                        'background',
                        'transparent',
                        'important'
                      );
                    }
                  }
                } catch (e) {
                  // Cross-origin iframe, can't access content
                  console.log(
                    'Cannot access iframe content for transparency override'
                  );
                }
              });

              // Set up MutationObserver to continuously force transparency
              // This catches any dynamically added elements
              const observer = new MutationObserver(mutations => {
                mutations.forEach(mutation => {
                  mutation.addedNodes.forEach(node => {
                    if (node.nodeType === Node.ELEMENT_NODE) {
                      const element = node as HTMLElement;
                      // Force transparency on any new element
                      element.style.setProperty(
                        'background-color',
                        'transparent',
                        'important'
                      );
                      element.style.setProperty(
                        'background',
                        'transparent',
                        'important'
                      );
                      element.style.setProperty(
                        'background-image',
                        'none',
                        'important'
                      );

                      // Also check all children
                      const children = element.querySelectorAll('*');
                      children.forEach(child => {
                        const childElement = child as HTMLElement;
                        childElement.style.setProperty(
                          'background-color',
                          'transparent',
                          'important'
                        );
                        childElement.style.setProperty(
                          'background',
                          'transparent',
                          'important'
                        );
                        childElement.style.setProperty(
                          'background-image',
                          'none',
                          'important'
                        );
                      });
                    }
                  });
                });
              });

              // Start observing
              observer.observe(container, {
                childList: true,
                subtree: true,
                attributes: true,
                attributeFilter: ['style', 'class'],
              });

              // Store observer for cleanup
              (container as any)._transparencyObserver = observer;
            }

            setIsLoading(false);
          }, 1000);
        };

        scriptElement.onerror = () => {
          if (timeoutId) {
            clearTimeout(timeoutId);
            timeoutId = null;
          }
          setError('Failed to load TradingView script');
          setIsLoading(false);
        };

        // Important: Add the script to the same container as the widget
        container.appendChild(scriptElement);
      } catch (err) {
        console.error('Widget initialization error:', err);
        setError(err instanceof Error ? err.message : 'Unknown error');
        setIsLoading(false);
      }
    };

    // Load the widget
    loadWidget();

    return () => {
      if (timeoutId) clearTimeout(timeoutId);
      if (container) {
        // Only clear TradingView injected content, preserve React structure
        const widgetElement = container.querySelector(
          '.tradingview-widget-container__widget'
        );
        if (widgetElement) {
          widgetElement.innerHTML = '';
        }
        // Remove any scripts
        const scripts = container.querySelectorAll('script');
        scripts.forEach(script => script.remove());
      }
    };
  }, [
    // Recreate when any of these change
    JSON.stringify(config),
  ]);

  return {
    containerRef,
    isLoading,
    error,
  };
}
