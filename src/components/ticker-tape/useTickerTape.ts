'use client';

import { useEffect, useRef, useState, useMemo } from 'react';
import { useTheme } from 'next-themes';
import type { TickerTapeConfig, UseTickerTapeReturn } from './TickerTape.types';

/**
 * Hook for theme-aware ticker tape configuration
 * Returns null until mounted to prevent hydration mismatches
 */
export function useTickerTapeTheme(): 'light' | 'dark' | null {
  const { theme, systemTheme, resolvedTheme } = useTheme();
  const [mounted, setMounted] = useState(false);

  // Handle hydration
  useEffect(() => {
    setMounted(true);
  }, []);

  return useMemo(() => {
    // Return null until mounted to prevent loading with wrong theme
    if (!mounted) {
      return null;
    }

    // Use resolvedTheme first as it's the most reliable
    if (resolvedTheme) {
      return resolvedTheme === 'dark' ? 'dark' : 'light';
    }

    // Fallback to manual resolution
    const effectiveTheme = theme === 'system' ? systemTheme : theme;
    return effectiveTheme === 'dark' ? 'dark' : 'light';
  }, [mounted, theme, systemTheme, resolvedTheme]);
}

/**
 * Hook that uses the exact pattern TradingView expects for embedding
 */
export function useTickerTape(config: TickerTapeConfig): UseTickerTapeReturn {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!containerRef.current) return;

    // Don't load widget until we have a proper theme
    if (!config.colorTheme) {
      console.log('Waiting for theme to be resolved...');
      return;
    }

    const container = containerRef.current;
    let timeoutId: NodeJS.Timeout | null = null;

    const loadWidget = () => {
      try {
        setIsLoading(true);
        setError(null);

        // Small delay for dark theme to ensure TradingView processes it correctly
        const delay = config.colorTheme === 'dark' ? 100 : 0;

        setTimeout(() => {
          loadWidgetInternal();
        }, delay);
      } catch (err) {
        console.error('Widget initialization error:', err);
        setError(err instanceof Error ? err.message : 'Unknown error');
        setIsLoading(false);
      }
    };

    const loadWidgetInternal = () => {
      try {
        // Complete DOM cleanup: TradingView widgets inject their own DOM and styles
        // We need to fully reset the container to prevent theme artifacts
        const widgetElement = container.querySelector(
          '.tradingview-widget-container__widget'
        );

        // Clear any TradingView injected content
        if (widgetElement) {
          widgetElement.innerHTML = '';
        }

        // Remove any existing scripts
        const existingScripts = container.querySelectorAll('script');
        existingScripts.forEach(script => script.remove());

        // Force cleanup of any TradingView global objects that might cache themes
        if (typeof window !== 'undefined') {
          // Clear any TradingView cached widgets
          const widgets = container.querySelectorAll('[id^="tradingview_"]');
          widgets.forEach(widget => widget.remove());

          // Remove any TradingView injected styles that might affect theming
          const tvStyles = document.querySelectorAll('style[data-tradingview]');
          tvStyles.forEach(style => {
            if (style.textContent?.includes('tradingview-widget-container')) {
              style.remove();
            }
          });
        }

        // Create and configure the script element with cache-busting
        const scriptElement = document.createElement('script');
        scriptElement.type = 'text/javascript';
        const cacheBuster = `${Date.now()}-${config.colorTheme}`;
        scriptElement.src = `https://s3.tradingview.com/external-embedding/embed-widget-ticker-tape.js?v=${cacheBuster}`;
        scriptElement.async = true;

        // Set the configuration as the script's content
        const configString = JSON.stringify({
          symbols: config.symbols,
          colorTheme: config.colorTheme,
          locale: config.locale,
          largeChartUrl: config.largeChartUrl,
          isTransparent: config.isTransparent,
          showSymbolLogo: config.showSymbolLogo,
          displayMode: config.displayMode,
        });

        // Log ticker loading for debugging (can be removed in production)
        console.log('TradingView ticker loading:', {
          theme: config.colorTheme,
          transparent: config.isTransparent,
          symbols: config.symbols.length,
        });

        scriptElement.innerHTML = configString;

        // Set timeout
        timeoutId = setTimeout(() => {
          setError('Widget failed to load');
          setIsLoading(false);
        }, 15000);

        // Handle load events
        scriptElement.onload = () => {
          console.log('TradingView script loaded successfully');
          if (timeoutId) {
            clearTimeout(timeoutId);
            timeoutId = null;
          }

          // Give the widget time to render
          setTimeout(() => {
            container.classList.add('loaded');

            // Force transparency in dark mode via CSS override
            if (config.colorTheme === 'dark' && config.isTransparent) {
              const tickerElements = container.querySelectorAll(
                '[class*="tradingview-widget"]'
              );
              tickerElements.forEach(element => {
                const htmlElement = element as HTMLElement;
                htmlElement.style.backgroundColor = 'transparent !important';
              });

              // Also try to override any iframe content if accessible
              const iframes = container.querySelectorAll('iframe');
              iframes.forEach(iframe => {
                try {
                  if (iframe.contentDocument) {
                    const body = iframe.contentDocument.body;
                    if (body) {
                      body.style.backgroundColor = 'transparent !important';
                    }
                  }
                } catch (e) {
                  // Cross-origin iframe, can't access content
                  console.log(
                    'Cannot access iframe content for transparency override'
                  );
                }
              });
            }

            setIsLoading(false);
          }, 1000);
        };

        scriptElement.onerror = () => {
          if (timeoutId) {
            clearTimeout(timeoutId);
            timeoutId = null;
          }
          setError('Failed to load TradingView script');
          setIsLoading(false);
        };

        // Important: Add the script to the same container as the widget
        container.appendChild(scriptElement);
      } catch (err) {
        console.error('Widget initialization error:', err);
        setError(err instanceof Error ? err.message : 'Unknown error');
        setIsLoading(false);
      }
    };

    // Load the widget
    loadWidget();

    return () => {
      if (timeoutId) clearTimeout(timeoutId);
      if (container) {
        // Only clear TradingView injected content, preserve React structure
        const widgetElement = container.querySelector(
          '.tradingview-widget-container__widget'
        );
        if (widgetElement) {
          widgetElement.innerHTML = '';
        }
        // Remove any scripts
        const scripts = container.querySelectorAll('script');
        scripts.forEach(script => script.remove());
      }
    };
  }, [
    // Recreate when any of these change
    JSON.stringify(config),
  ]);

  return {
    containerRef,
    isLoading,
    error,
  };
}
