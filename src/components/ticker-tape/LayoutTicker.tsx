'use client';

import { TickerTape } from './TickerTape';
import { useTickerTapeContext } from './TickerTapeContext';

/**
 * Layout ticker component that renders the ticker above navigation
 * when a page provides symbols via the TickerTapeContext
 */
export function LayoutTicker() {
  const { symbols, isVisible } = useTickerTapeContext();

  if (!isVisible || !symbols) {
    return null;
  }

  // Generate a key based on the first few symbols to force remount when symbols change
  const symbolsKey = symbols
    .slice(0, 3)
    .map(s => s.proName)
    .join('-');

  return (
    <div className="w-full">
      <TickerTape key={symbolsKey} symbols={symbols} />
    </div>
  );
}
