'use client';

import React, { memo } from 'react';
import { cn } from '@/lib/utils';
import { useTickerTape, useTickerTapeTheme } from './useTickerTape';
import { validateSymbols, DEFAULT_CONFIG } from './TickerTape.config';
import type { TickerTapeProps, TickerTapeConfig } from './TickerTape.types';

/**
 * TradingView Ticker Tape Component
 *
 * A reusable component that displays a TradingView ticker tape widget with
 * automatic theme detection and configurable symbol sets.
 */
function TickerTapeComponent({
  symbols,
  className,
  displayMode = DEFAULT_CONFIG.displayMode,
  showSymbolLogo = DEFAULT_CONFIG.showSymbolLogo,
  isTransparent = DEFAULT_CONFIG.isTransparent,
  largeChartUrl = DEFAULT_CONFIG.largeChartUrl,
  locale = DEFAULT_CONFIG.locale,
}: TickerTapeProps) {
  const colorTheme = useTickerTapeTheme();

  // Prepare configuration for the hook (must call hooks before any early returns)
  // Use 'light' as fallback if theme not detected yet
  const actualColorTheme = colorTheme || 'light';

  // TradingView Widget Limitation: The ticker tape widget has a known issue where
  // it doesn't properly handle transparency in dark mode. When colorTheme is 'dark'
  // and isTransparent is true, the widget still renders with a dark background
  // instead of being transparent. This is a limitation of TradingView's widget itself,
  // not our implementation. As a workaround, we disable transparency in dark mode.
  const smartTransparent = actualColorTheme === 'dark' ? false : isTransparent;

  const config: TickerTapeConfig = {
    symbols,
    colorTheme: actualColorTheme,
    locale,
    largeChartUrl,
    isTransparent: smartTransparent,
    showSymbolLogo,
    displayMode,
  };

  const { containerRef, isLoading, error } = useTickerTape(config);

  // Don't render the widget until theme is properly detected
  if (!colorTheme) {
    return (
      <div
        className={cn('ticker-tape-wrapper relative w-full h-16', className)}
      >
        <div className="animate-pulse bg-muted/20 h-full flex items-center justify-center">
          <div className="text-xs text-muted-foreground">Loading ticker...</div>
        </div>
      </div>
    );
  }

  // Validate symbols after hooks
  if (!validateSymbols(symbols)) {
    console.error('TickerTape: Invalid symbols provided', symbols);
    return (
      <div
        className={cn(
          'ticker-tape-error p-4 text-center text-red-500',
          className
        )}
      >
        <p>Error: Invalid ticker symbols provided</p>
        <p className="text-xs text-muted-foreground mt-1">
          Please check that all symbols are in the correct format (e.g.,
          &quot;FWB:SAP&quot;)
        </p>
      </div>
    );
  }

  // Handle errors
  if (error) {
    return (
      <div className={cn('ticker-tape-error p-4 text-center', className)}>
        <div className="rounded-md bg-destructive/10 p-3">
          <p className="text-sm text-destructive">
            Failed to load ticker tape: {error}
          </p>
          <p className="text-xs text-muted-foreground mt-1">
            Please check your internet connection and try again.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div
      key={`ticker-${colorTheme}`}
      className={cn('ticker-tape-wrapper relative w-full', className)}
    >
      {/* Static HTML structure like the original working example */}
      <div
        className="tradingview-widget-container"
        ref={containerRef}
        role="region"
        aria-label="Stock ticker tape"
        aria-live="polite"
      >
        <div className="tradingview-widget-container__widget"></div>
      </div>

      {/* Loading skeleton - absolutely positioned overlay */}
      {isLoading && (
        <div
          className={cn(
            'absolute inset-0 h-16 animate-pulse flex items-center justify-center',
            isTransparent ? 'bg-transparent' : 'bg-muted'
          )}
        >
          <div className="flex gap-2">
            <div className="size-2 bg-muted-foreground/50 rounded-full animate-bounce" />
            <div className="size-2 bg-muted-foreground/50 rounded-full animate-bounce [animation-delay:0.1s]" />
            <div className="size-2 bg-muted-foreground/50 rounded-full animate-bounce [animation-delay:0.2s]" />
          </div>
        </div>
      )}
    </div>
  );
}

export const TickerTape = memo(TickerTapeComponent);
export default TickerTape;
