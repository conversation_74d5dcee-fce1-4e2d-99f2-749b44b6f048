export interface TickerSymbol {
  proName: string;
  title?: string;
}

export interface TickerTapeProps {
  symbols: TickerSymbol[];
  className?: string;
  displayMode?: 'adaptive' | 'regular' | 'compact';
  showSymbolLogo?: boolean;
  isTransparent?: boolean;
  largeChartUrl?: string;
  locale?: string;
}

export interface TickerTapeConfig {
  symbols: TickerSymbol[];
  colorTheme: 'light' | 'dark';
  locale: string;
  largeChartUrl: string;
  isTransparent: boolean;
  showSymbolLogo: boolean;
  displayMode: 'adaptive' | 'regular' | 'compact';
}

export interface UseTickerTapeReturn {
  containerRef: React.RefObject<HTMLDivElement | null>;
  isLoading: boolean;
  error: string | null;
}

export interface SymbolSet {
  name: string;
  symbols: TickerSymbol[];
}

export interface SymbolSets {
  [key: string]: SymbolSet;
}
