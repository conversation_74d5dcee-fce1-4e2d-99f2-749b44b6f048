import type { SymbolSets, TickerSymbol } from './TickerTape.types';

// Investment symbols from client example
const INVESTMENT_SYMBOLS: TickerSymbol[] = [
  { proName: 'FWB:SAP', title: '' },
  { proName: 'FWB:SIE', title: '' },
  { proName: 'FWB:ALV', title: '' },
  { proName: 'FWB:BMW', title: '' },
  { proName: 'FWB:DTE', title: '' },
  { proName: 'FWB:VOW', title: '' },
  { proName: 'FWB:MBG', title: '' },
  { proName: 'FWB:BAS', title: '' },
  { proName: 'FWB:MUV2', title: '' },
  { proName: 'FWB:DPW', title: '' },
  { proName: 'FWB:DBK', title: '' },
  { proName: 'FWB:CBK', title: '' },
  { proName: 'FWB:FRE', title: '' },
  { proName: 'FWB:IFX', title: '' },
  { proName: 'FWB:ADS', title: '' },
  { proName: 'FWB:RWE', title: '' },
  { proName: 'FWB:EOAN', title: '' },
  { proName: 'FWB:BAYN', title: '' },
  { proName: 'FWB:HEN3', title: '' },
  { proName: 'FWB:CON', title: '' },
  { proName: 'FWB:LIN', title: '' },
  { proName: 'FWB:P911', title: '' },
  { proName: 'FWB:QIA', title: '' },
  { proName: 'FWB:SY1', title: '' },
  { proName: 'FWB:ZAL', title: '' },
  { proName: 'FWB:HNR1', title: '' },
  { proName: 'FWB:BEI', title: '' },
  { proName: 'FWB:MTX', title: '' },
  { proName: 'FWB:DB1', title: '' },
];

// Global market indices and cryptocurrencies for homepage
const HOMEPAGE_SYMBOLS: TickerSymbol[] = [
  { proName: 'FX_IDC:EURUSD', title: '' },
  { proName: 'BITSTAMP:BTCUSD', title: '' },
  { proName: 'BITSTAMP:ETHUSD', title: '' },
  { proName: 'IG:NASDAQ', title: '' },
  { proName: 'INDEX:N100', title: '' },
  { proName: 'VANTAGE:NIKKEI225', title: '' },
  { proName: 'HSI:HSI', title: '' },
  { proName: 'GOMARKETS:FTSE100', title: '' },
  { proName: 'TSX:TSX', title: '' },
  { proName: 'MARKETSCOM:SPAIN35', title: '' },
  { proName: 'XETR:DAX', title: '' },
  { proName: 'INDEX:CAC40', title: '' },
  { proName: 'INDEX:FTSEMIB', title: '' },
  { proName: 'SIX:SMI', title: '' },
];

// Technology focused symbols - crypto, tech stocks, and German tech
const TECHNOLOGY_SYMBOLS: TickerSymbol[] = [
  { proName: 'BINANCE:ETHUSDT', title: '' },
  { proName: 'COINBASE:BTCUSD', title: '' },
  { proName: 'BINANCE:LTCUSDT', title: '' },
  { proName: 'BINANCE:SAHARAUSDT', title: '' },
  { proName: 'BINANCE:DOGEUSDT', title: '' },
  { proName: 'COINBASE:PAXUSD', title: '' },
  { proName: 'KUCOIN:HYPEUSDT', title: '' },
  { proName: 'NASDAQ:TSLA', title: '' },
  { proName: 'NASDAQ:NVDA', title: '' },
  { proName: 'NASDAQ:AAPL', title: '' },
  { proName: 'NASDAQ:AMD', title: '' },
  { proName: 'NASDAQ:AMZN', title: '' },
  { proName: 'NASDAQ:PLTR', title: '' },
  { proName: 'NASDAQ:GOOGL', title: '' },
  { proName: 'NASDAQ:META', title: '' },
  { proName: 'NYSE:SAP', title: '' },
  { proName: 'NSE:SIEMENS', title: '' },
  { proName: 'BSE:BOSCHLTD', title: '' },
  { proName: 'GETTEX:DTE', title: '' },
  { proName: 'BET:INFINEON', title: '' },
  { proName: 'XETR:ZAL', title: '' },
  { proName: 'BINANCE:XRPUSDT', title: '' },
];

// International/Forex symbols for international category
const INTERNATIONAL_SYMBOLS: TickerSymbol[] = [
  { proName: 'CMCMARKETS:EURUSD', title: '' },
  { proName: 'FX:GBPUSD', title: '' },
  { proName: 'OANDA:USDJPY', title: '' },
  { proName: 'SAXO:CADEUR', title: '' },
  { proName: 'FX_IDC:CADUSD', title: '' },
  { proName: 'CMCMARKETS:EURGBP', title: '' },
  { proName: 'CMCMARKETS:EURJPY', title: '' },
  { proName: 'OANDA:EURCHF', title: '' },
];

// Economics/Commodities symbols for wirtschaft (economy) category
const ECONOMICS_SYMBOLS: TickerSymbol[] = [
  { proName: 'TVC:GOLD', title: '' },
  { proName: 'MARKETSCOM:OIL', title: '' },
  { proName: 'TVC:SILVER', title: '' },
  { proName: 'CAPITALCOM:COPPER', title: '' },
  { proName: 'PEPPERSTONE:ZINC', title: '' },
  { proName: 'PEPPERSTONE:ALUMINIUM', title: '' },
  { proName: 'CAPITALCOM:NATURALGAS', title: '' },
  { proName: 'FOREXCOM:COTTON', title: '' },
  { proName: 'FOREXCOM:COFFEE', title: '' },
  { proName: 'PEPPERSTONE:SUGAR', title: '' },
  { proName: 'CAPITALCOM:PLATINUM', title: '' },
];

export const SYMBOL_SETS: SymbolSets = {
  homepage: {
    name: 'Homepage Mixed',
    symbols: HOMEPAGE_SYMBOLS,
  },
  investment: {
    name: 'Investment Focus',
    symbols: INVESTMENT_SYMBOLS,
  },
  technology: {
    name: 'Technology Sector',
    symbols: TECHNOLOGY_SYMBOLS,
  },
  international: {
    name: 'International Markets',
    symbols: INTERNATIONAL_SYMBOLS,
  },
  wirtschaft: {
    name: 'Economics & Commodities',
    symbols: ECONOMICS_SYMBOLS,
  },
} as const;

export const DEFAULT_CONFIG = {
  locale: 'de_DE', // Use German locale like in the working TradingView example
  largeChartUrl: '',
  isTransparent: true, // Transparent by default
  showSymbolLogo: false, // Hide symbol logos for cleaner appearance
  displayMode: 'adaptive' as const, // Use adaptive mode like in the working TradingView example
};

/**
 * Get symbol set for a specific category
 */
export function getSymbolSetForCategory(categorySlug: string): TickerSymbol[] {
  // Map category slugs to symbol sets
  const categoryMapping: Record<string, keyof typeof SYMBOL_SETS> = {
    investment: 'investment',
    technology: 'technology',
    technologie: 'technology', // German slug for technology category
    tech: 'technology',
    international: 'international',
    wirtschaft: 'wirtschaft',
    economics: 'wirtschaft', // Alternative mapping
    // Add more mappings as needed
  };

  const symbolSetKey = categoryMapping[categorySlug] || 'homepage';
  return SYMBOL_SETS[symbolSetKey].symbols;
}

/**
 * Validate symbol format
 */
export function validateSymbol(symbol: TickerSymbol): boolean {
  return (
    typeof symbol.proName === 'string' &&
    symbol.proName.length > 0 &&
    symbol.proName.includes(':')
  );
}

/**
 * Validate array of symbols
 */
export function validateSymbols(symbols: TickerSymbol[]): boolean {
  return (
    Array.isArray(symbols) &&
    symbols.length > 0 &&
    symbols.every(validateSymbol)
  );
}
