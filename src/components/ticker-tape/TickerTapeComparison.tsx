'use client';

import React, { useEffect, useRef, memo } from 'react';
import { useTheme } from 'next-themes';
import { cn } from '@/lib/utils';
import { useTickerTape, useTickerTapeTheme } from './useTickerTape';
import { DEFAULT_CONFIG } from './TickerTape.config';
import type { TickerSymbol, TickerTapeConfig } from './TickerTape.types';

// Test symbols for comparison
const TEST_SYMBOLS: TickerSymbol[] = [
  { proName: 'FOREXCOM:SPXUSD', title: 'S&P 500 Index' },
  { proName: 'FOREXCOM:NSXUSD', title: 'US 100 Cash CFD' },
  { proName: 'FX_IDC:EURUSD', title: 'EUR to USD' },
  { proName: 'BITSTAMP:BTCUSD', title: 'Bitcoin' },
  { proName: 'BITSTAMP:ETHUSD', title: 'Ethereum' },
];

/**
 * Original TradingView Example Implementation
 * Exactly as provided by TradingView documentation
 */
function OriginalTradingViewWidget() {
  const container = useRef<HTMLDivElement>(null);
  const { theme, systemTheme, resolvedTheme } = useTheme();
  
  // Determine effective theme
  const effectiveTheme = resolvedTheme || (theme === 'system' ? systemTheme : theme);
  const colorTheme = effectiveTheme === 'dark' ? 'dark' : 'light';

  useEffect(() => {
    if (!container.current) return;

    // Clear previous content
    container.current.innerHTML = '';

    const script = document.createElement("script");
    script.src = "https://s3.tradingview.com/external-embedding/embed-widget-ticker-tape.js";
    script.type = "text/javascript";
    script.async = true;
    script.innerHTML = `
      {
        "symbols": [
          {
            "proName": "FOREXCOM:SPXUSD",
            "title": "S&P 500 Index"
          },
          {
            "proName": "FOREXCOM:NSXUSD",
            "title": "US 100 Cash CFD"
          },
          {
            "proName": "FX_IDC:EURUSD",
            "title": "EUR to USD"
          },
          {
            "proName": "BITSTAMP:BTCUSD",
            "title": "Bitcoin"
          },
          {
            "proName": "BITSTAMP:ETHUSD",
            "title": "Ethereum"
          }
        ],
        "colorTheme": "${colorTheme}",
        "locale": "de_DE",
        "largeChartUrl": "",
        "isTransparent": true,
        "showSymbolLogo": false,
        "displayMode": "adaptive"
      }`;

    // Create the widget container structure
    const widgetContainer = document.createElement('div');
    widgetContainer.className = 'tradingview-widget-container__widget';
    
    const copyrightDiv = document.createElement('div');
    copyrightDiv.className = 'tradingview-widget-copyright';
    copyrightDiv.innerHTML = '<a href="https://de.tradingview.com/" rel="noopener nofollow" target="_blank"><span class="blue-text">Track all markets on TradingView</span></a>';

    container.current.appendChild(widgetContainer);
    container.current.appendChild(copyrightDiv);
    container.current.appendChild(script);
  }, [colorTheme]);

  return (
    <div className="tradingview-widget-container" ref={container}>
      {/* Content will be populated by the script */}
    </div>
  );
}

/**
 * Current Implementation with Transparency Enabled
 * Your current approach but with the transparency workaround removed
 */
function CurrentImplementationWidget() {
  const colorTheme = useTickerTapeTheme();
  
  // Use 'light' as fallback if theme not detected yet
  const actualColorTheme = colorTheme || 'light';

  // REMOVED THE WORKAROUND - Enable transparency in dark mode
  const config: TickerTapeConfig = {
    symbols: TEST_SYMBOLS,
    colorTheme: actualColorTheme,
    locale: DEFAULT_CONFIG.locale,
    largeChartUrl: DEFAULT_CONFIG.largeChartUrl,
    isTransparent: true, // Always true - no workaround
    showSymbolLogo: DEFAULT_CONFIG.showSymbolLogo,
    displayMode: DEFAULT_CONFIG.displayMode,
  };

  const { containerRef, isLoading, error } = useTickerTape(config);

  // Don't render until theme is detected
  if (!colorTheme) {
    return (
      <div className="h-16 animate-pulse bg-muted/20 flex items-center justify-center">
        <div className="text-xs text-muted-foreground">Loading theme...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 text-center">
        <div className="rounded-md bg-destructive/10 p-3">
          <p className="text-sm text-destructive">Failed to load: {error}</p>
        </div>
      </div>
    );
  }

  return (
    <div key={`ticker-${colorTheme}`} className="relative w-full">
      <div
        className="tradingview-widget-container"
        ref={containerRef}
        role="region"
        aria-label="Stock ticker tape"
        aria-live="polite"
      >
        <div className="tradingview-widget-container__widget"></div>
      </div>

      {isLoading && (
        <div className="absolute inset-0 h-16 animate-pulse flex items-center justify-center bg-transparent">
          <div className="flex gap-2">
            <div className="size-2 bg-muted-foreground/50 rounded-full animate-bounce" />
            <div className="size-2 bg-muted-foreground/50 rounded-full animate-bounce [animation-delay:0.1s]" />
            <div className="size-2 bg-muted-foreground/50 rounded-full animate-bounce [animation-delay:0.2s]" />
          </div>
        </div>
      )}
    </div>
  );
}

/**
 * Theme Toggle Component for Testing
 */
function ThemeToggle() {
  const { theme, setTheme } = useTheme();

  return (
    <div className="flex gap-2 mb-4">
      <button
        onClick={() => setTheme('light')}
        className={cn(
          'px-3 py-1 rounded text-sm border',
          theme === 'light' ? 'bg-primary text-primary-foreground' : 'bg-background'
        )}
      >
        Light
      </button>
      <button
        onClick={() => setTheme('dark')}
        className={cn(
          'px-3 py-1 rounded text-sm border',
          theme === 'dark' ? 'bg-primary text-primary-foreground' : 'bg-background'
        )}
      >
        Dark
      </button>
      <button
        onClick={() => setTheme('system')}
        className={cn(
          'px-3 py-1 rounded text-sm border',
          theme === 'system' ? 'bg-primary text-primary-foreground' : 'bg-background'
        )}
      >
        System
      </button>
    </div>
  );
}

/**
 * Side-by-Side Comparison Component
 */
function TickerTapeComparison() {
  return (
    <div className="p-6 space-y-6">
      <div className="text-center">
        <h1 className="text-2xl font-bold mb-2">TradingView Ticker Tape Comparison</h1>
        <p className="text-muted-foreground mb-4">
          Testing transparency behavior in dark mode - both widgets should look identical
        </p>
        <ThemeToggle />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Original Implementation */}
        <div className="space-y-4">
          <div className="border rounded-lg p-4">
            <h2 className="text-lg font-semibold mb-2 text-green-600">
              ✅ Original TradingView Example
            </h2>
            <p className="text-sm text-muted-foreground mb-4">
              Exact implementation from TradingView documentation
            </p>
            <div className="border rounded overflow-hidden">
              <OriginalTradingViewWidget />
            </div>
          </div>
        </div>

        {/* Current Implementation */}
        <div className="space-y-4">
          <div className="border rounded-lg p-4">
            <h2 className="text-lg font-semibold mb-2 text-orange-600">
              🔧 Current Implementation (Transparency Enabled)
            </h2>
            <p className="text-sm text-muted-foreground mb-4">
              Your current approach with transparency workaround removed
            </p>
            <div className="border rounded overflow-hidden">
              <CurrentImplementationWidget />
            </div>
          </div>
        </div>
      </div>

      <div className="mt-8 p-4 bg-muted/50 rounded-lg">
        <h3 className="font-semibold mb-2">Testing Instructions:</h3>
        <ol className="list-decimal list-inside space-y-1 text-sm">
          <li>Switch between Light and Dark themes using the buttons above</li>
          <li>Compare the transparency behavior of both widgets</li>
          <li>In dark mode, both should have transparent backgrounds</li>
          <li>Look for any visual differences in styling or behavior</li>
          <li>Check browser console for any error messages</li>
        </ol>
      </div>
    </div>
  );
}

export default memo(TickerTapeComparison);
