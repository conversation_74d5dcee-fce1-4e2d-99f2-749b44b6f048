# TradingView Ticker Tape Component

A reusable, theme-aware React component for embedding TradingView ticker tape widgets with automatic light/dark mode switching and configurable symbol sets.

## ✨ Features

- 🎨 **Automatic theme inheritance** - Seamlessly adapts to your Next.js theme
- 🌙 **Dark mode transparency** - Full transparency support in both light and dark themes
- 📱 **Fully responsive** - Works on all screen sizes
- 🚀 **Performance optimized** - Memoized with efficient re-rendering
- 🔧 **Highly configurable** - Multiple display modes and options
- 🌐 **TypeScript support** - Full type safety and IntelliSense
- ♿ **Accessibility compliant** - ARIA attributes and screen reader support
- 🎯 **Loading states** - Smooth skeleton animations during load
- 🛡️ **Error handling** - Graceful fallbacks for failed loads
- 📐 **Full viewport width** - Spans entire screen width when needed
- 🎨 **Clean, borderless design** - Professional appearance

## Installation

The component is already integrated into the project. Import it from:

```typescript
import { TickerTape } from '@/components/ticker-tape';
```

## 🚀 Quick Start

### Basic Usage

```tsx
import { TickerTape } from '@/components/ticker-tape';

export default function MyPage() {
  return (
    <div>
      {/* Full viewport width ticker */}
      <TickerTape
        symbols={[
          { proName: 'FWB:SAP', title: '' },
          { proName: 'FWB:SIE', title: '' },
          { proName: 'NASDAQ:AAPL', title: '' },
        ]}
      />

      {/* Container-constrained ticker */}
      <div className="container mx-auto">
        <TickerTape
          symbols={[
            { proName: 'FWB:BMW', title: '' },
            { proName: 'FWB:DTE', title: '' },
          ]}
          className="border rounded-lg overflow-hidden"
        />
      </div>
    </div>
  );
}
```

### With Custom Configuration

```tsx
import { TickerTape } from '@/components/ticker-tape';

const customSymbols = [
  { proName: 'FWB:SAP', title: '' },
  { proName: 'FWB:SIE', title: '' },
  { proName: 'NASDAQ:AAPL', title: '' },
];

export default function CustomPage() {
  return (
    <TickerTape
      symbols={customSymbols}
      displayMode="compact"
      showSymbolLogo={false}
      isTransparent={true}
    />
  );
}
```

## Props API

### TickerTapeProps

| Prop             | Type                                   | Default      | Description                           |
| ---------------- | -------------------------------------- | ------------ | ------------------------------------- |
| `symbols`        | `TickerSymbol[]`                       | **Required** | Array of stock symbols to display     |
| `className`      | `string`                               | `undefined`  | Additional CSS classes                |
| `displayMode`    | `'adaptive' \| 'regular' \| 'compact'` | `'adaptive'` | Display mode for the ticker           |
| `showSymbolLogo` | `boolean`                              | `true`       | Whether to show company logos         |
| `isTransparent`  | `boolean`                              | `true`       | Whether the background is transparent |
| `largeChartUrl`  | `string`                               | `''`         | URL for large chart view              |
| `locale`         | `string`                               | `'en'`       | Locale for the widget                 |

### TickerSymbol Interface

```typescript
interface TickerSymbol {
  proName: string; // Exchange:Symbol format (e.g., 'FWB:SAP')
  title?: string; // Optional display title
}
```

## Predefined Symbol Sets

### Available Sets

```typescript
import { SYMBOL_SETS } from '@/components/ticker-tape';

// Homepage - Major DAX stocks
SYMBOL_SETS.homepage.symbols;

// Investment - Full investment category set (29 symbols)
SYMBOL_SETS.investment.symbols;

// Technology - Tech-focused symbols
SYMBOL_SETS.technology.symbols;
```

### Investment Symbol Set (Default Example)

The component includes the complete investment symbol set provided by the client:

- SAP, Siemens, Allianz, BMW, Deutsche Telekom
- Volkswagen, Mercedes-Benz, BASF, Munich Re, Deutsche Post
- Deutsche Bank, Commerzbank, Fresenius, Infineon, Adidas
- RWE, E.ON, Bayer, Henkel, Continental
- Linde, Porsche, QIAGEN, Symrise, Zalando
- Hannover Re, Beiersdorf, MTU Aero Engines, Deutsche Börse

## Category Integration

### Automatic Symbol Selection

```typescript
import { getSymbolSetForCategory } from '@/components/ticker-tape';

export default function CategoryPage({ params }: { params: { slug: string } }) {
  const symbols = getSymbolSetForCategory(params.slug);

  return <TickerTape symbols={symbols} />;
}
```

### Category Mappings

- `investment` → Investment symbol set
- `technology` or `tech` → Technology symbol set
- Default → Homepage symbol set

## Theme Integration

The component automatically inherits your site's theme using the `useTheme()` hook from `next-themes`:

- **Light Mode**: `colorTheme: 'light'`
- **Dark Mode**: `colorTheme: 'dark'`
- **System Mode**: Follows system preference

No manual theme configuration required!

## Error Handling

The component handles various error scenarios:

### Invalid Symbols

```

```
