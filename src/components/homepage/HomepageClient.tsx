'use client';

import { usePageTicker } from '@/components/ticker-tape/TickerTapeContext';
import type { TickerSymbol } from '@/components/ticker-tape/TickerTape.types';

interface HomepageClientProps {
  symbols: TickerSymbol[];
}

/**
 * Client component that sets up the homepage ticker via context
 * This allows the ticker to be rendered above the navigation in the layout
 */
export function HomepageClient({ symbols }: HomepageClientProps) {
  // Set the ticker symbols for this page
  usePageTicker(symbols);

  // This component doesn't render anything - it just sets up the ticker
  return null;
}
