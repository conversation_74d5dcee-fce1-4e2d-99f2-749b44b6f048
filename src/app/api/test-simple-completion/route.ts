import { NextRequest, NextResponse } from 'next/server';
import { testNonStructuredCompletion } from '@/lib/integrations/openai/unified-enhancement';

/**
 * Simple test endpoint that mimics ChatGPT exactly
 * No structured output, no complex schemas - just rewrite the article
 */
export async function POST(request: NextRequest) {
  try {
    const { title, content } = await request.json();

    if (!title || !content) {
      return NextResponse.json(
        { error: 'Title and content are required' },
        { status: 400 }
      );
    }

    console.log('🧪 SIMPLE TEST: Starting ChatGPT-style completion...');
    console.log(`📄 Input: "${title}" (${content.length} chars)`);

    const result = await testNonStructuredCompletion(title, content);

    if (result.success) {
      console.log('✅ SIMPLE TEST: Success!');
      console.log(`📊 Results: ${result.wordCount} words, ${result.isComplete ? 'COMPLETE' : 'INCOMPLETE'}`);
    } else {
      console.error('❌ SIMPLE TEST: Failed:', result.error);
    }

    return NextResponse.json({
      success: result.success,
      results: result,
      comparison: {
        expectedBehavior: 'ChatGPT would complete this instantly',
        actualBehavior: result.isComplete ? 'SUCCESS - Works like ChatGPT' : 'FAILED - Different from ChatGPT',
      },
    });
  } catch (error) {
    console.error('🧪 SIMPLE TEST ERROR:', error);
    return NextResponse.json(
      {
        error: 'Test failed',
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    );
  }
} 