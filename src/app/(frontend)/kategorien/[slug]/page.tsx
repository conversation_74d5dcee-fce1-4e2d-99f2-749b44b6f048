import { Suspense } from 'react';
import type { Metadata } from 'next';
import { notFound } from 'next/navigation';
import {
  getCachedCategoryArticles,
  getCachedCategorySlugs,
} from '@/lib/cache/categories';
import CategoryPageLayout from '@/components/categories/CategoryPageLayout';
import CategoryAccessibilityNav from '@/components/categories/CategoryAccessibilityNav';
import CategoryPageSkeleton from '@/components/categories/CategoryPageSkeleton';
import { CategoryClient } from '@/components/categories/CategoryClient';

interface CategoryPageProps {
  params: Promise<{ slug: string }>;
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}

// Generate metadata for SEO
export async function generateMetadata({
  params,
  searchParams,
}: {
  params: Promise<{ slug: string }>;
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}): Promise<Metadata> {
  const { slug } = await params;
  const resolvedSearchParams = await searchParams;
  const page = resolvedSearchParams.page
    ? parseInt(String(resolvedSearchParams.page), 10)
    : 1;

  try {
    const getCachedData = getCachedCategoryArticles(slug);
    const categoryData = await getCachedData();

    // ✅ FIX: Handle null case (category not found)
    if (!categoryData) {
      console.error(`Error generating metadata for category: ${slug}`);
      notFound();
    }

    const { categoryInfo, totalArticles } = categoryData;

    const title = categoryInfo.title;
    const baseDomain = 'https://borsenblick.de';

    // Handle pagination in metadata
    const pageTitle =
      page > 1
        ? `${title} - Seite ${page} | Börsen Blick`
        : `${title} | Börsen Blick`;

    const description =
      page > 1
        ? `${title}-Nachrichten - Seite ${page}. Weitere Artikel und Analysen aus ${totalArticles} verfügbaren Beiträgen.`
        : `Aktuelle ${title}-Nachrichten und Analysen. ${totalArticles} Artikel verfügbar.`;

    const canonicalUrl =
      page > 1
        ? `${baseDomain}/kategorien/${slug}?page=${page}`
        : `${baseDomain}/kategorien/${slug}`;

    // Build pagination links
    const alternates: any = {
      canonical: canonicalUrl,
    };

    // Add prev/next links for SEO
    if (page > 1) {
      alternates.prev =
        page === 2
          ? `${baseDomain}/kategorien/${slug}`
          : `${baseDomain}/kategorien/${slug}?page=${page - 1}`;
    }

    // We can't easily determine if there's a next page without more data
    // This would be better handled with actual pagination info from the API

    return {
      title: pageTitle,
      description,
      openGraph: {
        title: `${title} - Börsen Blick`,
        description,
        type: 'website',
        url: canonicalUrl,
      },
      twitter: {
        card: 'summary_large_image',
        title: pageTitle,
        description,
      },
      alternates,
      robots: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    };
  } catch (error) {
    console.error(`Error generating metadata for category: ${slug}`, error);
    return {
      title: 'Category Not Found | Börsen Blick',
      description: 'The requested category could not be found.',
    };
  }
}

// Generate static params for published categories using cached data
export async function generateStaticParams(): Promise<{ slug: string }[]> {
  try {
    return await getCachedCategorySlugs();
  } catch (error) {
    console.error('Error generating category static params:', error);
    return [];
  }
}

// Enable Next.js ISR (Incremental Static Regeneration)
export const revalidate = 300; // 5 minutes

// Main category page component
export default async function CategoryPage({
  params,
  searchParams,
}: CategoryPageProps) {
  const { slug } = await params;
  const resolvedSearchParams = await searchParams;
  const page = resolvedSearchParams.page
    ? parseInt(String(resolvedSearchParams.page), 10)
    : 1;

  // Validate category exists by attempting to fetch data
  try {
    const getCachedData = getCachedCategoryArticles(slug);
    const categoryData = await getCachedData();

    // ✅ FIX: Check if category data is null (category not found)
    if (!categoryData) {
      console.error(`Category not found: ${slug}`);
      notFound();
    }
  } catch (error) {
    console.error(`Error fetching category: ${slug}`, error);
    notFound();
  }

  return (
    <div className="min-h-dvh bg-background">
      {/* Set up category ticker via context (renders above navigation) */}
      <CategoryClient categorySlug={slug} />

      {/* Accessibility Navigation */}
      <CategoryAccessibilityNav categorySlug={slug} />

      {/* Main Content */}
      <main
        id="main-content"
        className="focus:outline-none"
        tabIndex={-1}
        role="main"
        aria-label={`Category page`}
      >
        {/* Page Title - Hidden but accessible */}
        <h1 className="sr-only">
          {page > 1 ? `Category Articles - Page ${page}` : 'Category Articles'}
        </h1>

        {/* Category Page Layout with Suspense */}
        <Suspense fallback={<CategoryPageSkeleton />}>
          <CategoryPageLayout categorySlug={slug} currentPage={page} />
        </Suspense>
      </main>
    </div>
  );
}
