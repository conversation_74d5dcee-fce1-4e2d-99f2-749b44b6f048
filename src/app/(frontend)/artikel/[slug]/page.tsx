import type { <PERSON>ada<PERSON> } from 'next';
import { notFound } from 'next/navigation';
import { Suspense } from 'react';
import { draftMode } from 'next/headers';
import { getPayload } from 'payload';
import config from '@payload-config';
import { getCachedDocument } from '@/utilities/getDocument';
import { getCachedPublishedArticleSlugs } from '@/lib/cache/articles';
import type { Article, Media, Category } from '@/payload-types';
import ArticleHeader from '@/components/articles/ArticleHeader';
import ArticleMetadata from '@/components/articles/ArticleMetadata';
import ArticleContent from '@/components/articles/ArticleContent';
import RelatedArticlesSection from '@/components/articles/RelatedArticlesSection';
import RelatedArticlesSkeleton from '@/components/articles/RelatedArticlesSkeleton';
import ArticleAccessibilityNav from '@/components/articles/ArticleAccessibilityNav';

interface ArticlePageProps {
  params: Promise<{ slug: string }>;
}

// Function to get article with draft mode support
async function getArticle(slug: string, isDraftMode: boolean) {
  if (isDraftMode) {
    // Fetch draft version when in draft mode
    const payload = await getPayload({ config });
    const articles = await payload.find({
      collection: 'articles',
      where: { slug: { equals: slug } },
      draft: true,
      limit: 1,
    });
    return articles.docs[0] || null;
  } else {
    // Use cached version for published articles
    const getCachedDoc = getCachedDocument('articles', slug, 2);
    const result = await getCachedDoc();
    return result as unknown as Article | null;
  }
}

// Generate metadata for SEO
export async function generateMetadata({
  params,
}: {
  params: Promise<{ slug: string }>;
}): Promise<Metadata> {
  const { slug } = await params;
  const draft = await draftMode();
  const isDraftMode = draft.isEnabled;

  const getCachedDoc = getCachedDocument('articles', slug, 2);
  const result = await getCachedDoc();
  const article = result as unknown as Article | null;

  // In metadata generation, we only generate metadata for published articles
  // (Draft articles will use default metadata)
  if (!article || (!isDraftMode && (article as any)._status !== 'published')) {
    return {
      title: 'Article Not Found | Börsen Blick',
      description: 'The requested article could not be found.',
    };
  }

  // Prioritise German content first, then English, then fallback
  const title =
    article.germanTab?.germanTitle ||
    article.englishTab?.enhancedTitle ||
    article.title;

  const description =
    article.germanTab?.germanSummary ||
    article.englishTab?.enhancedSummary ||
    'Read the latest financial news and market analysis.';

  // Handle featuredImage which can be number (ID) or Media object
  const featuredImage =
    typeof article.featuredImage === 'object'
      ? (article.featuredImage as Media)
      : null;

  return {
    title: `${title} | Börsen Blick`,
    description,
    openGraph: {
      title,
      description,
      type: 'article',
      publishedTime: article.publishedAt || article.createdAt,
      authors: ['Börsen Blick Team'],
      images: featuredImage?.url
        ? [
            {
              url: featuredImage.sizes?.social?.url || featuredImage.url,
              width: 1200,
              height: 630,
              alt: featuredImage.alt || title,
            },
          ]
        : [],
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
      images: featuredImage?.url
        ? [featuredImage.sizes?.social?.url || featuredImage.url]
        : [],
    },
  };
}

// Generate static params for published articles using cached data
export async function generateStaticParams(): Promise<{ slug: string }[]> {
  try {
    return await getCachedPublishedArticleSlugs();
  } catch (error) {
    console.error('Error generating static params:', error);
    return [];
  }
}

// Enable dynamic params for draft routes
export const dynamicParams = true;

// Main article page component
export default async function ArticlePage({ params }: ArticlePageProps) {
  const { slug } = await params;
  const draft = await draftMode();
  const isDraftMode = draft.isEnabled;

  const article = await getArticle(slug, isDraftMode);

  // Check access: draft mode shows any article, normal mode only shows published articles
  // PayloadCMS handles draft/published status internally via the getArticle function
  if (!article) {
    notFound();
  }

  return (
    <div className="min-h-dvh bg-background">
      {/* Draft Mode Banner */}
      {isDraftMode && (
        <div className="bg-yellow-50 border-b border-yellow-200 px-4 py-2">
          <div className="max-w-[1440px] mx-auto text-center">
            <span className="text-yellow-800 text-sm font-medium">
              🚧 Draft Mode: You are viewing unpublished content
            </span>
            <span className="mx-2 text-yellow-600">•</span>
            <a
              href={`/api/exit-preview?slug=/artikel/${slug}`}
              className="text-yellow-800 text-sm font-medium hover:text-yellow-900 underline"
            >
              Exit Draft Mode
            </a>
          </div>
        </div>
      )}

      {/* Accessibility Navigation */}
      <ArticleAccessibilityNav />

      {/* Match Homepage Grid Layout Exactly */}
      <div className="max-w-[1440px] mx-auto px-4 py-8 lg:py-12">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-0">
          {/* Column 1: Article metadata and details */}
          <aside
            id="article-metadata"
            className="sm:border-r sm:border-border sm:pr-2 lg:pr-3 xl:pr-4 mb-8 sm:mb-10 lg:mb-12 xl:mb-0 order-3 sm:order-1"
            aria-labelledby="article-details-heading"
            tabIndex={-1}
          >
            <h2 id="article-details-heading" className="sr-only">
              Article Details
            </h2>
            <div className="flex flex-col gap-6">
              <ArticleHeader article={article} locale="de" />
              <ArticleMetadata article={article} />
            </div>
          </aside>

          {/* Columns 2-3: Hero image and main content */}
          <section
            id="article-content"
            className="sm:col-span-1 lg:col-span-2 xl:col-span-2 sm:border-r sm:border-border sm:px-2 lg:px-3 xl:px-4 mb-8 sm:mb-10 lg:mb-12 xl:mb-0 order-1 sm:order-2"
            aria-labelledby="article-content-heading"
            tabIndex={-1}
          >
            <h2 id="article-content-heading" className="sr-only">
              Article Content
            </h2>
            <ArticleContent article={article} locale="de" />
          </section>

          {/* Column 4: Related articles sidebar */}
          <aside
            id="related-articles"
            className="xl:pl-4 order-2 sm:order-3"
            aria-labelledby="related-articles-heading"
            tabIndex={-1}
          >
            <h2 id="related-articles-heading" className="sr-only">
              Related Articles
            </h2>
            <Suspense fallback={<RelatedArticlesSkeleton maxArticles={4} />}>
              <RelatedArticlesSection
                currentArticle={article}
                maxArticles={4}
                locale="de"
              />
            </Suspense>
          </aside>
        </div>
      </div>
    </div>
  );
}
