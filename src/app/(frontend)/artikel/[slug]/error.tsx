'use client';

import { useEffect } from 'react';
import Link from 'next/link';

interface ErrorProps {
  error: Error & { digest?: string };
  reset: () => void;
}

export default function ArticleError({ error, reset }: ErrorProps) {
  useEffect(() => {
    // Log error to monitoring service
    console.error('Article page error:', error);
  }, [error]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full bg-white shadow-lg rounded-lg p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">
          Something went wrong!
        </h2>
        <p className="text-gray-600 mb-6">
          We encountered an error whilst loading this article. This could be due
          to:
        </p>
        <ul className="text-sm text-gray-500 mb-6 space-y-1">
          <li>• The article may have been moved or deleted</li>
          <li>• Network connectivity issues</li>
          <li>• Temporary server problems</li>
        </ul>
        <div className="flex gap-3">
          <button
            onClick={reset}
            className="flex-1 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colours"
          >
            Try Again
          </button>
          <Link
            href="/"
            className="flex-1 text-center bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200 transition-colours"
          >
            Go Home
          </Link>
        </div>
      </div>
    </div>
  );
}
