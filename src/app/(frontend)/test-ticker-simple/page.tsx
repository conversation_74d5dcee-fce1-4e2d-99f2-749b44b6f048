import React from 'react';

export default function TestTickerSimplePage() {
  return (
    <div className="container mx-auto py-8 space-y-8">
      <h1 className="text-3xl font-bold text-center mb-8">
        Simple TradingView Test
      </h1>

      <div className="space-y-4">
        <h2 className="text-xl font-semibold">Original Pattern Test</h2>
        <p className="text-sm text-muted-foreground">
          Using the exact pattern from your original example
        </p>

        {/* This should work exactly like your original example */}
        <div className="tradingview-widget-container">
          <div className="tradingview-widget-container__widget"></div>
          <div className="tradingview-widget-copyright">
            <a
              href="https://www.tradingview.com/"
              rel="noopener nofollow"
              target="_blank"
            >
              <span className="blue-text">
                Track all markets on TradingView
              </span>
            </a>
          </div>
          <script
            type="text/javascript"
            src="https://s3.tradingview.com/external-embedding/embed-widget-ticker-tape.js"
            async
            dangerouslySetInnerHTML={{
              __html: JSON.stringify({
                symbols: [
                  {
                    proName: 'FWB:SAP',
                    title: '',
                  },
                  {
                    proName: 'FWB:SIE',
                    title: '',
                  },
                  {
                    proName: 'FWB:ALV',
                    title: '',
                  },
                  {
                    proName: 'FWB:BMW',
                    title: '',
                  },
                  {
                    proName: 'FWB:DTE',
                    title: '',
                  },
                ],
                colorTheme: 'light',
                locale: 'en',
                largeChartUrl: '',
                isTransparent: false,
                showSymbolLogo: true,
                displayMode: 'adaptive',
              }),
            }}
          />
        </div>
      </div>

      <div className="space-y-4">
        <h2 className="text-xl font-semibold">Alternative Test</h2>
        <p className="text-sm text-muted-foreground">
          Testing with a different approach
        </p>

        <div id="test-widget-2">
          {/* This will be populated by the script below */}
        </div>

        <script
          dangerouslySetInnerHTML={{
            __html: `
              (function() {
                const container = document.getElementById('test-widget-2');
                if (container) {
                  container.innerHTML = '<div class="tradingview-widget-container"><div class="tradingview-widget-container__widget"></div><div class="tradingview-widget-copyright"><a href="https://www.tradingview.com/" rel="noopener nofollow" target="_blank"><span class="blue-text">Track all markets on TradingView</span></a></div></div>';
                  
                  const script = document.createElement('script');
                  script.type = 'text/javascript';
                  script.src = 'https://s3.tradingview.com/external-embedding/embed-widget-ticker-tape.js';
                  script.async = true;
                  script.innerHTML = JSON.stringify({
                    "symbols": [
                      {"proName": "FWB:SAP", "title": ""},
                      {"proName": "FWB:SIE", "title": ""},
                      {"proName": "FWB:BMW", "title": ""}
                    ],
                    "colorTheme": "light",
                    "locale": "en",
                    "largeChartUrl": "",
                    "isTransparent": false,
                    "showSymbolLogo": true,
                    "displayMode": "adaptive"
                  });
                  
                  container.appendChild(script);
                }
              })();
            `,
          }}
        />
      </div>
    </div>
  );
}
