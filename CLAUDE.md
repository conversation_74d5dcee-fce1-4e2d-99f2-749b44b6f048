# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Core Development

```bash
# Development server
pnpm dev                    # Start Next.js development server
pnpm devsafe               # Clean .next cache and start dev server

# Build and production
pnpm build                 # Build for production
pnpm start                 # Start production server

# Type checking and linting
pnpm lint                  # Run ESLint on src directory
pnpm lint:fix              # Fix ESLint issues automatically
pnpm format                # Format code with Prettier
pnpm format:check          # Check code formatting
pnpm lint:all              # Run both lint and format:check
pnpm fix:all               # Fix both lint and format issues

# PayloadCMS operations
pnpm generate:types        # Generate TypeScript types (run after schema changes)
pnpm generate:importmap    # Generate import map
pnpm payload               # PayloadCMS CLI commands

# Database migrations
pnpm migrate              # Run database migrations
pnpm migrate:create       # Create new migration
pnpm migrate:status       # Check migration status
pnpm migrate:fresh        # Reset and run all migrations
pnpm migrate:reset        # Reset migrations

# Testing
pnpm test                 # Run tests with vitest (if test script exists)

# Monitoring
pnpm check:openai         # Check OpenAI API status

# CI pipeline
pnpm ci                   # Run migrations and build (used in CI)
```

### Testing Commands

The project uses Vites<PERSON> for testing:

```bash
vitest                    # Run tests in watch mode
vitest run               # Run tests once
vitest --coverage        # Run tests with coverage
```

## Architecture Overview

Börsen Blick is a sophisticated financial news platform built on **PayloadCMS 3.0** that processes German financial content through an AI-enhanced pipeline:

**RSS Feeds → Firecrawl Extraction → OpenAI Enhancement → Lexical Storage → Multi-language Publication**

### Tech Stack

- **Framework**: Next.js 15 (App Router) with React 19
- **CMS**: PayloadCMS 3.0 with PostgreSQL adapter
- **Database**: Supabase PostgreSQL
- **Styling**: Tailwind CSS v4 + ShadcnUI components
- **Rich Text**: Lexical editor with PayloadCMS integration
- **AI Processing**: OpenAI GPT-4 for content enhancement and translation
- **Web Scraping**: Firecrawl with domain-specific configurations
- **Testing**: Vitest with jsdom environment

### Key Collections (Data Models)

#### Articles Collection

The central content model with sophisticated multi-tab structure:

- **English Tab**: AI-enhanced English content for international markets
- **Sources Tab**: Original content preservation and metadata
- **German Translation Tab**: Conditional German translations
- **SEO Tab**: Meta information and optimization

**Workflow States**: `candidate-article` → `translated` → `ready-for-review` → `published`

#### Supporting Collections

- **ProcessedUrls**: URL deduplication and processing tracking
- **RSSFeeds**: Source feed management with priority levels
- **Categories**: Content organization and filtering
- **Keywords**: Financial terms for content matching
- **Media**: Image management with optimized sizes

### Content Processing Pipeline

1. **RSS Discovery**: Fetches from prioritized German financial feeds
2. **Content Extraction**: Enhanced Firecrawl processing with site-specific configurations
3. **AI Enhancement**: OpenAI processing for readability, SEO, and translation
4. **Lexical Conversion**: HTML to Lexical format using PayloadCMS native utilities
5. **Publication**: Multi-stage editorial workflow

## PayloadCMS Development Patterns

### Always Use PayloadCMS Native Features

- **Rich Text**: Use `defaultLexical` configuration from `@/fields/defaultLexical`
- **Type Generation**: Run `pnpm generate:types` after schema changes
- **HTML to Lexical**: Use PayloadCMS native conversion utilities
- **Field Hooks**: Follow PayloadCMS argument patterns and return values
- **Access Control**: Leverage built-in access control patterns

### Lexical Editor Integration

Always use the project's standardized Lexical configuration:

```typescript
import { defaultLexical } from '@/fields/defaultLexical';

{
  type: 'richText',
  name: 'content',
  editor: defaultLexical,
  hooks: {
    afterRead: [({ value }) => prepareLexicalForReading(value)],
    beforeChange: [({ value }) => prepareLexicalForStorage(value)],
  }
}
```

### Field Configuration Patterns

- Use PayloadCMS conditional fields with `admin.condition`
- Leverage `admin.position: 'sidebar'` for metadata fields
- Use proper field validation with PayloadCMS patterns
- Implement field hooks for auto-population and transformation

## External Service Integrations

### Firecrawl Integration

Use the enhanced client with domain-specific configurations:

```typescript
import { enhancedFirecrawlClient } from '@/lib/integrations/firecrawl/enhanced-client';

const result = await enhancedFirecrawlClient.scrapeUrl(url, {
  formats: ['html'],
  onlyMainContent: true,
  removeBase64Images: true,
  blockAds: true,
  excludeTags: siteConfig?.firecrawlOptions?.excludeTags || [],
});
```

### OpenAI Integration

Content enhancement and translation workflows:

```typescript
import { enhanceAndTranslateContent } from '@/lib/integrations/openai/client';

const result = await enhanceAndTranslateContent(
  germanContent,
  keyPoints,
  originalTitle
);
```

### HTML to Lexical Conversion

Use PayloadCMS native conversion utilities:

```typescript
import { htmlToLexical } from '@/lib/utils/html-to-lexical';

const lexicalContent = await htmlToLexical(htmlString);
```

## API Endpoints Structure

### Content Processing

- `POST /api/run-production-pipeline` - Main RSS processing pipeline
- `POST /api/run-single-pipeline` - Process single RSS item
- `POST /api/articles/enhance` - AI enhancement for articles
- `POST /api/articles/translate` - German translation service

### Testing & Development

- `POST /api/run-test-pipeline` - Test content processing
- `POST /api/test-firecrawl` - Test Firecrawl integration
- `POST /api/test-*` - Various testing endpoints

### Health & Monitoring

- `GET /api/health` - System health check
- `GET /api/openai-status` - OpenAI API monitoring
- `GET /api/firecrawl-alerts` - Firecrawl service monitoring

## Important Development Guidelines

### Never Work Against PayloadCMS

- Always use PayloadCMS native features before creating custom solutions
- Use PayloadCMS type generation for end-to-end type safety
- Follow PayloadCMS hook patterns and return value requirements
- Leverage PayloadCMS admin configuration for optimal UX

### Package Management

- **Always use package managers** - never manually edit package.json
- Use `pnpm` for dependency management
- Clear build cache with `rm -rf .next` after significant PayloadCMS changes

### Content Processing Best Practices

- Use domain-specific Firecrawl configurations stored in project guides
- Implement proper error handling for external service failures
- Always validate Lexical content structure before saving
- Use PayloadCMS hooks for workflow automation

### Performance Considerations

- Control database query depth with `depth` parameter
- Use field selection with `select` for efficient queries
- Implement proper indexing on frequently queried fields
- Cache external API responses appropriately

## Error Handling Patterns

### External Service Integration

```typescript
try {
  const result = await externalApiCall();
  return result;
} catch (error) {
  if (error instanceof ValidationError) {
    console.error('Validation failed:', error.data);
    return { error: 'Validation failed', details: error.data };
  }
  console.error('Unexpected error:', error);
  throw error;
}
```

### PayloadCMS Operations

```typescript
// Use proper PayloadCMS error handling
const article = await payload
  .findByID({
    collection: 'articles',
    id: articleId,
    depth: 1,
  })
  .catch(error => {
    console.error('Failed to find article:', error);
    return null;
  });
```

## File Structure Navigation

- `src/payload.config.ts` - PayloadCMS configuration
- `src/collections/` - Data models and schemas
- `src/fields/defaultLexical.ts` - Standard rich text configuration
- `src/lib/integrations/` - External service integrations
- `src/lib/server/` - Server-side business logic
- `src/lib/utils/` - Utility functions and helpers
- `src/app/api/` - Next.js API routes
- `src/components/admin/` - PayloadCMS admin components

## Testing Approach

### Test Structure

- Unit tests in `__tests__` directories alongside source files
- Integration tests for external service integrations
- API endpoint testing for content processing pipelines
- PayloadCMS collection and field testing

### Key Test Areas

- Content processing pipeline functionality
- Firecrawl integration with domain configurations
- OpenAI enhancement and translation workflows
- HTML to Lexical conversion accuracy
- PayloadCMS collection operations

## Development Workflow

1. **Schema Changes**: Always run `pnpm generate:types` after modifying collections
2. **External Services**: Test integration endpoints before production deployment
3. **Content Processing**: Use test endpoints to validate pipeline changes
4. **Rich Text**: Use PayloadCMS native Lexical patterns for consistency
5. **Error Recovery**: Implement comprehensive error handling for external dependencies

## Common Development Tasks

### Adding New Collection

1. Create collection config in `src/collections/`
2. Add to `src/payload.config.ts`
3. Run `pnpm generate:types`
4. Clear build cache: `rm -rf .next`

### Modifying Content Pipeline

1. Test changes with `/api/test-*` endpoints
2. Update error handling for external service failures
3. Validate Lexical content conversion
4. Test workflow state transitions

### Adding Rich Text Fields

1. Use `defaultLexical` configuration
2. Implement proper hooks for content validation
3. Use PayloadCMS native conversion utilities
4. Test content preservation across formats
